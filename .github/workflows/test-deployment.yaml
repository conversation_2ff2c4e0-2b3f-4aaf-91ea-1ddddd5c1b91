name: grazias-ionic-test

on:
  workflow_dispatch: # Start manually
    inputs:
      DEPLOYMENT_TARGET:
        type: choice
        description: 'Select deployment target for Ansible.'
        options:
          - 'test'
      RUNNER:
        type: choice
        description: 'Use self hosted or GitHub provided runner'
        options:
          - 'self-hosted'
          - 'github-provided'

env:
  FRONTEND_DIR: "${{ github.workspace }}"
  ANSIBLE_DIR: "${{ github.workspace }}/ansible"

jobs:
  frontend-build:
    runs-on: ${{ inputs.RUNNER == 'self-hosted' && 'cloud-1' || 'ubuntu-22.04' }}
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: install frontend dependencies
        run: "npm i"
        working-directory: ${{ env.FRONTEND_DIR }}
      - name: build frontend
        run: "npm run build-${{ inputs.DEPLOYMENT_TARGET }}"
        working-directory: ${{ env.FRONTEND_DIR }}
      - name: package frontend
        run: "tar -czf frontend.tar.gz www/"
        working-directory: ${{ env.FRONTEND_DIR }}
      - name: persist frontend
        uses: actions/upload-artifact@v4
        with:
          name: frontend
          path: "${{ env.FRONTEND_DIR }}/frontend.tar.gz"
          retention-days: 1

  deploy-frontend:
    needs: [ frontend-build ]
    runs-on: ${{ inputs.RUNNER == 'self-hosted' && 'cloud-1' || 'ubuntu-22.04' }}
    defaults:
      run:
        working-directory: ${{ env.ANSIBLE_DIR }}
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: setup ssh
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_GITHUB_CI }}
            ${{ secrets.SSH_GRAZIAS_TEST }}

      - name: get frontend
        uses: actions/download-artifact@v4
        with:
          name: frontend
          path: "${{ env.ANSIBLE_DIR }}"
          
      - name: update roles
        run: "ansible-galaxy install -f -r roles/requirements.yml"

      - name: deploy frontend
        run: "ansible-playbook -i inventory-${{ inputs.DEPLOYMENT_TARGET }} setup-and-launch-frontend.yaml -v"
