{"name": "grazias-ionic", "description": "GrazIAS Admin Frontend", "version": "4.0.0", "scripts": {"ng": "ng", "start": "npm run generate-version && npm run generate-surveys && ng serve", "build": "npm run generate-version && npm run generate-surveys && ng build", "build-production": "npm run generate-version && npm run generate-surveys && ng build --configuration=production", "build-staging": "npm run generate-version && npm run generate-surveys && ng build --configuration=staging", "build-test": "npm run generate-version && npm run generate-surveys && ng build --configuration=test", "generate-version": "export GIT_DESCRIBE=\"$(git describe) [$(git rev-parse --abbrev-ref HEAD)]\" && echo \"export const VERSION_NUMBER = '${GIT_DESCRIBE}';\" > src/version.ts", "generate-surveys": "node generate-surveys.js", "lint": "ng lint", "format:check": "prettier --check src", "format:write": "prettier --write src"}, "private": true, "dependencies": {"@angular/animations": "^17.3.10", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.10", "@angular/compiler": "^17.3.10", "@angular/core": "^17.3.10", "@angular/forms": "^17.3.10", "@angular/material": "^17.3.10", "@angular/material-moment-adapter": "^17.3.10", "@angular/platform-browser": "^17.3.10", "@angular/platform-browser-dynamic": "^17.3.10", "@angular/router": "^17.3.10", "@ionic/angular": "^8.2.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@sentry/angular": "^8.5.0", "@swimlane/ngx-datatable": "^20.1.0", "angularx-qrcode": "^17.0.1", "chart.js": "^2.9.3", "chartjs-chart-box-and-violin-plot": "^4.0.0", "chartjs-plugin-datalabels": "^0.7.0", "core-js": "^3.3.6", "d3": "^7.9.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.20", "luxon": "^3.4.4", "moment": "^2.30.1", "mustache": "^4.2.0", "rxjs": "^7.5.0", "survey-angular": "1.7.28", "tslib": "^2.6.2", "zone.js": "~0.14.6"}, "devDependencies": {"@angular-devkit/architect": "^0.1703.8", "@angular-devkit/build-angular": "^17.3.8", "@angular-eslint/builder": "17.5.2", "@angular-eslint/eslint-plugin": "17.5.2", "@angular-eslint/eslint-plugin-template": "17.5.2", "@angular-eslint/schematics": "17.5.2", "@angular-eslint/template-parser": "17.5.2", "@angular/cli": "^17.3.8", "@angular/compiler-cli": "^17.3.10", "@angular/language-service": "^17.3.10", "@ionic/angular-toolkit": "^11.0.1", "@ionic/cli": "^7.2.0", "@types/chart.js": "^2.9.41", "@types/lodash": "^4.14.161", "@types/luxon": "^3.4.2", "@types/node": "^18.19.33", "@typescript-eslint/eslint-plugin": "7.11.0", "@typescript-eslint/parser": "7.11.0", "eslint": "^8.57.0", "prettier": "^3.2.5", "typescript": "~5.4.5"}}