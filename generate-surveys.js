const fs = require('fs');
const mustache = require('mustache');

const surveyPath = 'src/assets/surveys';
const templatesPath = 'src/assets/survey-templates';


// TODO: use survey types enum
const templates = {
  "organization": "organization-survey-template.mustache",
  "administration": "administration-survey-template.mustache",
  "staff": "staff-survey-template.mustache",
  "parental": "parental-survey-template.mustache"
};

// TODO: unable to import language-utils
const defaultLanguage = "en";
const languages = {
  "de": require('./src/assets/i18n/de.json'),
  "en": require('./src/assets/i18n/en.json'),
  "pt": require('./src/assets/i18n/pt.json'),
  "fr": require('./src/assets/i18n/fr.json'),
  "es": require('./src/assets/i18n/es.json')
};

const view = {
  tr: () => {
    return (text, _) => {
      const translations = Object.keys(languages)
        .filter(locale => locale !== defaultLanguage)
        .map(locale => `"${locale}": "${resolveKeyForLocale(text, locale)}"`)
        .join(',\n');
      return `{
"default": "${resolveKeyForLocale(text, defaultLanguage)}",
${translations}
}`;
    };
  }
};

// apply localization for key and locale
function resolveKeyForLocale(key, locale) {
  let data = languages[locale];
  for (const k of key.split('.')) {
    if (!(k in data )) {
      return key;
    }
    data = data[k];
  }
  // escape double quotes
  data = data.replace(/"/g, '\\"');
    // remove trailing \n
  return data.replace(/\n*$/, "");
}

function writeSurvey(type, content) {
  fs.writeFileSync(surveyPath + '/' + type + '-survey.json', content);
}

// read template file and replace translations
function surveyDescriptionFromTemplatePath(type, template) {
  fs.readFile( __dirname + '/' + templatesPath + '/' + template, (err, data) => {
    if (err) {
      throw err;
    }
    const survey = mustache.render(data.toString(), view);
    writeSurvey(type, survey)
  });
}


// main
for (const type in templates) {
  surveyDescriptionFromTemplatePath(type, templates[type])
}
