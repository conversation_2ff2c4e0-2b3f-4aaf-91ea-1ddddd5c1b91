{"compileOnSave": false, "compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "target": "ES2022", "module": "ES2022", "moduleResolution": "node", "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "downlevelIteration": true, "typeRoots": ["node_modules/@types"], "lib": ["ES2022", "dom"], "skipLibCheck": true, "useDefineForClassFields": false}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}