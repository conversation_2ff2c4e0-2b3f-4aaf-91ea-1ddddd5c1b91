{
  "showQuestionNumbers": "off",
  "showProgressBar": "top",
  "widthMode": "responsive",
  "pages": [
    {
      "name": "surveys.pFP.001",
      "elements": [
        {
          "type": "html",
          "name": "surveys.pFP.001.0.title",
          "codebookKey": "pFP_001_0",
          "html": {{#tr}}surveys.pFP.001.0.title{{/tr}}
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.001.1.title",
          "codebookKey": "pFP_001_1",
          "title": {{#tr}}surveys.pFP.001.1.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_001_1_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_001_1_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.001.2.title",
          "codebookKey": "pFP_001_2",
          "title": {{#tr}}surveys.pFP.001.2.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_001_2_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_001_2_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.001.3.title",
          "codebookKey": "pFP_001_3",
          "title": {{#tr}}surveys.pFP.001.3.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_001_3_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_001_3_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.001.4.title",
          "codebookKey": "pFP_001_4",
          "title": {{#tr}}surveys.pFP.001.4.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_001_4_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_001_4_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.002",
      "elements": [
        {
          "type": "html",
          "name": "surveys.pFP.002.0.title",
          "codebookKey": "pFP_002_0",
          "html": {{#tr}}surveys.pFP.002.0.title{{/tr}}
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.002.1.title",
          "codebookKey": "pFP_002_1",
          "title": {{#tr}}surveys.pFP.002.1.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_002_1_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_002_1_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.002.2.title",
          "codebookKey": "pFP_002_2",
          "title": {{#tr}}surveys.pFP.002.2.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_002_2_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_002_2_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.002.3.title",
          "codebookKey": "pFP_002_3",
          "title": {{#tr}}surveys.pFP.002.3.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_002_3_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_002_3_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.002.4.title",
          "codebookKey": "pFP_002_4",
          "title": {{#tr}}surveys.pFP.002.4.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_002_4_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_002_4_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.003",
      "elements": [
        {
          "type": "html",
          "name": "surveys.pFP.003.0.title",
          "codebookKey": "pFP_003_0",
          "html": {{#tr}}surveys.pFP.003.0.title{{/tr}}
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.003.1.title",
          "codebookKey": "pFP_003_1",
          "title": {{#tr}}surveys.pFP.003.1.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_003_1_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_003_1_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.003.2.title",
          "codebookKey": "pFP_003_2",
          "title": {{#tr}}surveys.pFP.003.2.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_003_2_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_003_2_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.003.3.title",
          "codebookKey": "pFP_003_3",
          "title": {{#tr}}surveys.pFP.003.3.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_003_3_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_003_3_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        },
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.003.4.title",
          "codebookKey": "pFP_003_4",
          "title": {{#tr}}surveys.pFP.003.4.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_003_4_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_003_4_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.004",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.004.1.title",
          "codebookKey": "pFP_004_1",
          "title": {{#tr}}surveys.pFP.004.1.title{{/tr}},
          "choices": [
            {
            "value": "surveys.pFP.004.1.0",
            "codebookKey": "pFP_004_1_0",
            "text": {{#tr}}surveys.pFP.004.1.0{{/tr}}
            },
            {
            "value": "surveys.pFP.004.1.1",
            "codebookKey": "pFP_004_1_1",
            "text": {{#tr}}surveys.pFP.004.1.1{{/tr}}
            },
            {
            "value": "surveys.pFP.004.1.2",
            "codebookKey": "pFP_004_1_2",
            "text": {{#tr}}surveys.pFP.004.1.2{{/tr}}
            },
            {
            "value": "surveys.pFP.004.1.3",
            "codebookKey": "pFP_004_1_3",
            "text": {{#tr}}surveys.pFP.004.1.3{{/tr}}
            },
            {
            "value": "surveys.pFP.004.1.4",
            "codebookKey": "pFP_004_1_4",
            "text": {{#tr}}surveys.pFP.004.1.4{{/tr}}
            }
          ]
        },
        {
          "type": "comment",
          "isRequired": true,
          "name": "surveys.pFP.004.2.title",
          "codebookKey": "pFP_004_2",
          "title": {{#tr}}surveys.pFP.004.2.title{{/tr}}
        }
      ]
    },
    {
      "name": "surveys.pFP.005",
      "elements": [
        {
          "type": "checkbox",
          "isRequired": true,
          "name": "surveys.pFP.005.0.title",
          "codebookKey": "pFP_005_0",
          "title": {{#tr}}surveys.pFP.005.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.pFP.005.0.0",
            "codebookKey": "pFP_005_A",
            "text": {{#tr}}surveys.pFP.005.0.0{{/tr}}
            },
            {
            "value": "surveys.pFP.005.0.1",
            "codebookKey": "pFP_005_B",
            "text": {{#tr}}surveys.pFP.005.0.1{{/tr}}
            },
            {
            "value": "surveys.pFP.005.0.2",
            "codebookKey": "pFP_005_C",
            "text": {{#tr}}surveys.pFP.005.0.2{{/tr}}
            },
            {
            "value": "surveys.pFP.005.0.3",
            "codebookKey": "pFP_005_D",
            "text": {{#tr}}surveys.pFP.005.0.3{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.006",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.006.0.title",
          "codebookKey": "pFP_006",
          "title": {{#tr}}surveys.pFP.006.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_006_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_006_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.007",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.007.0.title",
          "codebookKey": "pFP_007",
          "title": {{#tr}}surveys.pFP.007.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_007_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_007_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.008",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.008.0.title",
          "codebookKey": "pFP_008",
          "title": {{#tr}}surveys.pFP.008.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_008_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_008_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.009",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.009.0.title",
          "codebookKey": "pFP_009",
          "title": {{#tr}}surveys.pFP.009.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_009_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_009_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.010",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.010.0.title",
          "codebookKey": "pFP_010",
          "title": {{#tr}}surveys.pFP.010.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_010_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_010_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.011",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.011.0.title",
          "codebookKey": "pFP_011",
          "title": {{#tr}}surveys.pFP.011.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_011_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_011_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.012",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.012.0.title",
          "codebookKey": "pFP_012",
          "title": {{#tr}}surveys.pFP.012.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_012_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_012_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.013",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.013.0.title",
          "codebookKey": "pFP_013",
          "title": {{#tr}}surveys.pFP.013.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_013_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_013_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.014",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.014.0.title",
          "codebookKey": "pFP_014",
          "title": {{#tr}}surveys.pFP.014.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_014_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_014_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    },
    {
      "name": "surveys.pFP.015",
      "elements": [
        {
          "type": "radiogroup",
          "isRequired": true,
          "name": "surveys.pFP.015.0.title",
          "codebookKey": "pFP_015",
          "title": {{#tr}}surveys.pFP.015.0.title{{/tr}},
          "choices": [
            {
            "value": "surveys.answers.x.x.yes",
            "codebookKey": "pFP_015_0_1",
            "text": {{#tr}}surveys.answers.x.x.yes{{/tr}}
            },
            {
            "value": "surveys.answers.x.x.no",
            "codebookKey": "pFP_015_0_0",
            "text": {{#tr}}surveys.answers.x.x.no{{/tr}}
            }
          ]
        }
      ]
    }
  ]
}
