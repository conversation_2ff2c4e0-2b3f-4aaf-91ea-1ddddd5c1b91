@import 'fonts.scss';
@import 'color.scss';

@media print {
  body {
    display: block;
  }

  * {
    --ion-color-primary: #9acbee;
    --ion-color-primary-rgb: 154, 203, 238;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-primary-contrast-rgb: 154, 203, 238;
    --ion-color-primary-shade: #9acbee;
    --ion-color-primary-tint: #9acbee;
    --ion-color-print-text: #000000;

    color: var(--ion-color-print-text);
    font-size: 8pt;
    line-height: 14pt;
    font-family: 'Open Sans';
    -webkit-font-smoothing: antialiased;

    print-color-adjust: exact !important;
  }

  .page-break {
    display: block;
    page-break-before: always;
  }

  .screen-container {
    max-width: 1000px;
    margin: auto;
  }

  .print-button {
    display: none;
  }

  .spacing-large {
    height: 30px;
  }

  .legend-container {
    display: flex;
  }

  .legend-entry {
    display: flex;
    align-items: center;
    padding-right: 30px;
  }

  .legend-box {
    width: 20px;
    height: 20px;
    margin: 4px 0px 4px 0px;
  }

  .legend-description {
    font-size: 9px;
    line-height: 11px;
    padding: 0px 0px 0px 10px;
    margin: 0px;
  }

  .bar-chart-entry {
    display: flex;
    align-items: center;
  }

  .bar-chart-column-container {
    width: 50%;
  }

  .bar-chart-description {
    font-size: 9px;
    line-height: 11px;
    padding: 0px 0px 0px 16px;
    margin: 0px;
  }

  .bar-chart-image-container {
    width: 100%;
  }

  .doughnut-chart-row {
    display: flex;
    height: 220px;
    width: 100%;
  }

  .doughnut-chart {
    width: 33%;
  }

  .doughnut-chart-container {
    position: relative;
  }

  .doughnut-chart-content {
    position: absolute;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .doughnut-chart-content-header {
    font-size: 9px;
    line-height: 11px;
    font-weight: bold;
  }

  .doughnut-chart-content-description {
    display: block;
    font-size: 9px;
    line-height: 11px;
  }
}
