/* ------------------------- evaluator-analyis page: */

.logo {
  height: 25vh;
  margin: 2rem auto 0 auto;
  content: url('/assets/img/logo.png');
}

ion-toolbar {
  height: 4rem;
  --background: white;
}

* {
  --ion-color-primary: #9acbee;
  --ion-color-primary-rgb: 154, 203, 238;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 154, 203, 238;
  --ion-color-primary-shade: #9acbee;
  --ion-color-primary-tint: #9acbee;

  font-family: 'Comfortaa';
  -webkit-font-smoothing: antialiased;
}

.master-data-container {
  padding-top: 18rem;
  text-align: center;
  page-break-after: always;

  div {
    margin-bottom: 2rem;
  }

  .scale {
    margin-top: 1rem;
    font-size: 1rem;
    font-weight: 300;
  }

  .institution {
    color: var(--ion-color-primary);
    font-size: 1.5rem;
  }

  .info {
    font-size: 1rem;
    font-weight: 300;
  }
}

/* ------------------------- trait-analyis page: */

.trait-title {
  display: block;
  font-size: 1.67rem;
  line-height: 1.67rem;
  font-weight: 900;
  font-family: 'Montserrat';
}

.trait-explanation {
  text-align: justify;
  margin-bottom: 1rem;
  font-size: 0.75rem;
  line-height: 80%;
}

.img-placeholder {
  height: 20rem;
  width: 20rem;
  margin: 2rem auto 0 auto;
  background: url('/assets/img/img-placeholder.png') top center / contain no-repeat;
}

/* background colored circle FOR BROWSER!!! */

.branch-title-wrapper {
  position: relative;
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 50px;
  background-color: var(--zero-three-trait-12);

  &.didactics-t1-title {
    background-color: var(--didactics-trait-1) !important;
  }
  &.didactics-t2-title {
    background-color: var(--didactics-trait-2) !important;
  }

  &.grazias-t1-title {
    background-color: var(--grazias-trait-1) !important;
  }
  &.grazias-t2-title {
    background-color: var(--grazias-trait-2) !important;
  }
  &.grazias-t3-title {
    background-color: var(--grazias-trait-3) !important;
  }
  &.grazias-t4-title {
    background-color: var(--grazias-trait-4) !important;
  }
  &.grazias-t5-title {
    background-color: var(--grazias-trait-5) !important;
  }
  &.grazias-t6-title {
    background-color: var(--grazias-trait-6) !important;
  }
  &.grazias-t7-title {
    background-color: var(--grazias-trait-7) !important;
  }
  &.grazias-t8-title {
    background-color: var(--grazias-trait-8) !important;
  }
  &.grazias-t9-title {
    background-color: var(--grazias-trait-9) !important;
  }
  &.grazias-t10-title {
    background-color: var(--grazias-trait-10) !important;
  }
  &.grazias-t10a-title {
    background-color: var(--grazias-trait-10) !important;
  }
  &.grazias-t10b-title {
    background-color: var(--grazias-trait-10) !important;
  }
  &.grazias-t10c-title {
    background-color: var(--grazias-trait-10) !important;
  }
  &.grazias-t11-title {
    background-color: var(--grazias-trait-11) !important;
  }
  &.grazias-t12-title {
    background-color: var(--grazias-trait-12) !important;
  }
  &.grazias-t13-title {
    background-color: var(--grazias-trait-13) !important;
  }
  &.grazias-t14-title {
    background-color: var(--grazias-trait-14) !important;
  }

  &.math-t1-title {
    background-color: var(--math-trait-1) !important;
  }
  &.math-t2-title {
    background-color: var(--math-trait-2) !important;
  }
  &.math-t3-title {
    background-color: var(--math-trait-3) !important;
  }
  &.math-t4-title {
    background-color: var(--math-trait-4) !important;
  }
  &.math-t5-title {
    background-color: var(--math-trait-5) !important;
  }
  &.math-t6-title {
    background-color: var(--math-trait-6) !important;
  }
  &.math-t7-title {
    background-color: var(--math-trait-7) !important;
  }
  &.math-t8-title {
    background-color: var(--math-trait-8) !important;
  }
  &.math-t9-title {
    background-color: var(--math-trait-9) !important;
  }
  &.math-t10-title {
    background-color: var(--math-trait-10) !important;
  }
  &.math-t11-title {
    background-color: var(--math-trait-11) !important;
  }
  &.math-t12-title {
    background-color: var(--math-trait-12) !important;
  }

  &.nature-t1-title {
    background-color: var(--nature-trait-1) !important;
  }
  &.nature-t2-title {
    background-color: var(--nature-trait-2) !important;
  }
  &.nature-t3-title {
    background-color: var(--nature-trait-3) !important;
  }
  &.nature-t4-title {
    background-color: var(--nature-trait-4) !important;
  }
  &.nature-t5-title {
    background-color: var(--nature-trait-5) !important;
  }
  &.nature-t6-title {
    background-color: var(--nature-trait-6) !important;
  }
  &.nature-t7-title {
    background-color: var(--nature-trait-7) !important;
  }
  &.nature-t8-title {
    background-color: var(--nature-trait-8) !important;
  }
  &.nature-t9-title {
    background-color: var(--nature-trait-9) !important;
  }
  &.nature-t10-title {
    background-color: var(--nature-trait-10) !important;
  }
  &.nature-t11-title {
    background-color: var(--nature-trait-11) !important;
  }
  &.nature-t12-title {
    background-color: var(--nature-trait-12) !important;
  }

  .branch-title {
    margin: 0.6rem 0 1rem 0.6rem;
    position: absolute;
    white-space: nowrap;
    font-family: 'Montserrat';
    font-size: 1rem;
    font-weight: 400;
  }
}

.branch-explanation {
  text-align: justify;
  margin-left: 3rem;
  margin-bottom: 1rem;
  display: inline-block;
  font-size: 0.75rem;
  line-height: 140%;
}

.assessment-wrapper {
  display: table;
  margin: 1rem 0 1rem 3rem;

  .branch-score {
    display: table-cell;
    height: 100%;
    width: 3rem;
    font-family: 'Montserrat';
    font-size: 1.67rem;
    font-weight: 900;
    text-align: center;
    vertical-align: middle;
  }

  /* BROWSER */

  .vertical-divider {
    height: 100%;
    width: 1px;
    border: 1px solid rgba($color: red, $alpha: 0.2);
    border-radius: 100px;
    display: table-cell;

    &.didactics-t1-divider {
      border: 1px solid var(--didactics-trait-1);
    }
    &.didactics-t2-divider {
      border: 1px solid var(--didactics-trait-2);
    }

    &.grazias-t1-divider {
      border: 1px solid var(--grazias-trait-1);
    }
    &.grazias-t2-divider {
      border: 1px solid var(--grazias-trait-2);
    }
    &.grazias-t3-divider {
      border: 1px solid var(--grazias-trait-3);
    }
    &.grazias-t4-divider {
      border: 1px solid var(--grazias-trait-4);
    }
    &.grazias-t5-divider {
      border: 1px solid var(--grazias-trait-5);
    }
    &.grazias-t6-divider {
      border: 1px solid var(--grazias-trait-6);
    }
    &.grazias-t7-divider {
      border: 1px solid var(--grazias-trait-7);
    }
    &.grazias-t8-divider {
      border: 1px solid var(--grazias-trait-8);
    }
    &.grazias-t9-divider {
      border: 1px solid var(--grazias-trait-9);
    }
    &.grazias-t10-divider {
      border: 1px solid var(--grazias-trait-10);
    }
    &.grazias-t10a-divider {
      border: 1px solid var(--grazias-trait-10);
    }
    &.grazias-t10b-divider {
      border: 1px solid var(--grazias-trait-10);
    }
    &.grazias-t10c-divider {
      border: 1px solid var(--grazias-trait-10);
    }
    &.grazias-t11-divider {
      border: 1px solid var(--grazias-trait-11);
    }
    &.grazias-t12-divider {
      border: 1px solid var(--grazias-trait-12);
    }
    &.grazias-t13-divider {
      border: 1px solid var(--grazias-trait-13);
    }
    &.grazias-t14-divider {
      border: 1px solid var(--grazias-trait-14);
    }

    &.math-t1-divider {
      border: 1px solid var(--math-trait-1);
    }
    &.math-t2-divider {
      border: 1px solid var(--math-trait-2);
    }
    &.math-t3-divider {
      border: 1px solid var(--math-trait-3);
    }
    &.math-t4-divider {
      border: 1px solid var(--math-trait-4);
    }
    &.math-t5-divider {
      border: 1px solid var(--math-trait-5);
    }
    &.math-t6-divider {
      border: 1px solid var(--math-trait-6);
    }
    &.math-t7-divider {
      border: 1px solid var(--math-trait-7);
    }
    &.math-t8-divider {
      border: 1px solid var(--math-trait-8);
    }
    &.math-t9-divider {
      border: 1px solid var(--math-trait-9);
    }
    &.math-t10-divider {
      border: 1px solid var(--math-trait-10);
    }
    &.math-t11-divider {
      border: 1px solid var(--math-trait-11);
    }
    &.math-t12-divider {
      border: 1px solid var(--math-trait-12);
    }

    &.nature-t1-divider {
      border: 1px solid var(--nature-trait-1);
    }
    &.nature-t2-divider {
      border: 1px solid var(--nature-trait-2);
    }
    &.nature-t3-divider {
      border: 1px solid var(--nature-trait-3);
    }
    &.nature-t4-divider {
      border: 1px solid var(--nature-trait-4);
    }
    &.nature-t5-divider {
      border: 1px solid var(--nature-trait-5);
    }
    &.nature-t6-divider {
      border: 1px solid var(--nature-trait-6);
    }
    &.nature-t7-divider {
      border: 1px solid var(--nature-trait-7);
    }
    &.nature-t8-divider {
      border: 1px solid var(--nature-trait-8);
    }
    &.nature-t9-divider {
      border: 1px solid var(--nature-trait-9);
    }
    &.nature-t10-divider {
      border: 1px solid var(--nature-trait-10);
    }
    &.nature-t11-divider {
      border: 1px solid var(--nature-trait-11);
    }
    &.nature-t12-divider {
      border: 1px solid var(--nature-trait-12);
    }
  }

  .assessment-text {
    text-align: justify;
    margin-bottom: 1rem;
    padding-left: 1rem;
    font-size: 0.75rem;
    font-family: 'Montserrat';
    font-style: italic;
    font-weight: 300;
    display: table-cell;
    vertical-align: middle;
  }
}

.note-wrapper {
  margin-top: 2rem;
  margin-left: 3rem;

  .note-title {
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 900;
    line-height: 0.75rem !important;
  }

  .note-text {
    margin-bottom: 1rem;
    text-align: justify;
    display: inline-block;
    font-size: 0.75rem;
    line-height: 0.75rem;
  }
}
