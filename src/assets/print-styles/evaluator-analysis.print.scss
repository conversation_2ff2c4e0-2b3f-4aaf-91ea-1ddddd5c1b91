@import 'fonts.scss';
@import 'color.scss';
@import 'evaluator-analysis.browser.scss';

@media print {
  body {
    display: block;
  }

  * {
    --ion-color-primary: #9acbee;
    --ion-color-primary-rgb: 154, 203, 238;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-primary-contrast-rgb: 154, 203, 238;
    --ion-color-primary-shade: #9acbee;
    --ion-color-primary-tint: #9acbee;

    font-family: 'Comfortaa';
    -webkit-font-smoothing: antialiased;

    print-color-adjust: exact !important;
  }

  .page-break {
    display: block;
    page-break-before: always;
  }

  .branch-container {
    page-break-inside: avoid;
  }

  .legend-box {
    width: 12px !important;
    height: 12px !important;
  }

  .legend-description {
    font-size: 12px !important;
  }

  ion-header {
    display: none !important;
  }

  header nav,
  footer {
    display: none;
  }

  .app-root,
  .split-pane,
  .ion-page,
  ion-app,
  ion-nav,
  ion-tab,
  ion-tabs {
    contain: none;
  }

  body,
  ion-nav {
    overflow: initial;
  }

  body {
    position: static;
  }

  [ion-content-print] {
    overflow-y: visible !important;
    position: relative;
  }

  /* ------------------------- evaluator-analysis print: */

  .logo {
    height: 25vh;
    margin: 2rem auto 0 auto;
    content: url('/assets/img/logo.png');
  }

  .scale {
    text-align: center;
    margin-top: 1rem;
    font-size: 0.8rem;
    font-weight: 300;
  }

  .institution {
    margin: 1rem auto 0 auto;
    text-align: center;
    max-width: 20rem;
    color: var(--ion-color-primary);
    font-size: 1rem;
  }

  .info {
    margin-top: 1rem;
    text-align: center;
    font-size: 0.8rem;
    font-weight: 300;
  }

  /* ------------------------- trait-analysis print: */

  .trait-title {
    margin-bottom: 1rem;
    display: inline-block;
    font-family: 'Montserrat' !important;
    font-size: 1.13rem;
    line-height: 1.13rem;
    font-weight: 900;
  }

  .trait-explanation {
    margin-bottom: 1rem;
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 400;
    text-align: justify;
    display: inline-block;
    line-height: 140% !important;
  }

  .bar-image-container {
    width: auto !important;
    height: 150px !important;
    margin: auto !important;
    margin-top: 20px !important;
    margin-bottom: 30px !important;
    text-align: center !important;
  }

  .bar-image-container-with-labels {
    margin: auto !important;
    margin-top: 20px !important;
    margin-bottom: 30px !important;
    text-align: center !important;
  }

  .bar-image {
    width: auto !important;
    height: 150px !important;
  }

  .bar-image-with-labels {
    width: auto !important;
    max-width: 20cm;
    height: auto !important;
    max-height: 200px;
  }

  .bar-chart-container,
  .bar-chart-container-with-labels {
    display: none !important;
  }

  .branch-title-wrapper {
    position: relative;
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100px;
    background-color: var(--grazias-trait-1);
  }

  /* PRINT */

  .didactics-t1-title {
    background-color: var(--didactics-trait-1) !important;
  }
  .didactics-t2-title {
    background-color: var(--didactics-trait-2) !important;
  }

  .grazias-t1-title {
    background-color: var(--grazias-trait-1) !important;
  }
  .grazias-t2-title {
    background-color: var(--grazias-trait-2) !important;
  }
  .grazias-t3-title {
    background-color: var(--grazias-trait-3) !important;
  }
  .grazias-t4-title {
    background-color: var(--grazias-trait-4) !important;
  }
  .grazias-t5-title {
    background-color: var(--grazias-trait-5) !important;
  }
  .grazias-t6-title {
    background-color: var(--grazias-trait-6) !important;
  }
  .grazias-t7-title {
    background-color: var(--grazias-trait-7) !important;
  }
  .grazias-t8-title {
    background-color: var(--grazias-trait-8) !important;
  }
  .grazias-t9-title {
    background-color: var(--grazias-trait-9) !important;
  }
  .grazias-t10-title {
    background-color: var(--grazias-trait-10) !important;
  }
  .grazias-t10a-title {
    background-color: var(--grazias-trait-10) !important;
  }
  .grazias-t10b-title {
    background-color: var(--grazias-trait-10) !important;
  }
  .grazias-t10c-title {
    background-color: var(--grazias-trait-10) !important;
  }
  .grazias-t11-title {
    background-color: var(--grazias-trait-11) !important;
  }
  .grazias-t12-title {
    background-color: var(--grazias-trait-12) !important;
  }
  .grazias-t13-title {
    background-color: var(--grazias-trait-13) !important;
  }
  .grazias-t14-title {
    background-color: var(--grazias-trait-14) !important;
  }

  .math-t1-title {
    background-color: var(--math-trait-1) !important;
  }
  .math-t2-title {
    background-color: var(--math-trait-2) !important;
  }
  .math-t3-title {
    background-color: var(--math-trait-3) !important;
  }
  .math-t4-title {
    background-color: var(--math-trait-4) !important;
  }
  .math-t5-title {
    background-color: var(--math-trait-5) !important;
  }
  .math-t6-title {
    background-color: var(--math-trait-6) !important;
  }
  .math-t7-title {
    background-color: var(--math-trait-7) !important;
  }
  .math-t8-title {
    background-color: var(--math-trait-8) !important;
  }
  .math-t9-title {
    background-color: var(--math-trait-9) !important;
  }
  .math-t10-title {
    background-color: var(--math-trait-10) !important;
  }
  .math-t11-title {
    background-color: var(--math-trait-11) !important;
  }
  .math-t12-title {
    background-color: var(--math-trait-12) !important;
  }

  .nature-t1-title {
    background-color: var(--nature-trait-1) !important;
  }
  .nature-t2-title {
    background-color: var(--nature-trait-2) !important;
  }
  .nature-t3-title {
    background-color: var(--nature-trait-3) !important;
  }
  .nature-t4-title {
    background-color: var(--nature-trait-4) !important;
  }
  .nature-t5-title {
    background-color: var(--nature-trait-5) !important;
  }
  .nature-t6-title {
    background-color: var(--nature-trait-6) !important;
  }
  .nature-t7-title {
    background-color: var(--nature-trait-7) !important;
  }
  .nature-t8-title {
    background-color: var(--nature-trait-8) !important;
  }
  .nature-t9-title {
    background-color: var(--nature-trait-9) !important;
  }
  .nature-t10-title {
    background-color: var(--nature-trait-10) !important;
  }
  .nature-t11-title {
    background-color: var(--nature-trait-11) !important;
  }
  .nature-t12-title {
    background-color: var(--nature-trait-12) !important;
  }

  .branch-title {
    margin: 0.65rem 0 1rem 0.6rem;
    position: absolute;
    white-space: nowrap;
    font-family: 'Montserrat';
    font-size: 1rem;
    font-weight: 400;
  }

  .branch-explanation {
    text-align: justify;
    margin-left: 3rem;
    margin-bottom: 1rem;
    display: inline-block;
    font-size: 0.75rem;
    line-height: 140%;
  }

  .assessment-wrapper {
    display: table;
    margin: 1rem 0 1rem 3rem;
  }

  .branch-score {
    display: table-cell;
    height: 100%;
    width: 3rem;
    font-family: 'Montserrat';
    font-size: 1.67rem;
    font-weight: 900;
    text-align: center;
    vertical-align: middle;
  }

  /* PRINT */

  .vertical-divider {
    height: 100%;
    width: 1px;
    border: 1px solid var(--grazias-trait-1);
    background-color: var(--grazias-trait-1);
    border-radius: 50px;
    display: table-cell;
  }
  .didactics-t1-divider {
    border: 1px solid var(--didactics-trait-1);
    background-color: var(--didactics-trait-1);
  }
  .didactics-t2-divider {
    border: 1px solid var(--didactics-trait-2);
    background-color: var(--didactics-trait-2);
  }

  .grazias-t1-divider {
    border: 1px solid var(--grazias-trait-1);
    background-color: var(--grazias-trait-1);
  }
  .grazias-t2-divider {
    border: 1px solid var(--grazias-trait-2);
    background-color: var(--grazias-trait-2);
  }
  .grazias-t3-divider {
    border: 1px solid var(--grazias-trait-3);
    background-color: var(--grazias-trait-3);
  }
  .grazias-t4-divider {
    border: 1px solid var(--grazias-trait-4);
    background-color: var(--grazias-trait-4);
  }
  .grazias-t5-divider {
    border: 1px solid var(--grazias-trait-5);
    background-color: var(--grazias-trait-5);
  }
  .grazias-t6-divider {
    border: 1px solid var(--grazias-trait-6);
    background-color: var(--grazias-trait-6);
  }
  .grazias-t7-divider {
    border: 1px solid var(--grazias-trait-7);
    background-color: var(--grazias-trait-7);
  }
  .grazias-t8-divider {
    border: 1px solid var(--grazias-trait-8);
    background-color: var(--grazias-trait-8);
  }
  .grazias-t9-divider {
    border: 1px solid var(--grazias-trait-9);
    background-color: var(--grazias-trait-9);
  }
  .grazias-t10-divider {
    border: 1px solid var(--grazias-trait-10);
    background-color: var(--grazias-trait-10);
  }
  .grazias-t10a-divider {
    border: 1px solid var(--grazias-trait-10);
    background-color: var(--grazias-trait-10);
  }
  .grazias-t10b-divider {
    border: 1px solid var(--grazias-trait-10);
    background-color: var(--grazias-trait-10);
  }
  .grazias-t10c-divider {
    border: 1px solid var(--grazias-trait-10);
    background-color: var(--grazias-trait-10);
  }
  .grazias-t11-divider {
    border: 1px solid var(--grazias-trait-11);
    background-color: var(--grazias-trait-11);
  }
  .grazias-t12-divider {
    border: 1px solid var(--grazias-trait-12);
    background-color: var(--grazias-trait-12);
  }
  .grazias-t13-divider {
    border: 1px solid var(--grazias-trait-13);
    background-color: var(--grazias-trait-13);
  }
  .grazias-t14-divider {
    border: 1px solid var(--grazias-trait-14);
    background-color: var(--grazias-trait-14);
  }

  .math-t1-divider {
    border: 1px solid var(--math-trait-1);
    background-color: var(--math-trait-1);
  }
  .math-t2-divider {
    border: 1px solid var(--math-trait-2);
    background-color: var(--math-trait-2);
  }
  .math-t3-divider {
    border: 1px solid var(--math-trait-3);
    background-color: var(--math-trait-3);
  }
  .math-t4-divider {
    border: 1px solid var(--math-trait-4);
    background-color: var(--math-trait-4);
  }
  .math-t5-divider {
    border: 1px solid var(--math-trait-5);
    background-color: var(--math-trait-5);
  }
  .math-t6-divider {
    border: 1px solid var(--math-trait-6);
    background-color: var(--math-trait-6);
  }
  .math-t7-divider {
    border: 1px solid var(--math-trait-7);
    background-color: var(--math-trait-7);
  }
  .math-t8-divider {
    border: 1px solid var(--math-trait-8);
    background-color: var(--math-trait-8);
  }
  .math-t9-divider {
    border: 1px solid var(--math-trait-9);
    background-color: var(--math-trait-9);
  }
  .math-t10-divider {
    border: 1px solid var(--math-trait-10);
    background-color: var(--math-trait-10);
  }
  .math-t11-divider {
    border: 1px solid var(--math-trait-11);
    background-color: var(--math-trait-11);
  }
  .math-t12-divider {
    border: 1px solid var(--math-trait-12);
    background-color: var(--math-trait-12);
  }

  .nature-t1-divider {
    border: 1px solid var(--nature-trait-1);
    background-color: var(--nature-trait-1);
  }
  .nature-t2-divider {
    border: 1px solid var(--nature-trait-2);
    background-color: var(--nature-trait-2);
  }
  .nature-t3-divider {
    border: 1px solid var(--nature-trait-3);
    background-color: var(--nature-trait-3);
  }
  .nature-t4-divider {
    border: 1px solid var(--nature-trait-4);
    background-color: var(--nature-trait-4);
  }
  .nature-t5-divider {
    border: 1px solid var(--nature-trait-5);
    background-color: var(--nature-trait-5);
  }
  .nature-t6-divider {
    border: 1px solid var(--nature-trait-6);
    background-color: var(--nature-trait-6);
  }

  .nature-t7-divider {
    border: 1px solid var(--nature-trait-7);
    background-color: var(--nature-trait-7);
  }
  .nature-t8-divider {
    border: 1px solid var(--nature-trait-8);
    background-color: var(--nature-trait-8);
  }
  .nature-t9-divider {
    border: 1px solid var(--nature-trait-9);
    background-color: var(--nature-trait-9);
  }
  .nature-t10-divider {
    border: 1px solid var(--nature-trait-10);
    background-color: var(--nature-trait-10);
  }
  .nature-t11-divider {
    border: 1px solid var(--nature-trait-11);
    background-color: var(--nature-trait-11);
  }
  .nature-t12-divider {
    border: 1px solid var(--nature-trait-12);
    background-color: var(--nature-trait-12);
  }

  .assessment-text {
    display: table-cell;
    margin-bottom: 1rem;
    padding-left: 1rem;
    font-size: 0.75rem;
    font-family: 'Montserrat';
    font-style: italic;
    font-weight: 300;
    text-align: justify;
    overflow: auto !important;
    vertical-align: middle;
  }

  .note-title {
    margin-top: 2rem;
    font-size: 0.75rem;
    font-weight: 900;
    line-height: 1rem;
  }

  .note-text {
    text-align: justify;
    margin-bottom: 1rem;
    display: inline-block;
    font-size: 0.75rem;
    line-height: 0.75rem;
  }

  .bubble-chart-container {
    padding-top: 4rem;
    font-family: 'Comfortaa';
  }

  .bubble-chart-title {
    padding-top: 8rem;
    color: var(--ion-color-primary);
    font-size: 1.5rem;
    text-align: center;
  }
}
