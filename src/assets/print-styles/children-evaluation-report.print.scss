@import 'fonts.scss';
@import 'color.scss';

@media print {
  * {
    color: var(--ion-color-print-text);
    font-size: 8pt;
    line-height: 14pt;
    font-family: 'Open Sans';
    -webkit-font-smoothing: antialiased;

    print-color-adjust: exact !important;
  }

  h1 {
    font-size: 12pt;
    font-weight: bold;
    line-height: 18pt;
    padding-bottom: 8px;
  }

  h2 {
    font-size: 10pt;
    font-weight: bold;
    line-height: 16pt;
    padding-top: 40px;
  }

  h3 {
    font-size: 8pt;
    font-weight: bold;
    line-height: 14pt;
    padding-top: 40px;
  }

  .page-break {
    display: block;
    page-break-before: always;
  }

  .print-button {
    display: none;
  }

  .logo-container {
    display: flex;
    justify-content: center;
    padding-top: 264px;
    padding-bottom: 280px;
  }

  .front-page-logo {
    width: 180px;
    height: 200px;
  }

  .info-text {
    display: flex;
    flex-direction: column;
    padding-left: 32px;
    font-size: 10px;
  }

  .title {
    font-size: 24pt;
    padding-bottom: 8px;
    font-weight: bolder;
  }

  .subtitle {
    font-size: 10pt;
    font-weight: bold;
    line-height: 16pt;
    padding-bottom: 40px;
  }

  .row {
    display: flex;
    padding-top: 16px;
  }

  .row-spacer {
    height: 20px;
  }

  .column {
    display: flex;
    flex-direction: column;
    width: 47.5vw;
  }

  .column-spacer {
    display: flex;
    flex-direction: column;
    width: 5vw;
  }

  /*----------------------------------------------------------------------*/

  .question-title {
    margin-bottom: 4px;
  }

  .underline-q1 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q1);
    background-color: var(--child-evaluation-report-color-q1);
    margin-bottom: 8px;
  }

  .underline-q2 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q2);
    background-color: var(--child-evaluation-report-color-q2);
    margin-bottom: 8px;
  }

  .underline-q3 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q3);
    background-color: var(--child-evaluation-report-color-q3);
    margin-bottom: 8px;
  }

  .underline-q4 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q4);
    background-color: var(--child-evaluation-report-color-q4);
    margin-bottom: 8px;
  }

  .underline-q5 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q5);
    background-color: var(--child-evaluation-report-color-q5);
    margin-bottom: 8px;
  }

  .underline-q6 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q6);
    background-color: var(--child-evaluation-report-color-q6);
    margin-bottom: 8px;
  }

  .underline-q7 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q7);
    background-color: var(--child-evaluation-report-color-q7);
    margin-bottom: 8px;
  }

  .underline-q8 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q8);
    background-color: var(--child-evaluation-report-color-q8);
    margin-bottom: 8px;
  }

  .underline-q9 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q9);
    background-color: var(--child-evaluation-report-color-q9);
    margin-bottom: 8px;
  }

  .underline-q10 {
    height: 1px;
    width: 80px;
    border-radius: 5px;
    border: 1px solid var(--child-evaluation-report-color-q10);
    background-color: var(--child-evaluation-report-color-q10);
    margin-bottom: 8px;
  }

  .child-eval-bar-chart-image-container {
    height: 300px;
    width: auto;
    margin: 3rem auto 5rem;
    text-align: center;
  }

  .child-eval-bar-image-with-labels {
    height: 300px;
    width: auto;
  }

  .child-eval-bar-chart-container {
    height: 30rem;
    margin: 0 auto auto;
  }

  .free-form-answer-list-item {
    margin-left: 10px;
    margin-bottom: 5px;

    &.no-answer-given {
      color: var(--child-evaluation-report-color-disabled);
    }
  }

  /* children-evaluation-bar-chart-explanation.component
  ---------------------------------------------------------------------- */

  .explanation-title {
    font-size: 12px;
    font-weight: bold;
    line-height: 18px;
    padding-bottom: 8px;
  }

  .bar-chart-eplanation-share {
    margin-left: 15px;
    color: var(--child-evaluation-info-text-color);
    padding-bottom: 8px;
  }

  .bubble-chart-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .bubble-chart-page-padding {
    height: 160px;
  }

  tspan {
    font-size: 6pt !important;
  }
}
