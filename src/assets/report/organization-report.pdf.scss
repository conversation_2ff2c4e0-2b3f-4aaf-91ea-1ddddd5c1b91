@import '../print-styles/color.scss';
@import '../print-styles/children-evaluation-report.print.scss';
@import '../print-styles/evaluator-analysis.print.scss';

@page {
  size: A4;
  margin-top: 4vw;
  margin-bottom: 4vw;
}

html {
  print-color-adjust: exact !important;
}

* {
  font-family: 'Nunito Sans';
  white-space: pre-wrap;
}

ion-col,
ion-grid {
  padding-left: 0px;
  padding-right: 0px;
}

body * {
  font-size: 8pt;
  line-height: 14pt;
}

h1 {
  font-weight: bold;
  font-size: 1.13rem;
  line-height: 1.5rem;
  margin-bottom: 2rem;
  margin-top: 1.33rem;
}

h2 {
  font-weight: bold;
  font-size: 0.8rem;
  line-height: 1, 2rem;
  margin-bottom: 1.33rem;
  margin-top: 1.33rem;
}

h3 {
  font-weight: bold;
  font-size: 0.66rem;
  line-height: 0.9rem;
  margin-top: 1.33rem;
}

.front-page-list-entry-icon {
  width: 10px;
  height: 10px;
}

.front-page-list-entry {
  padding-left: 0.66rem;
  display: flex;
  align-items: center;
}

.content-container {
  padding: 3.3rem;
}

.front-page-evaluation-information-container {
  margin-top: 2rem;
  margin-bottom: 2.64rem;
  font-size: 0.66rem;
}

.organization-name {
  font-weight: bold;
  padding-bottom: 0.5rem;
}

.heading-underline {
  display: flex;
  width: 4.62rem;
  background-color: rgb(151, 191, 13, 1);
  print-color-adjust: exact !important;
  padding: 0.2rem;
}

.title,
.sub-title {
  font-size: 4vw;
  line-height: 4vw;
  padding-bottom: 1vw;
}
.title {
  font-weight: bold;
}
.sub-title {
  font-weight: lighter;
}

.segment {
  padding-bottom: 4vw;
  display: inline-block;
  width: 100%;
}

.chart_placeholder {
  background-color: green;
  height: 3.3rem;
  width: 100%;
}

.page-break {
  display: block;
  page-break-before: always;
}

.boxplot-legend {
  margin-bottom: 0.2rem;
}

.boxplot-legend-key {
  display: inline-block;
  width: 2.3rem;
}

.illustration-11-comparison-break {
  display: block;
  page-break-before: always;
}

.screen-container {
  max-width: 1000px;
  margin: auto;
  padding-left: 6vw;
  padding-right: 6vw;
}

.print-button {
  padding-top: 16px;
  width: 200px;
  margin: auto;
}

.spacing-small {
  height: 10px;
}

.spacing-medium {
  height: 20px;
}

.spacing-large {
  height: 40px;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
}

.legend-entry {
  display: flex;
  align-items: center;
  padding-right: 30px;
}

.legend-box {
  width: 15px;
  height: 15px;
  margin: 4px 0px 4px 0px;
}

.legend-description {
  font-size: 14px;
  padding: 0px 0px 0px 10px;
  margin: 0px;
}

.single-bar-chart-container {
  height: 40px;
}

.bar-chart-entry {
  display: flex;
  align-items: center;
}

.bar-chart-column-container {
  width: 50%;
}

.bar-chart-description {
  font-size: 12px;
  padding: 0px 0px 0px 16px;
  margin: 0px;
}

table {
  width: 100%;
  font-weight: 400;
  border: 1px solid lightgrey;
  border-collapse: collapse;
}

table tr th:first-child {
  border: 1px solid lightgrey;
}

table tr th {
  height: 2rem;
  font-weight: 900;
}

table tr td:first-child {
  border: 1px solid lightgrey;
}

.bordered-cell {
  border: 1px solid lightgrey;
}

table tr td {
  height: 2rem;
  text-align: center;
  vertical-align: middle;
  border-top: 1px solid lightgrey;
}

table tr td img {
  height: 0.66rem;
}

.td-left-align {
  text-align: left !important;
  width: 85%;
  padding-left: 1vw;
}

.table-width-third {
  width: 33.33%;
}

.table-width-half {
  width: 50%;
}

.cell-align-top {
  vertical-align: top;
}

.cell-bold {
  font-weight: bold;
}

.table-4-first-column {
  width: 85%;
  text-align: left;
  padding-left: 1vw;
}

.table-4-second-column {
  width: 15%;
}

.th-sub-title {
  font-weight: normal;
}

.table-cell-padding {
  padding-left: 0.5vw !important;
  padding-right: 0.5vw !important;
}

.table-margin-bottom {
  margin-bottom: 2vw !important;
}

.font-normal {
  font-weight: normal !important;
}

.td-increased-width {
  width: 15%;
}

.checkbox-image {
  height: 10px;
}

.bar-chart-image-container {
  width: 100%;
}

.boxplot-chart-image-container {
  width: 100%;
}

.doughnut-chart-row {
  display: flex;
  height: 300px;
  width: 100%;
}

.doughnut-chart {
  width: 33%;
}

.doughnut-chart-container {
  position: relative;
}

.doughnut-chart-content {
  position: absolute;
  text-align: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -70%);
}

.doughnut-chart-content-header {
  font-size: 12px;
  line-height: 12px;
  font-weight: bold;
}

.doughnut-chart-content-description {
  display: block;
  font-size: 10px;
  line-height: 10px;
  padding-top: 4px;
}

.example-illustration {
  display: block;
  margin-left: auto;
  margin-right: auto;
  margin-top: 0.66rem;
  width: 70%;
}
