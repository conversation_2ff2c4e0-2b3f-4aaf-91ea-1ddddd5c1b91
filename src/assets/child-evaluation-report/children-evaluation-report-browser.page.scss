@import '../print-styles/color.scss';
// shared style-sheet for browser-view
// this is a shared style-sheet used for several components of child-evaluator-report context
//----------------------------------------------------------------------

* {
  font-size: 8pt;
  line-height: 14pt;
  font-family: 'Open Sans';
}

h1 {
  font-size: 12pt;
  font-weight: bold;
  line-height: 18pt;
  padding-bottom: 8px;
}

h2 {
  font-size: 10pt;
  font-weight: bold;
  line-height: 16pt;
  padding-top: 40px;
}

h3 {
  font-size: 8pt;
  font-weight: bold;
  line-height: 14pt;
  padding-top: 40px;
}

.page-break {
  display: block;
  page-break-before: always;
  padding-top: 500px;
}

.screen-container {
  max-width: 1000px;
  margin: auto;
}

// Front page
//----------------------------------------------------------------------
.print-button {
  padding-top: 16px;
  width: 200px;
  margin: auto;
}

.evaluation-data {
  page-break-after: always;
}

.logo-container {
  display: flex;
  justify-content: center;
  padding-top: 480px;
  padding-bottom: 400px;
}

.image {
  width: 180px;
  height: 200px;
}

.info-text {
  display: flex;
  flex-direction: column;
  padding-left: 32px;
  font-size: 10pt;
}

.title {
  font-size: 24pt;
  padding-bottom: 8px;
  font-weight: bolder;
}

.subtitle {
  font-size: 10pt;
  font-weight: bold;
  line-height: 16pt;
  padding-bottom: 40px;
}

//----------------------------------------------------------------------

.page-padding {
  height: 0;
}

.row {
  display: flex;
  padding-top: 16px;
}

.row-spacer {
  height: 20px;
}

.column {
  display: flex;
  flex-direction: column;
  width: 47.5vw;
}

.column-spacer {
  display: flex;
  flex-direction: column;
  width: 5vw;
}

.question-title {
  margin-bottom: 4px;
}

.underline-q1 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q1);
  background-color: var(--child-evaluation-report-color-q1);
}

.underline-q2 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q2);
  background-color: var(--child-evaluation-report-color-q2);
}

.underline-q3 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q3);
  background-color: var(--child-evaluation-report-color-q3);
}

.underline-q4 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q4);
  background-color: var(--child-evaluation-report-color-q4);
}

.underline-q5 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q5);
  background-color: var(--child-evaluation-report-color-q5);
}

.underline-q6 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q6);
  background-color: var(--child-evaluation-report-color-q6);
}

.underline-q7 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q7);
  background-color: var(--child-evaluation-report-color-q7);
}

.underline-q8 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q8);
  background-color: var(--child-evaluation-report-color-q8);
}

.underline-q9 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q9);
  background-color: var(--child-evaluation-report-color-q9);
}

.underline-q10 {
  height: 5px;
  width: 70px;
  border-radius: 5px;
  border: 1px solid var(--child-evaluation-report-color-q10);
  background-color: var(--child-evaluation-report-color-q10);
}

.report-section {
  margin-bottom: 250px;
}

// children-evaluation-bar-chart.component
//----------------------------------------------------------------------
.child-eval-bar-chart-image-container {
  height: 250px;
  width: auto;
  margin: 3rem auto 1rem;
  text-align: center;

  img {
    max-height: 100%;
    width: auto;
  }
}

.child-eval-bar-chart-container {
  height: 30rem;
  margin: 0 auto auto;
}

// children-evaluation-free-form-answers.component
//----------------------------------------------------------------------
.free-form-answer-list-item {
  margin-top: 10px;
  margin-left: 10px;
  margin-bottom: 5px;

  &.no-answer-given {
    color: var(--child-evaluation-report-color-disabled);
  }
}

// children-evaluation-bar-chart-explanation.component
//----------------------------------------------------------------------
.explanation-title {
  font-size: 12px;
  font-weight: bold;
  line-height: 18px;
  padding-bottom: 8px;
}

.bar-chart-explanation-share {
  margin-left: 15px;
  color: var(--child-evaluation-info-text-color);
}

// children-evaluation-bubble-chart.component
//----------------------------------------------------------------------
.bubble-chart-container {
  margin-top: 280px;
  margin-bottom: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

tspan {
  font-size: 8pt !important;
}
