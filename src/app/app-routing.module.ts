import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { adminGuard } from './guards/admin.guard';
import { publicGuard } from './guards/public.guard';
import { AdminNavBarComponent } from './modules/organization/admin-nav-bar/admin-nav-bar.component';
import { SurveyFormComponent } from './modules/organization/survey-form/survey-form.component';
import { LoginPage } from './modules/shared/pages/login/login.page';
import { SurveyPage } from './modules/survey/pages/survey/survey.page';
import { TotpPage } from './modules/shared/pages/totp/totp.page';
import { totpGuard } from './guards/totp.guard';

const routes: Routes = [
  { path: '', redirectTo: 'login', pathMatch: 'full' },
  {
    path: 'login',
    canActivate: [publicGuard],
    component: LoginPage,
  },
  {
    path: 'totp',
    canActivate: [publicGuard, totpGuard],
    component: TotpPage,
  },
  {
    path: 'surveys/:surveyToken',
    component: SurveyPage,
  },
  {
    path: 'privacy-policy/:source',
    loadChildren: () => import('./pages/legal/terms/terms.module').then((m) => m.TermsPageModule),
  },
  {
    path: 'imprint',
    loadChildren: () => import('./pages/imprint/imprint.module').then((m) => m.ImprintPageModule),
  },
  {
    path: 'evaluator-analysis/:accessId/:evaluationRemoteId/:lang',
    loadChildren: () =>
      import('./pages/evaluator-analysis/evaluator-analysis.module').then(
        (m) => m.EvaluatorAnalysisPageModule,
      ),
  },
  {
    path: 'educator-analysis/:accessId/:evaluationRemoteId/:lang',
    loadChildren: () =>
      import('./pages/educator-analysis/educator-analysis.module').then(
        (m) => m.EducatorAnalysisPageModule,
      ),
  },
  {
    // These routes can be activated by admins
    path: 'admin',
    component: AdminNavBarComponent,
    canActivate: [adminGuard],
    children: [
      {
        path: 'create-user-form',
        loadChildren: () =>
          import('./pages/create-user-form/create-user-form.module').then(
            (m) => m.CreateUserFormPageModule,
          ),
      },
      {
        path: 'educator-search',
        loadChildren: () =>
          import('./pages/admin/educator-search/educator-search.module').then(
            (m) => m.EducatorSearchPageModule,
          ),
      },
      {
        path: 'dashboard',
        loadChildren: () =>
          import('./pages/admin/dashboard/dashboard.module').then((m) => m.DashboardPageModule),
      },
      {
        path: 'organizations',
        loadChildren: () =>
          import('./modules/organization/organizations/organizations.module').then(
            (m) => m.OrganizationsPageModule,
          ),
      },
      {
        path: 'organizations/upsert-organization-form/:orgId',
        loadChildren: () =>
          import(
            './modules/organization/upsert-organization-form/upsert-organization-form.module'
          ).then((m) => m.UpsertOrganizationFormPageModule),
      },
      {
        path: 'organizations/upsert-location-form/:orgId/:locationId',
        loadChildren: () =>
          import('./modules/organization/upsert-location-form/upsert-location-form.module').then(
            (m) => m.UpsertLocationFormPageModule,
          ),
      },
      {
        path: 'organizations/upsert-group-form/:orgId/:locationId/:groupId',
        loadChildren: () =>
          import('./modules/organization/upsert-group-form/upsert-group-form.module').then(
            (m) => m.UpsertGroupFormPageModule,
          ),
      },
      {
        path: 'organizations/survey-form/:surveyType/:orgId/:locationId/:groupId/:surveyId',
        component: SurveyFormComponent,
      },
      {
        path: 'organizations/evaluation-detail/:evaluationType/:evalId',
        loadChildren: () =>
          import('./modules/organization/evaluation-detail/evaluation-detail.module').then(
            (m) => m.EvaluationDetailPageModule,
          ),
      },
      {
        path: 'licenses',
        loadChildren: () =>
          import('./pages/admin/licenses/licenses.module').then((m) => m.LicensesPageModule),
      },
      {
        path: 'license-detail',
        loadChildren: () =>
          import('./pages/admin/license-detail/license-detail.module').then(
            (m) => m.LicenseDetailPageModule,
          ),
      },
      {
        path: 'projects',
        loadChildren: () =>
          import('./pages/admin/projects/projects.module').then((m) => m.ProjectsPageModule),
      },
      {
        path: 'project-detail',
        loadChildren: () =>
          import('./pages/admin/project-detail/project-detail.module').then(
            (m) => m.ProjectDetailPageModule,
          ),
      },
      {
        path: 'spss-filter',
        loadChildren: () =>
          import('./pages/admin/spss-filter/spss-filter.module').then((m) => m.SpssFilterModule),
      },
      {
        path: 'spss-filter-detail',
        loadChildren: () =>
          import('./pages/admin/spss-filter-detail/spss-filter-detail.module').then(
            (m) => m.SpssFilterDetailModule,
          ),
      },
      {
        path: 'legal-text-overview',
        loadChildren: () =>
          import('./pages/admin/legal/legal-text-overview/legal-text-overview.module').then(
            (m) => m.LegalTextOverviewModule,
          ),
      },
      {
        path: 'legal-text-version-list/:legalTextType',
        loadChildren: () =>
          import('./pages/admin/legal/legal-text-version-list/legal-text-version-list.module').then(
            (m) => m.LegalTextVersionListPageModule,
          ),
      },
      {
        path: 'legal-text-detail/:legalTextType/:versionName/:versionId',
        loadChildren: () =>
          import('./pages/admin/legal/legal-text-detail/legal-text-detail.module').then(
            (m) => m.LegalTextDetailPageModule,
          ),
      },
      {
        path: 'legal-text-detail/:legalTextType/:versionName',
        loadChildren: () =>
          import('./pages/admin/legal/legal-text-detail/legal-text-detail.module').then(
            (m) => m.LegalTextDetailPageModule,
          ),
      },
    ],
  },
  {
    path: 'reports',
    loadChildren: () => import('./modules/reports/report.module').then((m) => m.ReportModule),
  },
  {
    path: 'associate-evaluation-modal',
    loadChildren: () =>
      import(
        './modules/organization/organizations/associate-evaluation-modal/associate-evaluation-modal.module'
      ).then((m) => m.AssociateEvaluationModalPageModule),
  },
  {
    path: 'report-selection-error-modal',
    loadChildren: () =>
      import(
        './modules/organization/organizations/report-selection-error-modal/report-selection-error-modal.module'
      ).then((m) => m.ReportSelectionErrorModalPageModule),
  },
  {
    path: '**',
    loadChildren: () =>
      import('./pages/not-found/not-found.module').then((m) => m.NotFoundPageModule),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })],
  exports: [RouterModule],
})
export class AppRoutingModule {}
