import { HttpErrorResponse, HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { EMPTY, catchError, throwError } from 'rxjs';
import { UserService } from './services/user/user.service';
import { LoadingController, NavController } from '@ionic/angular';
import { ErrorResponse } from './services/admin/admin.service';

export const unauthorizedInterceptor: HttpInterceptorFn = (req, next): any => {
  const userService = inject(UserService);
  const navController = inject(NavController);
  const loadingController = inject(LoadingController);

  return next(req).pipe(
    catchError((error): any => {
      if (error instanceof HttpErrorResponse) {
        loadingController.dismiss();
        if (error.status === 401 && userService.isLoggedIn()) {
          userService.logout();
          navController.navigateRoot('/login');
          return EMPTY;
        } else {
          if (error.error['localizedMessage'] == null) {
            const response = new HttpErrorResponse({
              error: new ErrorResponse(error.message) as any,
              status: error.status,
            });
            return throwError(() => response);
          }
          return throwError(() => error);
        }
      } else {
        return throwError(() => error);
      }
    }),
  );
};
