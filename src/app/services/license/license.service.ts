import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { LicensesFilter } from '../../pages/admin/licenses/licenses.page';
import { EmptyResponse } from '../api/response/empty-response';
import { NonEmptyResponse } from '../evaluation/evaluation.service';
import { UserService } from '../user/user.service';

export class LicensesResponse {
  licenses: Array<AppUserLicense>;
}

export class AppUserLicense {
  constructor(
    id: number,
    name: string,
    licenseKey: string,
    totalActivations: number,
    remainingActivations: number,
    validFrom: Date,
    validUntil: Date,
    autoGenerated: boolean,
    childrenEvaluationData: ChildrenEvaluationLicense,
    externalEvaluationData: ExternalEvaluationLicense,
    selfEvaluationData: SelfEvaluationLicense,
  ) {
    this.id = id;
    this.name = name;
    this.licenseKey = licenseKey;
    this.totalActivations = totalActivations;
    this.remainingActivations = remainingActivations;
    this.validFrom = validFrom;
    this.validUntil = validUntil;
    this.autoGenerated = autoGenerated;
    this.childrenEvaluationData = childrenEvaluationData;
    this.externalEvaluationData = externalEvaluationData;
    this.selfEvaluationData = selfEvaluationData;
  }

  id: number;
  name: string;
  licenseKey: string;
  totalActivations: number;
  remainingActivations: number;
  validFrom: Date;
  validUntil: Date;
  autoGenerated: boolean;
  childrenEvaluationData: ChildrenEvaluationLicense;
  externalEvaluationData: ExternalEvaluationLicense;
  selfEvaluationData: SelfEvaluationLicense;
}

export class ChildrenEvaluationLicense {
  licenseId: number;
  totalEvaluationsRemaining: number;
}

export class ExternalEvaluationLicense {
  licenseId: number;
  totalEvaluationsRemaining: number;
  scaleTypePermissionGrazias: boolean;
  scaleTypePermissionGraziasV2: boolean;
  scaleTypePermissionMath: boolean;
  scaleTypePermissionNature: boolean;
  scaleTypePermissionDidactics: boolean;
}

export class SelfEvaluationLicense {
  licenseId: number;
  totalEvaluationsRemaining: number;
}

export class UpsertLicenseRequest {
  licenseId?: number;
  name: string;
  remainingActivations: number;
  validFrom: Date;
  validUntil: Date;
  childrenEvaluationLicenseRequest?: ChildrenEvaluationLicenseRequest;
  externalEvaluationLicenseRequest?: ExternalEvaluationLicenseRequest;
  selfEvaluationLicenseRequest?: SelfEvaluationLicenseRequest;
}

export class ChildrenEvaluationLicenseRequest {
  childrenEvaluationLicenseId?: number;
  totalEvaluationsRemaining: number;
}

export class ExternalEvaluationLicenseRequest {
  externalEvaluationLicenseId?: number;
  totalEvaluationsRemaining: number;
  scaleTypePermissionGrazias = false;
  scaleTypePermissionGraziasV2: boolean;
  scaleTypePermissionMath: boolean;
  scaleTypePermissionNature: boolean;
  scaleTypePermissionDidactics: boolean;
}

export class SelfEvaluationLicenseRequest {
  selfEvaluationLicenseId?: number;
  totalEvaluationsRemaining: number;
}

@Injectable({
  providedIn: 'root',
})
export class LicenseService {
  constructor(
    private http: HttpClient,
    private userService: UserService,
  ) {}

  getLicenses(filter: LicensesFilter): Observable<NonEmptyResponse<LicensesResponse>> {
    return this.http
      .get<LicensesResponse>(`${environment.apiBaseUrl}/api/v1/licenses/${filter}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response: LicensesResponse) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  createOrUpdateLicense(request: UpsertLicenseRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/licenses`, request, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage, undefined, errorResp.status));
        }),
      );
  }
}
