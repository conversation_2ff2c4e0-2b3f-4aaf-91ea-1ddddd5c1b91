import { getScaleTypeFromDefinition } from './scale-type-definition';
import { ScaleAgeDefinition, ScaleDefinition } from './scale-definition';
import { Trait } from './trait';

export class Scale {
  constructor(
    public scaleDefinition: ScaleDefinition,
    public traits: Map<string, Trait> = new Map(),
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static fromJson(scaleJson: any): Scale {
    const scale = new Scale(scaleJson.scaleDefinition);
    Object.keys(scaleJson.traits).forEach((traitId) => {
      scale.traits.set(traitId, Trait.fromJson(scaleJson.traits[traitId], scale));
    });

    return scale;
  }

  static getScaleAgeFromDefinition(scaleDefinition: ScaleDefinition): ScaleAgeDefinition {
    if (scaleDefinition.includes('ZERO_TO_THREE')) {
      return ScaleAgeDefinition.ZERO_TO_THREE;
    }
    if (scaleDefinition.includes('THREE_TO_SIX')) {
      return ScaleAgeDefinition.THREE_TO_SIX;
    }
    if (scaleDefinition.includes('ZERO_TO_SIX')) {
      return ScaleAgeDefinition.ZERO_TO_SIX;
    }
  }

  scaleDefinitionBaseNameKey(): string {
    return getScaleTypeFromDefinition(this.scaleDefinition);
  }

  localizationKeyPrefix(): string {
    return `scale.${this.scaleDefinition}`;
  }
}
