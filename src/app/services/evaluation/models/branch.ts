import { Item } from './item';
import { Trait } from './trait';

export class Branch {
  public notes = new Map<string, string>(); // educatorId -> note
  constructor(
    public id: string,
    public codebookKey: string,
    public sortOrder: number,
    public trait: Trait,
    public items: Map<string, Item> = new Map(),
    public scores: Map<string, number> = new Map(),
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static from<PERSON><PERSON>(branchJson: any, trait: Trait): Branch {
    const branch = new Branch(branchJson.id, branchJson.codebookKey, branchJson.sortOrder, trait);

    if (branchJson.scores) {
      Object.keys(branchJson.scores).forEach((educatorId) => {
        branch.scores.set(educatorId, branchJson.scores[educatorId]);
      });
    }

    Object.keys(branchJson.notes || {}).forEach((educatorId) => {
      branch.notes.set(educatorId, branchJson.notes[educatorId]);
    });

    Object.keys(branchJson.items).forEach((itemId) => {
      branch.items.set(itemId, Item.fromJson(branchJson.items[itemId], branch));
    });

    return branch;
  }

  localizationKeyPrefix(): string {
    return `${this.trait.localizationKeyPrefix()}.${this.id}`;
  }

  localizationKey(): string {
    return `${this.localizationKeyPrefix()}.title`;
  }
}
