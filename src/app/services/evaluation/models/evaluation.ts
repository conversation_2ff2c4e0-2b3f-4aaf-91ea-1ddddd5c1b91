import { Educator } from './educator';
import { Scale } from './scale';
import { ScaleDefinition } from './scale-definition';
import {
  getScaleTypeFromDefinition,
  getSortOrderForScaleTypeDefinition,
} from './scale-type-definition';

export class Evaluation {
  localId: string;
  customerServiceId?: string;
  createdAt: string;
  remoteId?: string | undefined;
  institutionName?: string | undefined;
  country?: string | undefined;
  dateOfSurvey?: string | undefined;
  language?: string | undefined;
  groupName?: string | undefined;
  projectAssignment?: string | undefined;
  nrOfDeclaredChildren?: number | undefined;
  nrOfPresentChildren?: number | undefined;
  ageOfYoungestInMonths?: number | undefined;
  ageOfOldestInMonths?: number | undefined;
  nrOfDisabledChildren?: number | undefined;
  nrOfNoneNativeSpeaker?: number | undefined;
  roomSize?: number | undefined;
  outsideAreaSize?: number | undefined;
  roomNames?: string | undefined;
  notes?: string | undefined;
  educators: Educator[];
  scales: Map<ScaleDefinition, Scale> = new Map();
  age: string | undefined;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static fromJson(evaluationJson: any): Evaluation {
    const evaluation = new Evaluation();

    const educators = [];
    if (evaluationJson.educators) {
      evaluationJson.educators.forEach((educatorJson) => {
        educators.push(Educator.fromJson(educatorJson));
      });
    }

    const scales = new Map<ScaleDefinition, Scale>();
    if (evaluationJson.scales) {
      Object.keys(evaluationJson.scales).forEach((scaleId) => {
        const scale = Scale.fromJson(evaluationJson.scales[scaleId]);
        scales.set(scale.scaleDefinition, scale);
      });
    }

    evaluation.localId = evaluationJson.localId;
    evaluation.customerServiceId = evaluationJson.customerServiceId;
    evaluation.createdAt = evaluationJson.createdAt;
    evaluation.remoteId = evaluationJson.remoteId;
    evaluation.institutionName = evaluationJson.institutionName;
    evaluation.country = evaluationJson.country;
    evaluation.dateOfSurvey = evaluationJson.dateOfSurvey;
    evaluation.language = evaluationJson.language;
    evaluation.groupName = evaluationJson.groupName;
    evaluation.projectAssignment = evaluationJson.projectAssignment;
    evaluation.nrOfDeclaredChildren = evaluationJson.nrOfDeclaredChildren;
    evaluation.nrOfPresentChildren = evaluationJson.nrOfPresentChildren;
    evaluation.ageOfYoungestInMonths = evaluationJson.ageOfYoungestInMonths;
    evaluation.ageOfOldestInMonths = evaluationJson.ageOfOldestInMonths;
    evaluation.nrOfDisabledChildren = evaluationJson.nrOfDisabledChildren;
    evaluation.nrOfNoneNativeSpeaker = evaluationJson.nrOfNoneNativeSpeaker;
    evaluation.roomSize = evaluationJson.roomSize;
    evaluation.outsideAreaSize = evaluationJson.outsideAreaSize;
    evaluation.roomNames = evaluationJson.roomNames;
    evaluation.notes = evaluationJson.notes;
    evaluation.educators = educators;
    evaluation.scales = scales;

    return evaluation;
  }

  getScaleFromDefinition(scaleDef: ScaleDefinition): Scale {
    return this.scales.get(scaleDef);
  }

  getAllScaleDefinitions(): Array<ScaleDefinition> {
    const scales: Array<ScaleDefinition> = [];
    Array.from(this.scales.keys()).forEach((scale) => {
      scales.push(scale);
    });
    return scales;
  }

  getSortedScaleDefinitions(): Array<ScaleDefinition> {
    const scales = this.getAllScaleDefinitions();
    return scales.sort((scaleDef1, scaleDef2) => {
      const n1 = this.getSortingOrder(scaleDef1);
      const n2 = this.getSortingOrder(scaleDef2);

      if (n1 > n2) {
        return 1;
      }
      if (n1 < n2) {
        return -1;
      }
      return 0;
    });
  }

  private getSortingOrder(scale: ScaleDefinition): number {
    const definition = getScaleTypeFromDefinition(scale);

    return getSortOrderForScaleTypeDefinition(definition);
  }
}
