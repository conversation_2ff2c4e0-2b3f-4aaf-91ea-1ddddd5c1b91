import { Branch } from './branch';
import { Scale } from './scale';

export class Trait {
  constructor(
    public id: string,
    public codebookKey: string,
    public scale: Scale,
    public teamScore: number | null = null,
    public scores: Map<string, number> = new Map(),
    public branches: Map<string, Branch> = new Map(),
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static from<PERSON><PERSON>(traitJson: any, scale: Scale): Trait {
    const trait = new Trait(traitJson.id, traitJson.codebookKey, scale, traitJson.teamScore);

    if (traitJson.scores) {
      Object.keys(traitJson.scores).forEach((educatorId) => {
        trait.scores.set(educatorId, traitJson.scores[educatorId]);
      });
    }

    Object.keys(traitJson.branches).forEach((branchId) => {
      trait.branches.set(branchId, Branch.fromJson(traitJson.branches[branchId], trait));
    });

    return trait;
  }

  localizationKeyPrefix(): string {
    return `${this.scale.localizationKeyPrefix()}.${this.id}`;
  }

  localizationKey(): string {
    return `${this.localizationKeyPrefix()}.title`;
  }

  getBubbleChartTitleLocalizationKey(): string {
    return `${this.localizationKeyPrefix()}.bubbleChart`;
  }

  getExplanationLocalizationKey(): string {
    return `${this.localizationKeyPrefix()}.explanation`;
  }
}
