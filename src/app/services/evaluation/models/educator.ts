import _ from 'lodash';

export class Educator {
  email: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  category: string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static from<PERSON><PERSON>(educatorJson: any): Educator {
    const educator = new Educator();

    educator.email = educatorJson.email;
    educator.firstName = educatorJson.firstName;
    educator.lastName = educatorJson.lastName;
    educator.dateOfBirth = educatorJson.dateOfBirth;
    educator.category = educatorJson.category;

    return educator;
  }

  id(): string {
    return btoa(this.email);
  }

  fullName(): string {
    if (_.isEmpty(this.firstName)) {
      if (_.isEmpty(this.lastName)) {
        return '-';
      } else {
        return this.lastName;
      }
    } else {
      if (_.isEmpty(this.lastName)) {
        return this.firstName;
      } else {
        return `${this.firstName} ${this.lastName}`;
      }
    }
  }
}
