import { Branch } from './branch';

export class Item {
  public answers = new Map<string, string>(); // educatorId -> option
  constructor(
    public id: string,
    public branch: Branch,
    public options: string[],
    public additionalInformation: boolean = false,
    public codebookKey: string,
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static fromJson(itemJson: any, branch: Branch): Item {
    const item = new Item(
      itemJson.id,
      branch,
      itemJson.options,
      itemJson.additionalInformation,
      itemJson.codebookKey,
    );
    Object.keys(itemJson.answers).forEach((educatorId) => {
      item.answers.set(educatorId, itemJson.answers[educatorId]);
    });

    return item;
  }
}
