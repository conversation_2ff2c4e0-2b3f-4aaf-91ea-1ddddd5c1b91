import { ScaleDefinition } from './scale-definition';

export enum ScaleTypeDefinition {
  DIDACTICS = 'global.label.didactics',
  GRAZIAS = 'global.label.grazias',
  GRAZIAS_V2 = 'global.label.grazias_v2',
  MATH = 'global.label.math',
  NATURE = 'global.label.nature',
}

export function getScaleTypeFromDefinition(scaleDefinition: ScaleDefinition): ScaleTypeDefinition {
  switch (scaleDefinition.split('_')[0]) {
    case 'DIDACTICS':
      return ScaleTypeDefinition.DIDACTICS;
    case 'GRAZIAS':
      return scaleDefinition.split('_')[1] === 'V2'
        ? ScaleTypeDefinition.GRAZIAS_V2
        : ScaleTypeDefinition.GRAZIAS;
    case 'MATH':
      return ScaleTypeDefinition.MATH;
    case 'NATURE':
      return ScaleTypeDefinition.NATURE;
  }
}

export function getSortOrderForScaleTypeDefinition(
  scaleTypeDefinition: ScaleTypeDefinition,
): number {
  switch (scaleTypeDefinition) {
    case ScaleTypeDefinition.GRAZIAS:
      return 0;
    case ScaleTypeDefinition.GRAZIAS_V2:
      return 1;
    case ScaleTypeDefinition.DIDACTICS:
      return 2;
    case ScaleTypeDefinition.MATH:
      return 3;
    case ScaleTypeDefinition.NATURE:
      return 4;
  }
}
