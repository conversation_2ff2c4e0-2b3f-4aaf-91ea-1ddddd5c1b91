export enum ScaleDefinition {
  D<PERSON><PERSON><PERSON>CS_ZERO_TO_THREE = 'DID<PERSON><PERSON>CS_ZERO_TO_THREE',
  DIDACTICS_THREE_TO_SIX = 'DIDACTICS_THREE_TO_SIX',
  DIDACTICS_ZERO_TO_SIX = 'DIDACTICS_ZERO_TO_SIX',
  DIDACTICS_SELF_EVALUATION_ZERO_TO_THREE = 'DIDACTICS_SELF_EVALUATION_ZERO_TO_THREE',
  DIDACTICS_SELF_EVALUATION_THREE_TO_SIX = 'DIDACTICS_SELF_EVALUATION_THREE_TO_SIX',
  DIDACTICS_SELF_EVALUATION_ZERO_TO_SIX = 'DIDACTICS_SELF_EVALUATION_ZERO_TO_SIX',
  GRAZIAS_ZERO_TO_THREE = 'GRAZIAS_ZERO_TO_THREE',
  GRAZIAS_THREE_TO_SIX = 'GRAZIAS_THREE_TO_SIX',
  GRAZIAS_ZERO_TO_SIX = 'GRAZIAS_ZERO_TO_SIX',
  GRAZIAS_SELF_EVALUATION_ZERO_TO_THREE = 'GRAZIAS_SELF_EVALUATION_ZERO_TO_THREE',
  GRAZIAS_SELF_EVALUATION_THREE_TO_SIX = 'GRAZIAS_SELF_EVALUATION_THREE_TO_SIX',
  GRAZIAS_SELF_EVALUATION_ZERO_TO_SIX = 'GRAZIAS_SELF_EVALUATION_ZERO_TO_SIX',
  GRAZIAS_V2_ZERO_TO_SIX = 'GRAZIAS_V2_ZERO_TO_SIX',
  GRAZIAS_V2_SELF_EVALUATION_ZERO_TO_SIX = 'GRAZIAS_V2_SELF_EVALUATION_ZERO_TO_SIX',
  MATH_ZERO_TO_THREE = 'MATH_ZERO_TO_THREE',
  MATH_THREE_TO_SIX = 'MATH_THREE_TO_SIX',
  MATH_ZERO_TO_SIX = 'MATH_ZERO_TO_SIX',
  MATH_SELF_EVALUATION_ZERO_TO_THREE = 'MATH_SELF_EVALUATION_ZERO_TO_THREE',
  MATH_SELF_EVALUATION_THREE_TO_SIX = 'MATH_SELF_EVALUATION_THREE_TO_SIX',
  MATH_SELF_EVALUATION_ZERO_TO_SIX = 'MATH_SELF_EVALUATION_ZERO_TO_SIX',
  NATURE_ZERO_TO_THREE = 'NATURE_ZERO_TO_THREE',
  NATURE_THREE_TO_SIX = 'NATURE_THREE_TO_SIX',
  NATURE_ZERO_TO_SIX = 'NATURE_ZERO_TO_SIX',
  NATURE_SELF_EVALUATION_ZERO_TO_THREE = 'NATURE_SELF_EVALUATION_ZERO_TO_THREE',
  NATURE_SELF_EVALUATION_THREE_TO_SIX = 'NATURE_SELF_EVALUATION_THREE_TO_SIX',
  NATURE_SELF_EVALUATION_ZERO_TO_SIX = 'NATURE_SELF_EVALUATION_ZERO_TO_SIX',
}

export enum ScaleAgeDefinition {
  ZERO_TO_THREE = 'global.label.0to3',
  THREE_TO_SIX = 'global.label.3to6',
  ZERO_TO_SIX = 'global.label.0to6',
}
