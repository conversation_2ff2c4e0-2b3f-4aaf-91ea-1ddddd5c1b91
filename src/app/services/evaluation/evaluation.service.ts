import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Evaluation } from './models/evaluation';

export class NonEmptyResponse<T> {
  constructor(
    public success: boolean,
    public errorMessage: string,
    public data?: T,
    public notFound?: boolean,
    public httpStatus?: number,
  ) {}
}

@Injectable({
  providedIn: 'root',
})
export class EvaluationService {
  constructor(private http: HttpClient) {}

  getEvaluationResult(
    accessId: string,
    evaluationRemoteId: string,
  ): Observable<NonEmptyResponse<Evaluation>> {
    return this.http
      .get<Evaluation>(
        `${environment.apiBaseUrl}/api/v1/evaluation-result/${accessId}/${evaluationRemoteId}`,
        {},
      )
      .pipe(
        map((response) => {
          const evaluation = Evaluation.fromJson(response);
          return new NonEmptyResponse<Evaluation>(true, '', evaluation, false);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(
            new NonEmptyResponse<Evaluation>(
              false,
              errorMessage,
              undefined,
              errorResp.status === 404,
            ),
          );
        }),
      );
  }

  getSelfEvaluationResult(
    accessId: string,
    evaluationRemoteId: string,
  ): Observable<NonEmptyResponse<Evaluation>> {
    return this.http
      .get<Evaluation>(
        `${environment.apiBaseUrl}/api/v1/educator/evaluation-result/${accessId}/${evaluationRemoteId}`,
        {},
      )
      .pipe(
        map((response) => {
          const evaluation = Evaluation.fromJson(response);
          return new NonEmptyResponse<Evaluation>(true, '', evaluation, false);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(
            new NonEmptyResponse<Evaluation>(
              false,
              errorMessage,
              undefined,
              errorResp.status === 404,
            ),
          );
        }),
      );
  }
}
