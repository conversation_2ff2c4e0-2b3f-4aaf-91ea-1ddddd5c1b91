import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  TraitColorsDidacticsThreeToSix,
  TraitColorsDidacticsZeroToSix,
  TraitColorsDidacticsZeroToThree,
  TraitColorsGraziasThreeToSix,
  TraitColorsGraziasV2,
  TraitColorsGraziasZeroToSix,
  TraitColorsGraziasZeroToThree,
  TraitColorsMathThreeToSix,
  TraitColorsMathZeroToSix,
  TraitColorsMathZeroToThree,
  TraitColorsNatureThreeToSix,
  TraitColorsNatureZeroToSix,
  TraitColorsNatureZeroToThree,
} from '../../modules/shared/components/charts/trait-colors';
import { Evaluation } from './models/evaluation';
import { Scale } from './models/scale';
import { ScaleAgeDefinition, ScaleDefinition } from './models/scale-definition';
import { ScaleTypeDefinition } from './models/scale-type-definition';

@Injectable({
  providedIn: 'root',
})
export class ScaleService {
  constructor(private translate: TranslateService) {}

  getTraitColorsForScale(scaleDef: ScaleDefinition) {
    switch (scaleDef) {
      case ScaleDefinition.DIDACTICS_ZERO_TO_THREE: {
        return TraitColorsDidacticsZeroToThree;
      }
      case ScaleDefinition.DIDACTICS_SELF_EVALUATION_ZERO_TO_THREE: {
        return TraitColorsDidacticsZeroToThree;
      }
      case ScaleDefinition.DIDACTICS_THREE_TO_SIX: {
        return TraitColorsDidacticsThreeToSix;
      }
      case ScaleDefinition.DIDACTICS_SELF_EVALUATION_THREE_TO_SIX: {
        return TraitColorsDidacticsThreeToSix;
      }
      case ScaleDefinition.DIDACTICS_ZERO_TO_SIX: {
        return TraitColorsDidacticsZeroToSix;
      }
      case ScaleDefinition.DIDACTICS_SELF_EVALUATION_ZERO_TO_SIX: {
        return TraitColorsDidacticsZeroToSix;
      }

      case ScaleDefinition.GRAZIAS_ZERO_TO_THREE: {
        return TraitColorsGraziasZeroToThree;
      }
      case ScaleDefinition.GRAZIAS_SELF_EVALUATION_ZERO_TO_THREE: {
        return TraitColorsGraziasZeroToThree;
      }
      case ScaleDefinition.GRAZIAS_THREE_TO_SIX: {
        return TraitColorsGraziasThreeToSix;
      }
      case ScaleDefinition.GRAZIAS_SELF_EVALUATION_THREE_TO_SIX: {
        return TraitColorsGraziasThreeToSix;
      }
      case ScaleDefinition.GRAZIAS_ZERO_TO_SIX: {
        return TraitColorsGraziasZeroToSix;
      }
      case ScaleDefinition.GRAZIAS_SELF_EVALUATION_ZERO_TO_SIX: {
        return TraitColorsGraziasZeroToSix;
      }

      case ScaleDefinition.MATH_ZERO_TO_THREE: {
        return TraitColorsMathZeroToThree;
      }
      case ScaleDefinition.MATH_SELF_EVALUATION_ZERO_TO_THREE: {
        return TraitColorsMathZeroToThree;
      }
      case ScaleDefinition.MATH_THREE_TO_SIX: {
        return TraitColorsMathThreeToSix;
      }
      case ScaleDefinition.MATH_SELF_EVALUATION_THREE_TO_SIX: {
        return TraitColorsMathThreeToSix;
      }
      case ScaleDefinition.MATH_ZERO_TO_SIX: {
        return TraitColorsMathZeroToSix;
      }
      case ScaleDefinition.MATH_SELF_EVALUATION_ZERO_TO_SIX: {
        return TraitColorsMathZeroToSix;
      }

      case ScaleDefinition.NATURE_ZERO_TO_THREE: {
        return TraitColorsNatureZeroToThree;
      }
      case ScaleDefinition.NATURE_SELF_EVALUATION_ZERO_TO_THREE: {
        return TraitColorsNatureZeroToThree;
      }
      case ScaleDefinition.NATURE_THREE_TO_SIX: {
        return TraitColorsNatureThreeToSix;
      }
      case ScaleDefinition.NATURE_SELF_EVALUATION_THREE_TO_SIX: {
        return TraitColorsNatureThreeToSix;
      }
      case ScaleDefinition.NATURE_ZERO_TO_SIX: {
        return TraitColorsNatureZeroToSix;
      }
      case ScaleDefinition.NATURE_SELF_EVALUATION_ZERO_TO_SIX: {
        return TraitColorsNatureZeroToSix;
      }
      case ScaleDefinition.GRAZIAS_V2_ZERO_TO_SIX: {
        return TraitColorsGraziasV2;
      }
      case ScaleDefinition.GRAZIAS_V2_SELF_EVALUATION_ZERO_TO_SIX: {
        return TraitColorsGraziasV2;
      }
      default: {
        return TraitColorsGraziasZeroToThree;
      }
    }
  }

  getScaleAndAgeLocalizedString(scaleDefinition: ScaleDefinition, evaluation: Evaluation): string {
    const currentScale = evaluation.scales.get(scaleDefinition);
    let scaleTitle = this.translate.instant(currentScale.scaleDefinitionBaseNameKey());

    switch (Scale.getScaleAgeFromDefinition(scaleDefinition)) {
      case ScaleAgeDefinition.ZERO_TO_THREE: {
        scaleTitle += ' 0-3';
        break;
      }
      case ScaleAgeDefinition.THREE_TO_SIX: {
        scaleTitle += ' 3-6';
        break;
      }
      case ScaleAgeDefinition.ZERO_TO_SIX: {
        scaleTitle += ' 0-6';
        break;
      }
    }
    return scaleTitle;
  }

  getSpecificScaleNameTranslation(scale: ScaleTypeDefinition): string {
    switch (scale) {
      case ScaleTypeDefinition.GRAZIAS:
        return this.translate.instant('global.label.specificScaleTitle.basic');
      case ScaleTypeDefinition.GRAZIAS_V2:
        return this.translate.instant('global.label.specificScaleTitle.basic_v2');
      case ScaleTypeDefinition.NATURE:
        return this.translate.instant('global.label.specificScaleTitle.nature');
      case ScaleTypeDefinition.MATH:
        return this.translate.instant('global.label.specificScaleTitle.math');
      case ScaleTypeDefinition.DIDACTICS:
        return this.translate.instant('global.label.specificScaleTitle.didactics');
    }
  }

  getFullScaleName(scale: ScaleTypeDefinition): string {
    return 'GrazIAS ' + this.translate.instant(this.getSpecificScaleNameTranslation(scale));
  }

  hasScoringSchemeV2(scale: ScaleDefinition): boolean {
    return (
      scale === ScaleDefinition.GRAZIAS_V2_ZERO_TO_SIX ||
      scale === ScaleDefinition.GRAZIAS_V2_SELF_EVALUATION_ZERO_TO_SIX
    );
  }
}
