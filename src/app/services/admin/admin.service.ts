import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ParamMap } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map, timeout } from 'rxjs/operators';
import { DataExportType } from 'src/app/model/DataExportType';
import {
  CreateUserRequest,
  DeleteEvalOrAdminUserRequest,
  UpdateUserRequest,
} from 'src/app/pages/create-user-form/create-user-form.page';
import { environment } from 'src/environments/environment';
import { EvaluationType } from '../../modules/organization/organizations/utils';
import { ChildrenEvaluationReportDo } from '../../modules/reports/children-evaluation-report/models/children-evaluation-report';
import { Languages } from '../../utils/language-helper';
import { EmptyResponse } from '../api/response/empty-response';
import { NonEmptyResponse } from '../evaluation/evaluation.service';
import { Evaluation } from '../evaluation/models/evaluation';
import { ScaleDefinition } from '../evaluation/models/scale-definition';
import { SurveyType } from '../jwt/survey-token.service';
import { UserRole, UserService } from '../user/user.service';

export class AdminUserListResponseItem {
  firstName: string;
  lastName: string;
  mailAddress?: string;
  userName: string;
  trackingId: string;
  role: UserRole;
  createdAt: Date;
}

export class AdminPendingUserListResponseItem {
  firstName?: string;
  lastName?: string;
  mailAddress?: string;
  userName: string;
  role: UserRole;
}

export class AdminUserListResponse {
  users: Array<AdminUserListResponseItem>;
  pendingUsers: Array<AdminPendingUserListResponseItem>;
}

export class EducatorSearchRequest {
  email: string;

  constructor(email: string) {
    this.email = email;
  }
}

export class EducatorDeleteRequest {
  id: string;

  constructor(id: string) {
    this.id = id;
  }
}

export class EducatorSearchResponse {
  email: string;
  generatedId: string;
}

export class ProjectResponseItem {
  id: number;
  name: string;
}

export class ProjectResponse {
  projects: Array<ProjectResponseItem>;
}

export class CreateProjectRequest {
  projectName: string;

  constructor(projectName: string) {
    this.projectName = projectName;
  }
}

export class UpdateProjectRequest {
  id: number;
  projectName: string;

  constructor(id: number, projectName: string) {
    this.id = id;
    this.projectName = projectName;
  }
}

export class OrganizationsWithLocationsAndGroupsResponse {
  result: Array<OrgAndLocations>;
  totalCount: number;
  hasPagination: boolean;
}

export class CreateOrganizationRequest {
  organizationName: string;
  initialContactName: string;
  initialContactMail: string;
  initialContactPhone?: string;
  tags: Array<string>;
  notes?: string;
}

export class CreateLocationRequest {
  organizationId: number;
  locationName: string;
  street: string;
  streetNumber: string;
  zipCode: string;
  city: string;
  country: string;
  primaryLocationManagementFirstName?: string;
  primaryLocationManagementLastName?: string;
  primaryLocationManagementMail?: string;
  secondaryLocationManagementFirstName?: string;
  secondaryLocationManagementLastName?: string;
  secondaryLocationManagementMail?: string;
  locationPhone?: string;
  numberOfEmployedEducators?: number;
  locationDescription?: string;
  tags: Array<string>;
  notes?: string;
}

export class CreateGroupRequest {
  locationId: number;
  groupName: string;
  numberOfEvaluableEducators?: number;
  childrensAgeRange?: Childrens_Age_Range;
  tags: Array<string>;
  notes?: string;
}

export enum Childrens_Age_Range {
  ZERO_TO_THREE,
  ZERO_TO_SIX,
  THREE_TO_SIX,
}

export class OrgAndLocations {
  organization: OrganizationListDo;
  locations: Array<LocationAndGroups>;
}

class OrganizationListDo {
  id: number;
  organizationName: string;
  tags: Array<string>;
  surveys: Array<Survey>;
}

class LocationAndGroups {
  location: LocationListDo;
  groups: Array<GroupListDo>;
}

class LocationListDo {
  id: number;
  locationName: string;
  tags: Array<string>;
  surveys: Array<Survey>;
}

class GroupListDo {
  id: number;
  groupName: string;
  tags: Array<string>;
  surveys: Array<Survey>;
  evaluations: Array<EvaluationInfoDto>;
  childEvaluations: Array<ChildEvaluationInfoDto>;
}

export class EvaluationInfoDto {
  id: string;
  dateOfSurvey: string;
  evaluatorFirstName: string;
  evaluatorLastName: string;
  scales: Array<ScaleDefinition>;
}

export class ChildEvaluationInfoDto {
  id: string;
  dateOfSurvey: string;
  evaluatorFirstName: string;
  evaluatorLastName: string;
}

export class OrganizationDo {
  id: number;
  organizationName: string;
  initialContactName: string;
  initialContactMail: string;
  initialContactPhone: string;
  tags: Array<string>;
  notes: string;
  createdAt: Date;
  surveys: Array<Survey>;
}

export class OrganizationResponse {
  result: OrganizationDo;
}

export class LocationDo {
  id: number;
  organizationId: number;
  locationName: string;
  street: string;
  streetNumber: string;
  zipCode: string;
  city: string;
  country: string;
  primaryLocationManagementFirstName: string;
  primaryLocationManagementLastName: string;
  primaryLocationManagementMail: string;
  secondaryLocationManagementFirstName: string;
  secondaryLocationManagementLastName: string;
  secondaryLocationManagementMail: string;
  locationPhone: string;
  numberOfEmployedEducators: number;
  locationDescription: string;
  tags: Array<string>;
  notes: string;
  createdAt: string;
}

export class LocationResponse {
  location: LocationDo;
}

export class GroupDo {
  id: number;
  locationId: number;
  groupName: string;
  numberOfEvaluableEducators: number;
  childrensAgeRange: string;
  tags: Array<string>;
  notes: string;
  createdAt: string;
}

export class GroupResponse {
  group: GroupDo;
}

export class CreateSurveyRequest {
  entityId: number;
  type: string;
  token: string;
  validFrom: Date;
  validTo: Date;
  email: string;
  tags: Array<string>;
  note?: string;
}

export class Survey {
  id: number;
  type: SurveyType;
  token: string;
  state: SurveyState;
  validFrom: Date;
  validUntil: Date;
  email: string;
  tags: Array<string>;
  note: string;
  createdAt: Date;
  numberOfResults?: number;
}

export enum SurveyState {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  NO_RESULTS_AVAILABLE = 'NO_RESULTS_AVAILABLE',
  EXPIRED = 'EXPIRED',
}

export class SurveyResponse {
  survey: Survey;
}

export class SurveyTokenRequest {
  type: string;
  entityId: number;
}

export class SurveyTokenResponse {
  token: string;
}

export class SurveyInfrastructureResponse {
  result: SurveyInfrastructureResult;
}

export class SurveyInfrastructureResult {
  surveyId: number;
  organizationName: string;
  organizationId: number;
  locationName: string;
  locationId: number;
  groupName: string;
  groupId: number;
}

export class OrganizationStructureNamesRequest {
  organizationId: number;
  locationId?: number;
  groupId?: number;
}

export class OrganizationStructureNamesResponse {
  result: OrganizationStructureNamesResult;
}

export class OrganizationStructureNamesResult {
  organizationName: string;
  locationName?: string;
  groupName?: string;
}

export class StoreSurveyAnswerRequest {
  surveyToken: string;
  surveyData: string;
  surveyLanguage: string;
}

export class SurveyUrlValidResponse {
  valid: boolean;
}

export class AvailableTagsResponse {
  result: Array<string>;
}

export class EvaluationGroupInformationResponse {
  result: EvaluationGroupInformation;
}

export class EvaluationGroupInformation {
  customerServiceId: string;
  evaluationId: string;
  type: EvaluationType;
  dateOfSurvey: string;
  evaluator: string;
  groupId?: number;
}

export interface OrganizationEvaluationDetail {
  customerServiceId: string;
  organizationName: string;
  locationName: string;
  groupName: string;
  evaluator: string;
  numberOfParticipants: number;
  uploadedAt: string;
}

export class OrganizationReportDataRequest {
  constructor(
    public surveyIds: Array<number>,
    public childEvaluationIds: Array<string>,
    public evaluationIds: Array<string>,
  ) {}

  static fromRequestParameters(params: ParamMap): OrganizationReportDataRequest {
    return new OrganizationReportDataRequest(
      params.getAll('surveyIds').map((p) => parseInt(p, 10)),
      params.getAll('childEvaluationIds'),
      params.getAll('evaluationIds'),
    );
  }

  toRequestParameters(node: string): { [param: string]: string | string[] } {
    return {
      surveyIds: this.surveyIds.map((p) => p.toString()),
      childEvaluationIds: this.childEvaluationIds,
      evaluationIds: this.evaluationIds,
      nodeName: node,
    };
  }
}

export class ComparisonReportDataRequest {
  constructor(
    public comparisonSetA: OrganizationReportDataRequest,
    public comparisonSetB: OrganizationReportDataRequest,
  ) {}

  static fromRequestParameters(params: ParamMap): ComparisonReportDataRequest {
    return new ComparisonReportDataRequest(
      new OrganizationReportDataRequest(
        params.getAll('surveyIdsA').map((p) => parseInt(p, 10)),
        params.getAll('childEvaluationIdsA'),
        params.getAll('evaluationIdsA'),
      ),
      new OrganizationReportDataRequest(
        params.getAll('surveyIdsB').map((p) => parseInt(p, 10)),
        params.getAll('childEvaluationIdsB'),
        params.getAll('evaluationIdsB'),
      ),
    );
  }

  toRequestParameters(node: string): { [param: string]: string | string[] } {
    return {
      surveyIdsA: this.comparisonSetA.surveyIds.map((p) => p.toString()),
      childEvaluationIdsA: this.comparisonSetA.childEvaluationIds,
      evaluationIdsA: this.comparisonSetA.evaluationIds,
      surveyIdsB: this.comparisonSetB.surveyIds.map((p) => p.toString()),
      childEvaluationIdsB: this.comparisonSetB.childEvaluationIds,
      evaluationIdsB: this.comparisonSetB.evaluationIds,
      nodeName: node,
    };
  }
}

export class OrganizationReportDataResponse {
  surveysAndResults: SurveysAndResults;
  childEvaluations: AggregatedChildEvaluations;
  evaluation?: Evaluation;
  surveysMinDate: Date;
  surveysMaxDate: Date;
}

export class AggregatedChildEvaluations {
  numberOfChildEvaluations: number;
  aggregatedChildEvaluation?: ChildrenEvaluationReportDo;
}

export class ComparisonReportDataResponse {
  surveysMinDate: Date;
  surveysMaxDate: Date;
  dataResponseSetA: OrganizationReportDataResponse;
  dataResponseSetB: OrganizationReportDataResponse;
}

export interface GenerateReportRequest {
  urlSegment: string;
  name?: string;
}

export class SurveysAndResults {
  surveys: Array<Survey>;
  organizationSurveyResults: Array<OrganizationSurveyResult>;
  administrationSurveyResults: Array<AdministrationSurveyResult>;
  parentalSurveyResults: Array<ParentalSurveyResult>;
  staffSurveyResults: Array<StaffSurveyResult>;
}

export class OrganizationSurveyResult {
  id: number;
  surveyId: number;
  createdAt: Date;
  locale: string;
  t_001_0: string;
  t_002_1: string;
  t_002_2: string;
  t_003_0: string;
  t_004_1: string;
  t_004_2: string;
  t_004_3: string;
  t_004_4: string;
  t_005_0: string;
  t_006_0: string;
  t_007_0: string;
}

export class AdministrationSurveyResult {
  id: number;
  surveyId: number;
  createdAt: Date;
  locale: string;
  locationId: number;
  l_001_1: string;
  l_001_2: string;
  l_002_0: string;
  l_003_0: string;
  l_004_0: string;
  l_005_0: string;
  l_006_0: string;
  l_007_0: string[];
  l_008_1: string;
  l_008_2: string;
  l_008_3: string;
  l_008_4: string;
  l_008_5: string;
  l_008_6: string;
  l_008_7: string;
  l_008_8: string;
  l_008_9: string;
  l_008_10: string;
  l_008_11: string;
  l_009_1: string;
  l_009_2: string;
}

export class ParentalSurveyResult {
  id: number;
  surveyId: number;
  createdAt: Date;
  locale: string;
  locationId: number;
  fam_001_0: string;
  fam_002_0: string;
  fam_003_0: string;
  fam_004_1: string;
  fam_004_2: string;
  fam_004_3: string;
  fam_004_4: string;
  fam_004_5: string;
  fam_004_6: string;
  fam_004_7: string;
  fam_004_8: string;
  fam_004_9: string;
  fam_004_10: string;
  fam_004_11: string;
  fam_004_12: string;
  fam_005_1: string;
  fam_005_2: string;
  fam_005_3: string;
}

export class StaffSurveyResult {
  id: number;
  surveyId: number;
  createdAt: Date;
  locale: string;
  pfp_001_1: string;
  pfp_001_2: string;
  pfp_001_3: string;
  pfp_001_4: string;
  pfp_002_1: string;
  pfp_002_2: string;
  pfp_002_3: string;
  pfp_002_4: string;
  pfp_003_1: string;
  pfp_003_2: string;
  pfp_003_3: string;
  pfp_003_4: string;
  pfp_004_1: string;
  pfp_004_2: string;
  pfp_005_0: Array<string>;
  pfp_006_0: string;
  pfp_007_0: string;
  pfp_008_0: string;
  pfp_009_0: string;
  pfp_010_0: string;
  pfp_011_0: string;
  pfp_012_0: string;
  pfp_013_0: string;
  pfp_014_0: string;
  pfp_015_0: string;
}

export class UserResponse {
  trackingId: string;
  firstName: string;
  lastName: string;
}

export class DataExportFilterRequest {
  constructor(
    dateFrom: string,
    dateUntil: string,
    projectIds: number[],
    trackingIds: string[],
    type: DataExportType,
  ) {
    this.dateFrom = dateFrom;
    this.dateUntil = dateUntil;
    this.projectIds = projectIds;
    this.trackingIds = trackingIds;
    this.type = type;
  }

  dateFrom: string;
  dateUntil: string;
  projectIds: number[];
  trackingIds: string[];
  type: DataExportType;
}

export class DataExportFilterResponse {
  selectStatement: string;
}

export class FilterResponse {
  queryId: string;
  selectStatement: string;
  filteredFrom: Date;
  filteredUntil: Date;
  type: DataExportType;
  queryCreatedAt: Date;
}

export class FilterListResponse {
  filter: FilterResponse[];
}

export enum LegalTextType {
  PRIVACY_STATEMENT = 'PRIVACY_STATEMENT',
  TERMS_OF_USE = 'TERMS_OF_USE',
}

export function getUrlParamFromLegalTextType(type: LegalTextType): string {
  switch (type) {
    case LegalTextType.PRIVACY_STATEMENT:
      return 'privacy-statement';
    case LegalTextType.TERMS_OF_USE:
      return 'terms-of-use';
  }
}

export function getLegalTextTypeFromUrlParam(param: string): LegalTextType {
  switch (param) {
    case 'privacy-statement':
      return LegalTextType.PRIVACY_STATEMENT;
    case 'terms-of-use':
      return LegalTextType.TERMS_OF_USE;
  }
}

export class LegalTextVersionListResponse {
  versions: LegalTextVersionListItemResponse[];
}

export class LegalTextVersionListItemResponse {
  id: string;
  version: string;
  status: LegalTextStatus;
}

export class LegalTextUpsertRequest {
  id?: string;
  deContent?: string;
  enContent?: string;
  esContent?: string;
  frContent?: string;
  huContent?: string;
  itContent?: string;
  ptContent?: string;
  slContent?: string;
  zhContent?: string;

  constructor(
    id: string,
    deContent: string,
    enContent: string,
    esContent: string,
    frContent: string,
    huContent: string,
    itContent: string,
    ptContent: string,
    slContent: string,
    zhContent: string,
  ) {
    this.id = id;
    this.deContent = deContent;
    this.enContent = enContent;
    this.esContent = esContent;
    this.frContent = frContent;
    this.huContent = huContent;
    this.itContent = itContent;
    this.ptContent = ptContent;
    this.slContent = slContent;
    this.zhContent = zhContent;
  }
}

export class UpsertLegalTextResponse {
  id: string;
}

export enum LegalTextStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  HISTORIC = 'HISTORIC',
}

export class LegalTextVersionResponse {
  id: string;
  status: LegalTextStatus;
  deContent?: string;
  enContent?: string;
  esContent?: string;
  frContent?: string;
  huContent?: string;
  itContent?: string;
  ptContent?: string;
  slContent?: string;
  zhContent?: string;

  constructor(
    id: string,
    status: LegalTextStatus,
    deContent: string,
    enContent: string,
    esContent: string,
    frContent: string,
    huContent: string,
    itContent: string,
    ptContent: string,
    slContent: string,
    zhContent: string,
  ) {
    this.id = id;
    this.status = status;
    this.deContent = deContent;
    this.enContent = enContent;
    this.esContent = esContent;
    this.frContent = frContent;
    this.huContent = huContent;
    this.itContent = itContent;
    this.ptContent = ptContent;
    this.slContent = slContent;
    this.zhContent = zhContent;
  }
}

export class ErrorResponse {
  localizedMessage: string;

  constructor(localizedMessage: string) {
    this.localizedMessage = localizedMessage;
  }
}

@Injectable({
  providedIn: 'root',
})
export class AdminService {
  constructor(
    private http: HttpClient,
    private userService: UserService,
  ) {}

  getAdminAndEvaluatorUser(): Observable<NonEmptyResponse<AdminUserListResponse>> {
    return this.http
      .get<AdminUserListResponse>(
        `${environment.apiBaseUrl}/api/v1/admin/evaluator-admin-user-list`,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  createUser(user: CreateUserRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/admin/create-user`, user, {
        observe: 'response',
        headers: new HttpHeaders()
          .append('x-auth-token', this.userService.authToken)
          // accounts that are being created by an admin should receive the email on german always (requested by KFU)
          .append('App-Lang', Languages.DE),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  anonymizeEvalOrAdminUser(user: DeleteEvalOrAdminUserRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/admin/anonymize-evaluator-or-admin`, user, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })

      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  updateUser(user: UpdateUserRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/admin/update-user`, user, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  getAvailableProjects(): Observable<NonEmptyResponse<ProjectResponse>> {
    return this.http
      .get(`${environment.apiBaseUrl}/api/v1/projects`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        timeout(1000 * 10),
        map((response: ProjectResponse) => {
          return new NonEmptyResponse<ProjectResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  createProject(request: CreateProjectRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/projects`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined, null, errorResp.status));
        }),
      );
  }

  updateProject(request: UpdateProjectRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/projects/update`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined, null, errorResp.status));
        }),
      );
  }

  getEducatorSearchResult(
    request: EducatorSearchRequest,
  ): Observable<NonEmptyResponse<EducatorSearchResponse>> {
    return this.http
      .post<EducatorSearchResponse>(
        `${environment.apiBaseUrl}/api/v1/admin/find-educator`,
        request,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response: EducatorSearchResponse) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  deleteEducator(request: EducatorDeleteRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/admin/anonymize-educator`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getOrganizationsWithLocationsAndGroups(
    tags: string[],
    offset: number,
    count: number,
  ): Observable<NonEmptyResponse<OrganizationsWithLocationsAndGroupsResponse>> {
    return this.http
      .get<OrganizationsWithLocationsAndGroupsResponse>(
        `${environment.apiBaseUrl}/api/v1/organizations/admin-list/${offset}/${count}`,
        {
          params: { tags },
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  createOrganization(
    request: CreateOrganizationRequest,
  ): Observable<NonEmptyResponse<OrganizationResponse>> {
    return this.http
      .put<OrganizationResponse>(`${environment.apiBaseUrl}/api/v1/organizations`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response.body);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  updateOrganization(request: CreateOrganizationRequest, orgId: number): Observable<EmptyResponse> {
    return this.http
      .put(`${environment.apiBaseUrl}/api/v1/organizations/${orgId}`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  createLocation(request: CreateLocationRequest): Observable<NonEmptyResponse<LocationResponse>> {
    return this.http
      .put<LocationResponse>(`${environment.apiBaseUrl}/api/v1/organizations/locations`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response.body);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  updateLocation(request: CreateLocationRequest, locationId: number): Observable<EmptyResponse> {
    return this.http
      .put(`${environment.apiBaseUrl}/api/v1/organizations/locations/${locationId}`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  createGroup(request: CreateGroupRequest): Observable<NonEmptyResponse<GroupResponse>> {
    return this.http
      .put<GroupResponse>(
        `${environment.apiBaseUrl}/api/v1/organizations/locations/groups`,
        request,
        {
          observe: 'response',
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response.body);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  updateGroup(request: CreateGroupRequest, groupId: number): Observable<EmptyResponse> {
    return this.http
      .put(`${environment.apiBaseUrl}/api/v1/organizations/locations/groups/${groupId}`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  getLocationById(id: number): Observable<NonEmptyResponse<LocationResponse>> {
    return this.http
      .get<LocationResponse>(`${environment.apiBaseUrl}/api/v1/organizations/locations/${id}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getGroupById(id: number): Observable<NonEmptyResponse<GroupResponse>> {
    return this.http
      .get<GroupResponse>(`${environment.apiBaseUrl}/api/v1/organizations/locations/groups/${id}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getOrganizationById(id: number): Observable<NonEmptyResponse<OrganizationResponse>> {
    return this.http
      .get<OrganizationResponse>(`${environment.apiBaseUrl}/api/v1/organizations/${id}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getSurveyById(id: number): Observable<NonEmptyResponse<SurveyResponse>> {
    return this.http
      .get<SurveyResponse>(`${environment.apiBaseUrl}/api/v1/surveys/${id}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  generateSurveyToken(
    request: SurveyTokenRequest,
  ): Observable<NonEmptyResponse<SurveyTokenResponse>> {
    return this.http
      .post<SurveyTokenResponse>(
        `${environment.apiBaseUrl}/api/v1/surveys/generate-token`,
        request,
        {
          observe: 'response',
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response.body);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  refreshSurveyToken(surveyId: number): Observable<NonEmptyResponse<SurveyResponse>> {
    return this.http
      .post<SurveyResponse>(
        `${environment.apiBaseUrl}/api/v1/surveys/${surveyId}/refresh-token`,
        {},
        {
          observe: 'response',
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response.body);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  createSurvey(request: CreateSurveyRequest): Observable<NonEmptyResponse<SurveyResponse>> {
    return this.http
      .post<SurveyResponse>(`${environment.apiBaseUrl}/api/v1/surveys`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response.body);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  updateSurvey(request: CreateSurveyRequest, surveyId: number): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/surveys/${surveyId}`, request, {
        observe: 'response',
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  getInfrastructureBySurveyId(
    surveyId: number,
  ): Observable<NonEmptyResponse<SurveyInfrastructureResponse>> {
    return this.http
      .get<SurveyInfrastructureResponse>(
        `${environment.apiBaseUrl}/api/v1/surveys/${surveyId}/infrastructure`,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getInfrastructureByEntityId(
    request: OrganizationStructureNamesRequest,
  ): Observable<NonEmptyResponse<OrganizationStructureNamesResponse>> {
    return this.http
      .post<OrganizationStructureNamesResponse>(
        `${environment.apiBaseUrl}/api/v1/organizations/names`,
        request,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  saveSurveyAnswer(request: StoreSurveyAnswerRequest): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/surveys/store-answers`, request, {
        headers: new HttpHeaders(),
        observe: 'response',
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  canSurveyBeEvaluated(token: string): Observable<NonEmptyResponse<SurveyUrlValidResponse>> {
    return this.http
      .get<SurveyUrlValidResponse>(`${environment.apiBaseUrl}/api/v1/surveys/${token}/is-valid`, {})
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  retrieveTagsLike(tag: string): Observable<NonEmptyResponse<AvailableTagsResponse>> {
    return this.http
      .get<AvailableTagsResponse>(`${environment.apiBaseUrl}/api/v1/organizations/tags/${tag}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getEvalInfoByCustomerServiceID(
    evaluationType: EvaluationType,
    customerServiceId: string,
  ): Observable<NonEmptyResponse<EvaluationGroupInformationResponse>> {
    return this.http
      .get<EvaluationGroupInformationResponse>(
        `${environment.apiBaseUrl}/api/v1/admin/${evaluationType}/info/${customerServiceId}`,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  associateEvaluationWithGroup(request: EvaluationGroupInformation): Observable<EmptyResponse> {
    return this.http
      .post(
        `${environment.apiBaseUrl}/api/v1/organizations/locations/groups/associate-evaluation`,
        request,
        {
          observe: 'response',
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  getOrganizationEvaluationDetail(
    evaluationId: string,
    evaluationType: EvaluationType,
  ): Observable<NonEmptyResponse<OrganizationEvaluationDetail>> {
    return this.http
      .get<OrganizationEvaluationDetail>(
        `${environment.apiBaseUrl}/api/v1/organizations/evaluation-detail/${evaluationId}`,
        {
          observe: 'body',
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
          params: {
            evaluationType,
          },
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getOrganizationReportData(
    request: OrganizationReportDataRequest,
  ): Observable<NonEmptyResponse<OrganizationReportDataResponse>> {
    return this.http
      .post<OrganizationReportDataResponse>(
        `${environment.apiBaseUrl}/api/v1/organizations/report`,
        request,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          const reportData = new OrganizationReportDataResponse();
          reportData.surveysAndResults = response.surveysAndResults;
          reportData.childEvaluations = response.childEvaluations;
          reportData.evaluation =
            response.evaluation !== null ? Evaluation.fromJson(response.evaluation) : null;
          reportData.surveysMinDate = response.surveysMinDate;
          reportData.surveysMaxDate = response.surveysMaxDate;
          return new NonEmptyResponse(true, '', reportData);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  generateReport(request: GenerateReportRequest, language: Languages): Observable<EmptyResponse> {
    return this.http
      .post(`${environment.apiBaseUrl}/api/v1/admin/generate-report`, request, {
        headers: new HttpHeaders()
          .append('x-auth-token', this.userService.authToken)
          .append('Accept-Language', language),
      })
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  getComparisonReportData(
    request: ComparisonReportDataRequest,
  ): Observable<NonEmptyResponse<ComparisonReportDataResponse>> {
    return this.http
      .post<ComparisonReportDataResponse>(
        `${environment.apiBaseUrl}/api/v1/organizations/comparison-report`,
        request,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getAvailableSPSSFilters(): Observable<NonEmptyResponse<FilterListResponse>> {
    return this.http
      .get(`${environment.apiBaseUrl}/api/v1/admin/export/filter-list`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response: FilterListResponse) => {
          return new NonEmptyResponse<FilterListResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  deleteSPSSFilter(queryId: string, exportType: DataExportType): Observable<EmptyResponse> {
    return this.http
      .delete(
        `${environment.apiBaseUrl}/api/v1/admin/export/filter-list/${exportType}/${queryId}`,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response: EmptyResponse) => {
          return new NonEmptyResponse<EmptyResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  deleteAllSPSSFilter(): Observable<EmptyResponse> {
    return this.http
      .delete(`${environment.apiBaseUrl}/api/v1/admin/export/filter-list`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response: EmptyResponse) => {
          return new NonEmptyResponse<EmptyResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  createSPSSFilter(
    request: DataExportFilterRequest,
  ): Observable<NonEmptyResponse<DataExportFilterResponse>> {
    return this.http
      .post<DataExportFilterResponse>(
        `${environment.apiBaseUrl}/api/v1/admin/export/generate`,
        request,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response: DataExportFilterResponse) => {
          return new NonEmptyResponse<DataExportFilterResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(
            new NonEmptyResponse(false, errorMessage, undefined, null, errorResponse.status),
          );
        }),
      );
  }

  getUserDataFromTrackingId(trackingId: string): Observable<NonEmptyResponse<UserResponse>> {
    return this.http
      .get(`${environment.apiBaseUrl}/api/v1/user-data/${trackingId}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response: UserResponse) => {
          return new NonEmptyResponse<UserResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getLegalTextList(
    type: LegalTextType,
  ): Observable<NonEmptyResponse<LegalTextVersionListResponse>> {
    return this.http
      .get(`${environment.apiBaseUrl}/api/v1/legal/${getUrlParamFromLegalTextType(type)}`, {
        headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
      })
      .pipe(
        map((response: LegalTextVersionListResponse) => {
          return new NonEmptyResponse<LegalTextVersionListResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  saveLegalText(
    request: LegalTextUpsertRequest,
    type: LegalTextType,
  ): Observable<NonEmptyResponse<UpsertLegalTextResponse>> {
    return this.http
      .post(
        `${environment.apiBaseUrl}/api/v1/legal/${getUrlParamFromLegalTextType(type)}`,
        request,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response: UpsertLegalTextResponse) => {
          return new NonEmptyResponse<UpsertLegalTextResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  getLegalText(
    versionId: string,
    type: LegalTextType,
  ): Observable<NonEmptyResponse<LegalTextVersionResponse>> {
    return this.http
      .get(
        `${environment.apiBaseUrl}/api/v1/legal/${getUrlParamFromLegalTextType(type)}/${versionId}`,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response: LegalTextVersionResponse) => {
          return new NonEmptyResponse<LegalTextVersionResponse>(true, '', response);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }

  publishLegalText(versionName: string, type: LegalTextType): Observable<EmptyResponse> {
    return this.http
      .post(
        `${environment.apiBaseUrl}/api/v1/legal/${getUrlParamFromLegalTextType(
          type,
        )}/${versionName}/publish`,
        {},
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResponse) => {
          const errorMessage = errorResponse.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }
}
