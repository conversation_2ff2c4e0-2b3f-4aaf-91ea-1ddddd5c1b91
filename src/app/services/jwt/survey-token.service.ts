import { Injectable } from '@angular/core';
import jwtDecode from 'jwt-decode';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class SurveyTokenService {
  private static decodeJwtToken(token: string): TokenPayload {
    return jwtDecode(token);
  }

  getSurveyTypeFromToken(token: string): SurveyType | null {
    const decoded = SurveyTokenService.decodeJwtToken(token);
    const type: number = decoded.type;

    switch (type) {
      case 0:
        return SurveyType.ORGANIZATION_SURVEY;
      case 1:
        return SurveyType.ADMINISTRATION_SURVEY;
      case 2:
        return SurveyType.PARENTAL_SURVEY;
      case 3:
        return SurveyType.STAFF_SURVEY;
      default:
        return null;
    }
  }

  getTokenIssuedAtDate(token: string): Date {
    const decoded = SurveyTokenService.decodeJwtToken(token);
    return new Date(decoded.iat * 1000);
  }

  getLinkToQuestionnaire(token: string): string {
    return `${environment.webBaseUrl}/surveys/${token}`;
  }
}

interface TokenPayload {
  type: number;
  iat: number;
}

export enum SurveyType {
  ORGANIZATION_SURVEY = 'organization',
  ADMINISTRATION_SURVEY = 'administration',
  PARENTAL_SURVEY = 'parental',
  STAFF_SURVEY = 'staff',
}
