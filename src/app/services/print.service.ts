import { Injectable } from '@angular/core';
import { VERSION_NUMBER } from 'src/version';

export enum PrintStyles {
  EVALUATOR_ANALYSIS = '/assets/print-styles/evaluator-analysis.print.scss',
  CHILD_EVALUATION_REPORT = '/assets/print-styles/children-evaluation-report.print.scss',
  EVALUATOR_ANALYSIS_TEST = '/assets/print-styles/evaluator-analysis.screen.scss',
  ORGANIZATION_AND_LOCATION_REPORT = '/assets/report/organization-report.pdf.scss',
  COMPARISON_REPORT = '/assets/report/organization-report.pdf.scss',
}

@Injectable({
  providedIn: 'root',
})
export class PrintService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  openBrowserPrintDialog(printContent: any, styleSheetPath: string): void {
    const WindowPrt = window.open(
      '',
      '',
      'left=0,top=0,width=900,height=900,toolbar=0,scrollbars=0,status=0',
    );

    WindowPrt.document.write('<html><head><title>GrazIAS</title>');

    WindowPrt.document.write('</head><body>');
    WindowPrt.document.write(printContent.innerHTML);
    WindowPrt.document.write('</body></html>');

    const linkPrint = WindowPrt.document.createElement('link');
    linkPrint.type = 'text/css';
    linkPrint.rel = 'stylesheet';
    linkPrint.href = `${styleSheetPath}?v=${VERSION_NUMBER}`;
    linkPrint.media = 'print';

    const linkScreen = WindowPrt.document.createElement('link');
    linkScreen.type = 'text/css';
    linkScreen.rel = 'stylesheet';
    linkScreen.href = `${PrintStyles.EVALUATOR_ANALYSIS_TEST}?v=${VERSION_NUMBER}`;
    linkScreen.media = 'screen';

    const head = WindowPrt.document.getElementsByTagName('head')[0];
    head.appendChild(linkPrint);
    head.appendChild(linkScreen);

    linkPrint.onload = () => {
      console.log('onload listener');
      const interval = setInterval(() => {
        if (document.readyState === 'complete') {
          WindowPrt.document.close();
          WindowPrt.focus();
          WindowPrt.print();
          clearInterval(interval);
        }
      }, 400);
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  loadServerPdfGenerationStyles(printContent: any, styleSheetPath: string): void {
    document.write('<html><head><title>GrazIAS</title>');

    document.write('</head><body>');
    document.write(printContent.innerHTML);
    document.write('</body></html>');

    const linkPrint = document.createElement('link');
    linkPrint.type = 'text/css';
    linkPrint.rel = 'stylesheet';
    linkPrint.href = `${styleSheetPath}?v=${VERSION_NUMBER}`;
    linkPrint.media = 'print';

    const linkScreen = document.createElement('link');
    linkScreen.type = 'text/css';
    linkScreen.rel = 'stylesheet';
    linkScreen.href = `${styleSheetPath}?v=${VERSION_NUMBER}`;
    linkScreen.media = 'screen';

    const head = document.getElementsByTagName('head')[0];
    head.appendChild(linkPrint);
    head.appendChild(linkScreen);

    linkPrint.onload = () => {
      console.log('onload listener');
      const interval = setInterval(() => {
        if (document.readyState === 'complete') {
          document.close();
          focus();
          print();
          clearInterval(interval);
        }
      }, 400);
    };
  }
}
