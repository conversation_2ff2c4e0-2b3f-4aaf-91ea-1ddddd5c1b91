import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { UserAdministrationOption, UserListItemViewModel } from 'src/app/utils/utils';

export class AdminCreateUserRouteState {
  constructor(
    public user: UserListItemViewModel,
    public mode: UserAdministrationOption,
  ) {}
}

@Injectable({ providedIn: 'root' })
export class RouteStateService {
  private adminPathParamState = new BehaviorSubject(
    new AdminCreateUserRouteState(undefined, undefined),
  );

  readonly adminState: Observable<AdminCreateUserRouteState>;

  constructor() {
    this.adminState = this.adminPathParamState.asObservable();
  }

  updateAdminState(newPathParam: AdminCreateUserRouteState): void {
    this.adminPathParamState.next(newPathParam);
  }

  getAdminState(): Observable<AdminCreateUserRouteState> {
    return this.adminState;
  }
}
