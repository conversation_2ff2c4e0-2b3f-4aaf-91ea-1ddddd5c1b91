import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NavController } from '@ionic/angular';
import { isEmpty } from 'lodash';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { StorageKeys } from 'src/app/model/StorageKeys';
import { environment } from 'src/environments/environment';
import { AdminRoutes } from '../../app-routes';
import { EmptyResponse } from '../api/response/empty-response';
import { NonEmptyResponse } from '../evaluation/evaluation.service';
import { ProjectResponse } from '../admin/admin.service';

export class LoginResult {
  constructor(
    public success: boolean,
    public invalidCredentials: boolean,
    public errorMessage: string | undefined,
  ) {}
}

export interface TotpResponse {
  totpState: TotpState;
  totpSetupQrCodeUrl?: string;
  totpSecret?: string;
}

export enum TotpState {
  SETUP_REQUIRED = 'SETUP_REQUIRED',
  CODE_REQUIRED = 'CODE_REQUIRED',
}

export class UserDataResponse {
  firstName: string;
  lastName: string;
  mail: string;
  trackingId: string;
  role: UserRole;
}

@Injectable({
  providedIn: 'root',
})
export class UserService {
  public authToken: string;
  public userRole: UserRole;

  constructor(
    private http: HttpClient,
    private keys: StorageKeys,
  ) {}

  init() {
    this.loadToken();
    this.loadUserRole();
  }

  isLoggedIn(): boolean {
    return !isEmpty(this.authToken);
  }

  isLoggedInUserAnAdmin(): boolean {
    if (this.isLoggedIn()) {
      return this.userRole === UserRole.ADMIN_USER;
    }
    return false;
  }

  requestTotpLogin(
    userLoginIdentifier: string,
    password: string,
  ): Observable<NonEmptyResponse<TotpResponse>> {
    return this.http
      .post<TotpResponse>(`${environment.apiBaseUrl}/api/v1/request-totp-login`, {
        userLoginIdentifier,
        password,
      })
      .pipe(
        map((response) => {
          return new NonEmptyResponse<TotpResponse>(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined, null, errorResp.status));
        }),
      );
  }

  totpLogin(
    userLoginIdentifier: string,
    password: string,
    totpCode: string,
  ): Observable<EmptyResponse> {
    return this.http
      .post<UserDataResponse>(
        `${environment.apiBaseUrl}/api/v1/totp-login`,
        { userLoginIdentifier, password, totpCode },
        { observe: 'response' },
      )
      .pipe(
        map((response) => {
          this.storeAuthToken(response.headers.get('x-auth-token'));
          this.storeUserRole(response.body.role);
          return new EmptyResponse(true, '', false, response.status);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(
            new EmptyResponse(false, errorMessage, errorResp.status == 400, errorResp.status),
          );
        }),
      );
  }

  logout(): Observable<EmptyResponse> {
    const logoutToken = this.authToken;
    this.authToken = null;
    this.userRole = null;
    localStorage.removeItem(this.keys.STORAGE_KEY_AUTH_TOKEN);
    localStorage.removeItem(this.keys.STORAGE_KEY_USER_ROLE);
    return this.http
      .post(
        `${environment.apiBaseUrl}/api/v1/logout`,
        {},
        {
          observe: 'response',
          headers: new HttpHeaders().append('x-auth-token', logoutToken),
        },
      )
      .pipe(
        map(() => {
          return new EmptyResponse(true, undefined);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new EmptyResponse(false, errorMessage));
        }),
      );
  }

  storeAuthToken(token: string): void {
    this.authToken = token;
    localStorage.setItem(this.keys.STORAGE_KEY_AUTH_TOKEN, token);
  }

  storeUserRole(role: UserRole) {
    this.userRole = role;
    localStorage.setItem(this.keys.STORAGE_KEY_USER_ROLE, role);
  }

  loadUserRole() {
    this.userRole = localStorage.getItem(this.keys.STORAGE_KEY_USER_ROLE) as UserRole;
  }

  loadToken() {
    this.authToken = localStorage.getItem(this.keys.STORAGE_KEY_AUTH_TOKEN);
  }
}

export enum UserRole {
  ADMIN_USER = 'ADMIN_USER',
}
