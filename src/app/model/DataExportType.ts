export enum DataExportType {
  EXTERNAL_EVALUATION_MATH = 'EXTERNAL_EVALUATION_MATH',
  EXTERNAL_EVLUATION_DIDACTICS = 'EXTERNAL_EVALUATION_DIDACTICS',
  EXTERNAL_EVLUATION_TEBI = 'EXTERNAL_EVALUATION_TEBI',
  EXTERNAL_EVALUATION_GRAZIAS_V2 = 'EXTERNAL_EVALUATION_GRAZIAS_V2',
  SELF_EVALUATION_GRAZIAS_V2 = 'SELF_EVALUATION_GRAZIAS_V2',
  CHILDREN_EVALUATION = 'CHILDREN_EVALUATION',
}

export const getDataExportTypeTranslationKey = (
  scaleType: DataExportType,
  shortVersion: boolean = false,
): string => {
  switch (scaleType.valueOf()) {
    case DataExportType.EXTERNAL_EVALUATION_GRAZIAS_V2.valueOf():
      return shortVersion
        ? 'global.label.specificScaleTitle.basic_v2'
        : 'spssFilter.specificScaleTitle.basic_v2';
    case DataExportType.EXTERNAL_EVLUATION_TEBI.valueOf():
      return shortVersion
        ? 'global.label.specificScaleTitle.nature'
        : 'spssFilter.specificScaleTitle.nature';
    case DataExportType.EXTERNAL_EVLUATION_DIDACTICS.valueOf():
      return shortVersion
        ? 'global.label.specificScaleTitle.didactics'
        : 'spssFilter.specificScaleTitle.didactics';
    case DataExportType.EXTERNAL_EVALUATION_MATH.valueOf():
      return shortVersion
        ? 'global.label.specificScaleTitle.math'
        : 'spssFilter.specificScaleTitle.math';
    case DataExportType.SELF_EVALUATION_GRAZIAS_V2.valueOf():
      return 'spssFilter.specificScaleTitle.self.basic_v2';
    case DataExportType.CHILDREN_EVALUATION.valueOf():
      return 'spssFilter.specificScaleTitle.child';
  }
};
