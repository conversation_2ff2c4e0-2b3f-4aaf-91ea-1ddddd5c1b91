<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'organization.title' | translate}}</b>
      </ion-row>
      <ion-title>{{ getPageTitle() }}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid class="indentation">
    <ion-row>
      <ion-col>
        <app-evaluation-detail-info-box
          [evaluationId]="evaluationId"
          [evaluationType]="evaluationType"
        >
        </app-evaluation-detail-info-box>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
