import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { ActivatedRoute } from '@angular/router';
import { EvaluationType } from '../organizations/utils';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-evaluation-detail',
  templateUrl: './evaluation-detail.page.html',
  styleUrls: ['./evaluation-detail.page.scss'],
})
export class EvaluationDetailPage implements OnInit {
  public evaluationId: string;
  public evaluationType: EvaluationType;

  private pageBeforeNavigation: number = 1;

  constructor(
    private navController: NavController,
    private route: ActivatedRoute,
    private translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.evaluationId = params.evalId;
      this.evaluationType = params.evaluationType;
    });

    this.route.queryParams.subscribe(params => {
      this.pageBeforeNavigation = +params['page'];
    });
  }

  getPageTitle(): string {
    switch (this.evaluationType) {
      case EvaluationType.CHILD_EVALUATION:
        return this.translate.instant('organiziation.finished-child-evaluation.title');
      case EvaluationType.EVALUATION:
        return this.translate.instant('organiziation.finished-evaluation.title');
    }
  }

  navigateBack(): void {
    this.navController.navigateRoot(`admin/organizations?initialPage=${this.pageBeforeNavigation}`);
  }
}
