import { AbstractControl } from '@angular/forms';

export class FormValidationHelper {
  static handleValidationError(control: AbstractControl): string | null {
    if (control.hasError('required')) {
      return 'global.error.required';
    }
    if (control.hasError('email')) {
      return 'global.error.email';
    }
    if (control.hasError('maxlength')) {
      return 'global.error.textTooLarge';
    }
    return null;
  }
}
