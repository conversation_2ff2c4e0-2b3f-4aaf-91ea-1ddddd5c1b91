import { Injectable } from '@angular/core';
import { OrganizationNodeType } from './organizations/utils';
import { SurveyState } from '../../services/admin/admin.service';
import { ComparisonSelection } from './organizations/comparison-toggle/comparison-toggle.component';
import {
  ScaleAgeDefinition,
  ScaleDefinition,
} from '../../services/evaluation/models/scale-definition';
import { ScaleTypeDefinition } from '../../services/evaluation/models/scale-type-definition';

@Injectable({
  providedIn: 'root',
})
export class OrganizationRepository {
  public newlyCreatedNode: OrganizationNode;
}

export interface OrganizationNode {
  name: string;
  id: number;
  tags: Array<string>;
  detail?: string;
  orgId?: number;
  locId?: number;
  groupId?: number;
  type: OrganizationNodeType;
  children?: OrganizationNode[];
  surveyToken?: string;
  surveyState?: SurveyState;
  evaluationId?: string;
  reportSelectionActive?: boolean;
  isSelected?: boolean;
  comparisonReportSelectionActive?: boolean;
  comparisonReportSelection?: ComparisonSelection;
  evaluatedScales?: Array<ScaleDefinition>;
  scales?: Array<ScaleTypeDefinition>;
  scaleAgeDefinition?: ScaleAgeDefinition;
  validFrom?: Date;
  validUntil?: Date;
}

export enum SurveyUrlParam {
  organization = 'organization-survey',
  administration = 'administration-survey',
  parental = 'parental-survey',
  staff = 'staff-survey',
}
