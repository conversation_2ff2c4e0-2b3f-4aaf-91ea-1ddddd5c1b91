import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { IsoDatePipe } from 'src/app/pipes/iso-date.pipe';
import {
  AdminService,
  CreateLocationRequest,
  LocationDo,
  OrganizationDo,
} from '../../../services/admin/admin.service';
import { Countries } from '../../../utils/countries';
import { Utils } from '../../../utils/utils';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import { FormValidationHelper } from '../helpers/form-validation-helper';
import { OrganizationNode, OrganizationRepository } from '../organizations.repository';
import { OrganizationNodeType } from '../organizations/utils';

export enum UpsertStrategy {
  CREATE,
  UPDATE,
}

export enum LoadingUiEntry {
  PARENT_DATA,
  FORM_DATA,
}

@Component({
  selector: 'app-upsert-location-form',
  templateUrl: './upsert-location-form.page.html',
  styleUrls: ['../upsert-organization-form/upsert-organization-form.page.scss'],
})
export class UpsertLocationFormPage implements OnInit, AfterViewInit {
  constructor(
    public translate: TranslateService,
    private formBuilder: UntypedFormBuilder,
    private adminService: AdminService,
    private navController: NavController,
    private route: ActivatedRoute,
    private toastController: ToastController,
    private loadingController: LoadingController,
    private organizationRepo: OrganizationRepository,
  ) {}

  private upsertStrategy: UpsertStrategy;

  public orgId: number;
  public locationId?: number;

  public countries = Countries.getLocationFormCountries();
  public countriesAlertOptions = {
    header: this.translate.instant('organization.upsert_location.choose_country'),
  };

  public locationNameErrorMessage?: string;
  public streetErrorMessage?: string;
  public streetNumberErrorMessage?: string;
  public zipCodeErrorMessage?: string;
  public cityErrorMessage?: string;
  public countryErrorMessage?: string;
  public primaryLocationManagementFirstNameErrorMessage?: string;
  public primaryLocationManagementLastNameErrorMessage?: string;
  public primaryLocationManagementMailErrorMessage?: string;
  public secondaryLocationManagementFirstNameErrorMessage?: string;
  public secondaryLocationManagementLastNameErrorMessage?: string;
  public secondaryLocationManagementMailErrorMessage?: string;
  public locationPhoneErrorMessage?: string;
  public numberOfEmployedEducatorsErrorMessage?: string;
  public locationDescriptionErrorMessage?: string;
  public tags: Array<string> = [];
  public notesErrorMessage?: string;

  public formDataLoadingUi: HTMLIonLoadingElement = null;
  public parentDataLoadingUi: HTMLIonLoadingElement = null;

  public orgName = '-';
  public orgContactPerson = '-';
  public locCreatedAt = '-';
  public buttonType = PrimaryButtonType;

  private pageBeforeNavigation: number = 1;

  @ViewChild('content') content: any;

  public upsertLocationForm = this.formBuilder.group({
    locationName: ['', [Validators.required]],
    street: ['', [Validators.required]],
    streetNumber: ['', [Validators.required]],
    zipCode: ['', [Validators.required]],
    city: ['', [Validators.required]],
    country: ['', [Validators.required]],
    primaryLocationManagementFirstName: [''],
    primaryLocationManagementLastName: [''],
    primaryLocationManagementMail: ['', [Validators.email]],
    secondaryLocationManagementFirstName: [''],
    secondaryLocationManagementLastName: [''],
    secondaryLocationManagementMail: ['', [Validators.email]],
    locationPhone: [''],
    numberOfEmployedEducators: [''],
    locationDescription: [''],
    notes: [''],
  });

  ngAfterViewInit(): void {
    this.content.scrollToTop();
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.orgId = +params.orgId; // (+) converts string 'id' to a number
      this.locationId = +params.locationId;
      if (this.locationId !== -1) {
        this.upsertStrategy = UpsertStrategy.UPDATE;
        this.fetchAndPrefillFormData();
      } else {
        this.upsertStrategy = UpsertStrategy.CREATE;
      }
    });
    this.fetchOrgData();

    this.route.queryParams.subscribe(params => {
      this.pageBeforeNavigation = +params['page'];
    });
  }

  private async fetchAndPrefillFormData(): Promise<void> {
    await this.showLoadingUi(LoadingUiEntry.FORM_DATA);
    this.adminService.getLocationById(this.locationId).subscribe(async (resp) => {
      if (resp.success) {
        this.prefillLocationData(resp.data.location);
      } else {
        this.showErrorMessage(true);
      }
      await this.hideLoadingUi(LoadingUiEntry.FORM_DATA);
    });
  }

  private async fetchOrgData(): Promise<void> {
    await this.showLoadingUi(LoadingUiEntry.PARENT_DATA);
    this.adminService.getOrganizationById(this.orgId).subscribe(async (resp) => {
      if (resp.success) {
        this.setOrganizationData(resp.data.result);
      } else {
        this.showErrorMessage(true);
      }
      await this.hideLoadingUi(LoadingUiEntry.PARENT_DATA);
    });
  }

  private prefillLocationData(location: LocationDo): void {
    this.organizationRepo.newlyCreatedNode = {
      name: location.locationName,
      id: location.id,
      tags: location.tags,
      orgId: this.orgId,
      locId: location.id,
      groupId: -1,
      type: OrganizationNodeType.LOCATION,
      level: 1,
      children: [],
    } as OrganizationNode;
    this.upsertLocationForm.controls.locationName.setValue(location.locationName);
    this.upsertLocationForm.controls.street.setValue(location.street);
    this.upsertLocationForm.controls.streetNumber.setValue(location.streetNumber);
    this.upsertLocationForm.controls.zipCode.setValue(location.zipCode);
    this.upsertLocationForm.controls.city.setValue(location.city);
    this.upsertLocationForm.controls.country.setValue(location.country);
    this.upsertLocationForm.controls.primaryLocationManagementFirstName.setValue(
      location.primaryLocationManagementFirstName,
    );
    this.upsertLocationForm.controls.primaryLocationManagementLastName.setValue(
      location.primaryLocationManagementLastName,
    );
    this.upsertLocationForm.controls.primaryLocationManagementMail.setValue(
      location.primaryLocationManagementMail,
    );
    this.upsertLocationForm.controls.secondaryLocationManagementFirstName.setValue(
      location.secondaryLocationManagementFirstName,
    );
    this.upsertLocationForm.controls.secondaryLocationManagementLastName.setValue(
      location.secondaryLocationManagementLastName,
    );
    this.upsertLocationForm.controls.secondaryLocationManagementMail.setValue(
      location.secondaryLocationManagementMail,
    );
    this.upsertLocationForm.controls.locationPhone.setValue(location.locationPhone);
    this.upsertLocationForm.controls.numberOfEmployedEducators.setValue(
      location.numberOfEmployedEducators,
    );
    this.upsertLocationForm.controls.locationDescription.setValue(location.locationDescription);
    this.upsertLocationForm.controls.notes.setValue(location.notes);
    if (typeof location.tags !== 'undefined' && location.tags.length > 0) {
      this.tags = location.tags;
    }

    this.locCreatedAt = new IsoDatePipe(this.translate.currentLang).transform(location.createdAt);
  }

  private setOrganizationData(organization: OrganizationDo): void {
    this.orgName = organization.organizationName;
    this.orgContactPerson = organization.initialContactName;
  }

  private createLocation(request: CreateLocationRequest): void {
    this.adminService.createLocation(request).subscribe(async (result) => {
      if (result.success) {
        this.organizationRepo.newlyCreatedNode = {
          name: result.data.location.locationName,
          id: result.data.location.id,
          tags: result.data.location.tags,
          orgId: this.orgId,
          locId: result.data.location.id,
          groupId: -1,
          type: OrganizationNodeType.LOCATION,
          level: 1,
          children: [],
        } as OrganizationNode;
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }

  private updateLocation(request: CreateLocationRequest): void {
    this.adminService.updateLocation(request, this.locationId).subscribe(async (result) => {
      if (result.success) {
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }

  private async createLoadingUi(): Promise<HTMLIonLoadingElement> {
    return await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
  }

  async submitForm(): Promise<void> {
    if (this.upsertLocationForm.valid) {
      const request = new CreateLocationRequest();
      request.organizationId = this.orgId;
      request.locationName = this.upsertLocationForm.value.locationName;
      request.street = this.upsertLocationForm.value.street;
      request.streetNumber = this.upsertLocationForm.value.streetNumber;
      request.zipCode = this.upsertLocationForm.value.zipCode;
      request.city = this.upsertLocationForm.value.city;
      request.country = this.upsertLocationForm.value.country;
      request.primaryLocationManagementFirstName =
        this.upsertLocationForm.value.primaryLocationManagementFirstName;
      request.primaryLocationManagementLastName =
        this.upsertLocationForm.value.primaryLocationManagementLastName;
      request.primaryLocationManagementMail =
        this.upsertLocationForm.value.primaryLocationManagementMail;
      request.secondaryLocationManagementFirstName =
        this.upsertLocationForm.value.secondaryLocationManagementFirstName;
      request.secondaryLocationManagementLastName =
        this.upsertLocationForm.value.secondaryLocationManagementLastName;
      request.secondaryLocationManagementMail =
        this.upsertLocationForm.value.secondaryLocationManagementMail;
      request.locationPhone = this.upsertLocationForm.value.locationPhone;
      request.numberOfEmployedEducators = this.upsertLocationForm.value.numberOfEmployedEducators;
      request.locationDescription = this.upsertLocationForm.value.locationDescription;
      request.tags = this.tags;
      request.notes = this.upsertLocationForm.value.notes;

      this.upsertStrategy === UpsertStrategy.UPDATE
        ? this.updateLocation(request)
        : this.createLocation(request);
    } else {
      this.validateForm();
    }
  }

  validateForm(): void {
    this.locationNameErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.locationName,
    );
    this.streetErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.street,
    );
    this.streetNumberErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.streetNumber,
    );
    this.zipCodeErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.zipCode,
    );
    this.cityErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.city,
    );
    this.countryErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.country,
    );
    this.primaryLocationManagementFirstNameErrorMessage =
      FormValidationHelper.handleValidationError(
        this.upsertLocationForm.controls.primaryLocationManagementFirstName,
      );
    this.primaryLocationManagementLastNameErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.primaryLocationManagementLastName,
    );
    this.primaryLocationManagementMailErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.primaryLocationManagementMail,
    );
    this.secondaryLocationManagementFirstNameErrorMessage =
      FormValidationHelper.handleValidationError(
        this.upsertLocationForm.controls.secondaryLocationManagementFirstName,
      );
    this.secondaryLocationManagementLastNameErrorMessage =
      FormValidationHelper.handleValidationError(
        this.upsertLocationForm.controls.secondaryLocationManagementLastName,
      );
    this.secondaryLocationManagementMailErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.secondaryLocationManagementMail,
    );
    this.locationPhoneErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.locationPhone,
    );
    this.numberOfEmployedEducatorsErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.numberOfEmployedEducators,
    );
    this.locationDescriptionErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.locationDescription,
    );
    this.notesErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertLocationForm.controls.notes,
    );
  }

  navigateBack(): void {
    this.navController.navigateRoot(`admin/organizations?initialPage=${this.pageBeforeNavigation}`);
  }

  getSubmitButtonLabel(): string {
    return this.upsertStrategy === UpsertStrategy.UPDATE
      ? this.translate.instant('global.save')
      : this.translate.instant('global.create');
  }

  getToolbarTitle(): string {
    return this.upsertStrategy === UpsertStrategy.UPDATE
      ? this.translate.instant('organization.update-location')
      : this.translate.instant('organization.new-location');
  }

  onTagsChanged(tags: string[]): void {
    this.tags = tags;
  }

  async showErrorMessage(shouldReturn: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('global.error.generic'),
      color: 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }

  async showLoadingUi(loadingUiEntity: LoadingUiEntry): Promise<void> {
    switch (loadingUiEntity) {
      case LoadingUiEntry.PARENT_DATA:
        this.parentDataLoadingUi = await this.createLoadingUi();
        await this.parentDataLoadingUi.present();
        break;
      case LoadingUiEntry.FORM_DATA:
        this.formDataLoadingUi = await this.createLoadingUi();
        await this.formDataLoadingUi.present();
        break;
    }
  }

  async hideLoadingUi(loadingUiEntity: LoadingUiEntry): Promise<void> {
    switch (loadingUiEntity) {
      case LoadingUiEntry.PARENT_DATA:
        await this.parentDataLoadingUi.dismiss();
        break;
      case LoadingUiEntry.FORM_DATA:
        await this.formDataLoadingUi.dismiss();
        break;
    }
  }
}
