<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'organization.title' | translate}}</b>
      </ion-row>
      <ion-title>{{getToolbarTitle()}}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content #content class="design-v2 content">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col>
        <form [formGroup]="upsertLocationForm" (ngSubmit)="submitForm()" class="input-container">
          <ion-grid class="form-spacing">
            <app-upsert-form-info-box
              [parentTitle]="'organization.upsert-organization.title' | translate"
              [parentName]="orgName"
              [sectionTitle1]="'organization.upsert-form-info-box.contact_person' | translate"
              [sectionValue1]="orgContactPerson"
              [sectionTitle2]="'organization.upsert-form-info-box.location_created_at' | translate"
              [sectionValue2]="locCreatedAt"
            >
            </app-upsert-form-info-box>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="locationName"
                  type="text"
                  [label]="'organization.upsert_location.location_name' | translate"
                  [setFocus]="true"
                  [inputValue]="upsertLocationForm.controls.locationName.value"
                  [errorMessage]="locationNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="street"
                  type="text"
                  [label]="'organization.upsert_location.street' | translate"
                  [inputValue]="upsertLocationForm.controls.street.value"
                  [errorMessage]="streetErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="streetNumber"
                  type="text"
                  [label]="'organization.upsert_location.street_number' | translate"
                  [inputValue]="upsertLocationForm.controls.streetNumber.value"
                  [errorMessage]="streetNumberErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="zipCode"
                  type="text"
                  [label]="'organization.upsert_location.zip_code' | translate"
                  [inputValue]="upsertLocationForm.controls.zipCode.value"
                  [errorMessage]="zipCodeErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="city"
                  type="text"
                  [label]="'organization.upsert_location.city' | translate"
                  [inputValue]="upsertLocationForm.controls.city.value"
                  [errorMessage]="cityErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <app-dropdown-with-heading
                  formControlName="country"
                  [label]="'organization.upsert_location.country' | translate"
                  [inputValue]="upsertLocationForm.controls.country.value"
                  [errorMessage]="countryErrorMessage"
                  [values]="countries"
                  [interfaceOptions]="countriesAlertOptions"
                >
                </app-dropdown-with-heading>
              </ion-col>
              <ion-col></ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-tagging [tags]="tags" (tagsChanged)="onTagsChanged($event)"></app-tagging>
              </ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col>
                <app-textarea-with-heading
                  formControlName="notes"
                  [label]="'organization.upsert_location.notes' | translate"
                  [inputValue]="upsertLocationForm.controls.notes.value"
                  [errorMessage]="notesErrorMessage"
                  rows="7"
                >
                </app-textarea-with-heading>
              </ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col>
                <ion-text class="section-title">
                  {{'organization.upsert_location.primary_location_management_title' | translate}}
                </ion-text>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="primaryLocationManagementFirstName"
                  type="text"
                  [label]="'organization.upsert_location.primary_location_management_first_name' | translate"
                  [inputValue]="upsertLocationForm.controls.primaryLocationManagementFirstName.value"
                  [errorMessage]="primaryLocationManagementFirstNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="primaryLocationManagementLastName"
                  type="text"
                  [label]="'organization.upsert_location.primary_location_management_last_name' | translate"
                  [inputValue]="upsertLocationForm.controls.primaryLocationManagementLastName.value"
                  [errorMessage]="primaryLocationManagementLastNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="primaryLocationManagementMail"
                  type="text"
                  [label]="'organization.upsert_location.primary_location_management_mail' | translate"
                  [inputValue]="upsertLocationForm.controls.primaryLocationManagementMail.value"
                  [errorMessage]="primaryLocationManagementMailErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col></ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col>
                <ion-text class="section-title">
                  {{'organization.upsert_location.secondary_location_management_title' | translate}}
                </ion-text>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="secondaryLocationManagementFirstName"
                  type="text"
                  [label]="'organization.upsert_location.secondary_location_management_first_name' | translate"
                  [inputValue]="upsertLocationForm.controls.secondaryLocationManagementFirstName.value"
                  [errorMessage]="secondaryLocationManagementFirstNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="secondaryLocationManagementLastName"
                  type="text"
                  [label]="'organization.upsert_location.secondary_location_management_last_name' | translate"
                  [inputValue]="upsertLocationForm.controls.secondaryLocationManagementLastName.value"
                  [errorMessage]="secondaryLocationManagementLastNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="secondaryLocationManagementMail"
                  type="text"
                  [label]="'organization.upsert_location.secondary_location_management_mail' | translate"
                  [inputValue]="upsertLocationForm.controls.secondaryLocationManagementMail.value"
                  [errorMessage]="secondaryLocationManagementMailErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col></ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="locationPhone"
                  type="text"
                  [label]="'organization.upsert_location.phone' | translate"
                  [inputValue]="upsertLocationForm.controls.locationPhone.value"
                  [errorMessage]="locationPhoneErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="numberOfEmployedEducators"
                  type="text"
                  [label]="'organization.upsert_location.number_of_employed_educators' | translate"
                  [inputValue]="upsertLocationForm.controls.numberOfEmployedEducators.value"
                  [errorMessage]="numberOfEmployedEducatorsErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col>
                <app-textarea-with-heading
                  formControlName="locationDescription"
                  [label]="'organization.upsert_location.location_description' | translate"
                  [inputValue]="upsertLocationForm.controls.locationDescription.value"
                  [errorMessage]="locationDescriptionErrorMessage"
                  rows="14"
                >
                </app-textarea-with-heading>
              </ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col size="6"></ion-col>
              <ion-col size="3">
                <app-primary-button
                  expand="block"
                  (onClick)="navigateBack()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'global.button.cancel' | translate"
                >
                </app-primary-button>
              </ion-col>
              <ion-col size="3">
                <app-primary-button
                  type="submit"
                  expand="block"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [label]="getSubmitButtonLabel()"
                  [buttonType]="buttonType.GRADIENT"
                >
                </app-primary-button>
              </ion-col>
            </ion-row>
            <div class="bottom-safe-area"></div>
          </ion-grid>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
