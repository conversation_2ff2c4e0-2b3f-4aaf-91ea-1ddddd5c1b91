import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import {
  AdminService,
  CreateOrganizationRequest,
  OrganizationDo,
} from '../../../services/admin/admin.service';
import { Utils } from '../../../utils/utils';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import { FormValidationHelper } from '../helpers/form-validation-helper';
import { OrganizationNode, OrganizationRepository } from '../organizations.repository';
import { OrganizationNodeType } from '../organizations/utils';
import { UpsertStrategy } from '../upsert-location-form/upsert-location-form.page';

@Component({
  selector: 'app-upsert-organization-form',
  templateUrl: './upsert-organization-form.page.html',
  styleUrls: ['./upsert-organization-form.page.scss'],
})
export class UpsertOrganizationFormPage implements OnInit, AfterViewInit {
  constructor(
    public translate: TranslateService,
    private formBuilder: UntypedFormBuilder,
    private adminService: AdminService,
    private navController: NavController,
    private route: ActivatedRoute,
    private toastController: ToastController,
    private loadingController: LoadingController,
    private organizationRepo: OrganizationRepository,
  ) {}

  public orgId: number;

  public organizationNameErrorMessage?: string;
  public initialContactNameErrorMessage?: string;
  public initialContactMailErrorMessage?: string;
  public initialContactPhoneErrorMessage?: string;
  public notesErrorMessage?: string;
  public buttonType = PrimaryButtonType;
  public tags: string[] = [];

  private upsertStrategy: UpsertStrategy;
  private loadingUi: HTMLIonLoadingElement = null;

  private pageBeforeNavigation: number = 1;

  @ViewChild('content') content: any;

  public upsertOrganizationForm = this.formBuilder.group({
    organizationName: ['', [Validators.required]],
    initialContactName: ['', [Validators.required]],
    initialContactMail: ['', [Validators.required, Validators.email]],
    initialContactPhone: [''],
    notes: [''],
  });

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.orgId = +params.orgId; // (+) converts string 'id' to a number
      if (this.orgId !== -1) {
        this.upsertStrategy = UpsertStrategy.UPDATE;
        this.fetchAndPrefillFormData();

        this.route.queryParams.subscribe(params => {
          this.pageBeforeNavigation = +params['page'];
        });
      } else {
        this.upsertStrategy = UpsertStrategy.CREATE;
      }
    });
  }

  ngAfterViewInit(): void {
    this.content.scrollToTop();
  }

  private async fetchAndPrefillFormData(): Promise<void> {
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getOrganizationById(this.orgId).subscribe(async (resp) => {
      if (resp.success) {
        this.prefillLocationData(resp.data.result);
      } else {
        await this.showErrorMessage(true);
      }
      await this.loadingUi.dismiss();
    });
  }

  private prefillLocationData(organization: OrganizationDo): void {
    this.upsertOrganizationForm.controls.organizationName.setValue(organization.organizationName);
    this.upsertOrganizationForm.controls.initialContactName.setValue(
      organization.initialContactName,
    );
    this.upsertOrganizationForm.controls.initialContactMail.setValue(
      organization.initialContactMail,
    );
    this.upsertOrganizationForm.controls.initialContactPhone.setValue(
      organization.initialContactPhone,
    );
    this.upsertOrganizationForm.controls.notes.setValue(organization.notes);
    if (typeof organization.tags !== 'undefined' && organization.tags.length > 0) {
      this.tags = organization.tags;
    }
    this.organizationRepo.newlyCreatedNode = <OrganizationNode>{
      name: organization.organizationName,
      id: organization.id,
      tags: organization.tags,
      orgId: organization.id,
      locId: -1,
      groupId: -1,
      type: OrganizationNodeType.ORGANIZATION,
      level: 0,
      children: [],
    };
  }

  async submitForm(): Promise<void> {
    if (this.upsertOrganizationForm.valid) {
      const request = new CreateOrganizationRequest();
      request.organizationName = this.upsertOrganizationForm.value.organizationName;
      request.initialContactName = this.upsertOrganizationForm.value.initialContactName;
      request.initialContactMail = this.upsertOrganizationForm.value.initialContactMail;
      request.initialContactPhone = this.upsertOrganizationForm.value.initialContactPhone;
      request.tags = this.tags;
      request.notes = this.upsertOrganizationForm.value.notes;

      this.upsertStrategy === UpsertStrategy.CREATE
        ? this.createOrganization(request)
        : this.updateOrganization(request);
    } else {
      this.validateForm();
    }
  }

  private createOrganization(request: CreateOrganizationRequest): void {
    this.adminService.createOrganization(request).subscribe(async (result) => {
      if (result.success) {
        this.organizationRepo.newlyCreatedNode = {
          name: result.data.result.organizationName,
          id: result.data.result.id,
          tags: result.data.result.tags,
          orgId: result.data.result.id,
          locId: -1,
          groupId: -1,
          type: OrganizationNodeType.ORGANIZATION,
          level: 0,
          children: [],
        } as OrganizationNode;
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }

  private updateOrganization(request: CreateOrganizationRequest): void {
    this.adminService.updateOrganization(request, this.orgId).subscribe(async (result) => {
      if (result.success) {
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }

  validateForm(): void {
    this.organizationNameErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertOrganizationForm.controls.organizationName,
    );
    this.initialContactNameErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertOrganizationForm.controls.initialContactName,
    );
    this.initialContactMailErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertOrganizationForm.controls.initialContactMail,
    );
    this.initialContactPhoneErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertOrganizationForm.controls.initialContactPhone,
    );
    this.notesErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertOrganizationForm.controls.notes,
    );
  }

  navigateBack(): void {
    this.navController.navigateRoot(`admin/organizations?initialPage=${this.pageBeforeNavigation}`);
  }

  getSubmitButtonLabel(): string {
    return this.upsertStrategy === UpsertStrategy.UPDATE
      ? this.translate.instant('global.save')
      : this.translate.instant('global.create');
  }

  getToolbarTitle(): string {
    return this.upsertStrategy === UpsertStrategy.UPDATE
      ? this.translate.instant('organization.update-organization')
      : this.translate.instant('organization.new-organization');
  }

  onTagsChanged(tags: string[]): void {
    this.tags = tags;
  }

  async showErrorMessage(shouldReturn: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('global.error.generic'),
      color: 'danger',
      position: 'bottom',
      duration: 10000,
      cssClass: 'global-toast',
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }
}
