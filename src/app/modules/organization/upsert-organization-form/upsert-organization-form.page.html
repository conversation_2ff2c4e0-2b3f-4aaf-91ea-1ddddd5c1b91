<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'organization.title' | translate}}</b>
      </ion-row>
      <ion-title>{{getToolbarTitle()}}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content #content class="design-v2 content">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col>
        <form [formGroup]="upsertOrganizationForm" (ngSubmit)="submitForm()">
          <ion-grid class="form-spacing">
            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="organizationName"
                  type="text"
                  [label]="'organization.upsert-organization.placeholder.org-name' | translate"
                  [setFocus]="true"
                  [inputValue]="upsertOrganizationForm.controls.organizationName.value"
                  [errorMessage]="organizationNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="initialContactName"
                  type="text"
                  [label]="'organization.upsert-organization.placeholder.initial-contact-name' | translate"
                  [inputValue]="upsertOrganizationForm.controls.initialContactName.value"
                  [errorMessage]="initialContactNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="initialContactMail"
                  type="text"
                  [label]="'organization.upsert-organization.placeholder.initial-contact-mail' | translate"
                  [inputValue]="upsertOrganizationForm.controls.initialContactMail.value"
                  [errorMessage]="initialContactMailErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="initialContactPhone"
                  type="text"
                  [label]="'organization.upsert-organization.placeholder.initial-contact-phone' | translate"
                  [inputValue]="upsertOrganizationForm.controls.initialContactPhone.value"
                  [errorMessage]="initialContactPhoneErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-tagging [tags]="tags" (tagsChanged)="onTagsChanged($event)"></app-tagging>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-textarea-with-heading
                  formControlName="notes"
                  [label]="'organization.upsert-organization.placeholder.notes' | translate"
                  [inputValue]="upsertOrganizationForm.controls.notes.value"
                  [errorMessage]="notesErrorMessage"
                  rows="7"
                >
                </app-textarea-with-heading>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col size="6"></ion-col>
              <ion-col size="3">
                <app-primary-button
                  expand="block"
                  (onClick)="navigateBack()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'global.button.cancel' | translate"
                >
                </app-primary-button>
              </ion-col>
              <ion-col size="3">
                <app-primary-button
                  type="submit"
                  expand="block"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [label]="getSubmitButtonLabel()"
                  [buttonType]="buttonType.GRADIENT"
                >
                </app-primary-button>
              </ion-col>
            </ion-row>
            <div class="bottom-safe-area"></div>
          </ion-grid>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
