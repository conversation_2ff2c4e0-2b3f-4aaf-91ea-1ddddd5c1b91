@import 'src/theme/_variables_v2.scss';

.flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
}

.main-content {
  height: 100%;

  mat-tree-node {
    min-height: unset;
  }

  .toggle-icon {
    width: 12px;
    height: 12px;
    margin-left: 20px;
    margin-right: 16px;
    cursor: pointer;
  }

  .copy-icon {
    display: flex;
    align-items: center;
    position: absolute;
    right: 16px;
    top: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .custom-badge {
    margin-right: 10px;
    border-radius: 6px;
    --background: var(--ion-color-primary-V2);
    --padding-start: 7px;
    --padding-end: 8px;

    &.flex {
      display: flex;
      align-items: center;
      position: absolute;
      right: 36px;
      top: 10px;
      height: 20px;
    }
  }

  .tree-node {
    border: solid $color-light-gray 1px;
    border-radius: 8px;
    display: flex;
    margin-top: 8px;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-right: 16px;
  }

  .tree-node-child {
    display: flex;
  }

  .example-tree-invisible {
    display: none;
  }

  .dropdown {
    display: block;
  }

  .list-item {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;
    position: relative;
  }

  .flex-container {
    display: flex;
    align-items: center;
    position: absolute;
    right: 16px;
  }

  .column {
    padding-right: 0;
    overflow: hidden;

    .image-text-flex {
      display: flex;
      align-items: center;

      .check-box {
        margin-left: 20px !important;
        --background: white;
        --background-checked: var(--ion-color-primary-V2);
        --border-color: var(--ion-color-primary-V2);
        --border-color-checked: var(--ion-color-primary-V2);
        --border-width: 1.9px;
      }

      .list-icon {
        margin-left: 18px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .tree-indent {
    padding-left: 24px;
  }

  .empty-view {
    padding-left: 52px;
    padding-top: 8px;
    padding-bottom: 10px;
    color: $color-empty-text;
  }

  .tree-padding {
    padding-left: 94px;
    padding-right: 79px;
    padding-bottom: 20px;
  }

  .searchbar-container {
    padding: 20px 79px 40px 94px;
  }

  ::ng-deep ion-segment {
    margin-top: 24px;
    --background: none;
    --background-checked: none;
    --background-hover: none;
    --background-activated: none;
    --color: var(--ion-color-primary-V2);
    --color-checked: var(--ion-color-primary-V2);
    --color-checked-disabled: var(--color-checked);
    --indicator-color: transparent;
    justify-content: flex-start;

    ion-segment-button {
      margin-right: 32px;
      flex: unset;
      --padding-start: 0;
      --padding-end: 0;
      min-width: unset;
      text-transform: unset;
      font-size: 16px;
      color: $color-primary-v2;
      border: none;
    }

    .segment-button-checked {
      font-weight: 800;
    }
  }

  .child-evaluation {
    margin-left: 10px;
    border-radius: 6px;
    margin-top: 10px;
    background-color: var(--ion-color-child-evaluation);
  }

  .evaluation {
    margin-left: 10px;
    border-radius: 6px;
    margin-top: 10px;
    background-color: var(--ion-color-grazias-evaluation);
  }

  .survey {
    margin-left: 10px;
    border-radius: 6px;
    margin-bottom: 2px;
    margin-top: 12px;

    &.organization-survey {
      background-color: var(--ion-color-organization-survey);

      &.survey-state-open {
        background: var(--ion-background-organization-survey-open-state);
      }
    }

    &.administration-survey {
      background-color: var(--ion-color-administration-survey);

      &.survey-state-open {
        background: var(--ion-background-administration-survey-open-state);
      }
    }

    &.parental-survey {
      background-color: var(--ion-color-parental-survey);

      &.survey-state-open {
        background: var(--ion-background-parental-survey-open-state);
      }
    }

    &.staff-survey {
      background-color: var(--ion-color-staff-survey);

      &.survey-state-open {
        background: var(--ion-background-staff-survey-open-state);
      }
    }
  }

  .ripple-parent {
    overflow: hidden;
  }
}

.content {
  height: calc(100vh - 128px);
}

.title {
  margin-left: 4px;
  font-size: 16px;
  width: 100%;

  &:hover {
    text-decoration: underline;
    cursor: pointer;
  }
}

.title-survey {
  font-size: 16px;
  margin-left: 18px;
  width: 100%;

  &:hover {
    text-decoration: none;
    cursor: pointer;

    .main-node-name {
      text-decoration: underline;
    }
  }

  .node-detail {
    font-weight: 400;

    .display-none {
      display: none;
    }
  }

  .separator {
    padding: 0 8px;

    &.evaluation {
      padding-left: 0;
      color: var(--ion-color-primary-V2);
      background-color: var(--ion-color-grazias-evaluation);
      opacity: 0.2;
    }

    &.child-evaluation {
      padding-left: 0;
      color: var(--ion-color-primary-V2);
      background-color: var(--ion-color-child-evaluation);
      opacity: 0.2;
    }

    &.survey {
      padding-left: 0;
      color: var(--ion-color-primary-V2);
      opacity: 0.2;
    }
  }
}

.report-control {
  width: 144px;
  margin-right: 15px;
}

.scales {
  .scale-icon {
    display: inline-block;
    height: 1rem;
    width: 1rem;
    margin-right: 0.55rem;

    &.didactics {
      background: url('/src/assets/svg/skala_didaktik.svg') center center / contain no-repeat;
    }

    &.grazias {
      background: url('/src/assets/svg/skala_basis.svg') center center / contain no-repeat;
    }

    &.math {
      background: url('/src/assets/svg/skala_mathe.svg') center center / contain no-repeat;
    }

    &.nature {
      background: url('/src/assets/svg/skala_physik.svg') center center / contain no-repeat;
    }

    &.grazias-v2 {
      background: url('/src/assets/svg/skala_basis_new.svg') center center / contain no-repeat;
    }
  }
}

.list-item-sub-title {
  font-weight: 400;
  margin-right: 0.6rem;
}

.list-item-survey-date {
  font-weight: 400;
}

.no-results-badge {
  --background: white;
  display: flex;
  height: 24px;
  margin-left: 16px;
  align-items: center;

  .warning-icon {
    margin-right: 6px;
    display: flex;
    align-items: flex-start;
    width: 16px;
    height: 16px;
    color: $color-danger;
  }

  .no-results-available-message {
    font-weight: 700;
    margin: 0 2px;
    color: $color-danger;
  }
}

.icon-wrapper {
  display: inline-block;
  vertical-align: middle;
}
