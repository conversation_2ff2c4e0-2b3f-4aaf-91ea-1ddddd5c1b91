import { Component, Input } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { PrimaryButtonType } from '../../../shared/components/primary-button/primary-button.component';
import { SurveyType } from '../../../../services/jwt/survey-token.service';
import { TranslateService } from '@ngx-translate/core';
import { ReportType } from '../models';

@Component({
  selector: 'app-report-selection-error-modal',
  templateUrl: './report-selection-error-modal.page.html',
  styleUrls: ['./report-selection-error-modal.page.scss', './common.scss'],
})
export class ReportSelectionErrorModalPage {
  @Input() type: ReportType;

  public buttonType = PrimaryButtonType;
  public reportType = ReportType;
  public surveyType = SurveyType;

  constructor(
    private modalController: ModalController,
    public translate: TranslateService,
  ) {}

  public async dismissModal(): Promise<void> {
    await this.modalController.dismiss();
  }

  public getConditionFor(surveyType: SurveyType): string {
    const zeroOrOne = this.translate.instant('report_selection_error.condition.zero_or_one');
    const zeroOrTwo = this.translate.instant('report_selection_error.condition.zero_or_two');
    const zeroToN = this.translate.instant('report_selection_error.condition.zero_to_n');
    switch (this.type) {
      case ReportType.ORGANIZATION_REPORT:
        return surveyType === SurveyType.PARENTAL_SURVEY || surveyType === SurveyType.STAFF_SURVEY
          ? zeroToN
          : zeroOrOne;
      case ReportType.LOCATION_REPORT:
        return surveyType === SurveyType.ORGANIZATION_SURVEY ||
          surveyType === SurveyType.ADMINISTRATION_SURVEY
          ? zeroOrOne
          : zeroToN;
      case ReportType.COMPARISON_REPORT:
        return zeroOrTwo;
    }
  }

  public getSubtitle(): string {
    switch (this.type) {
      case ReportType.ORGANIZATION_REPORT:
        return this.translate.instant('report_selection_error.sub_title.organization_report');
      case ReportType.LOCATION_REPORT:
        return this.translate.instant('report_selection_error.sub_title.location_report');
      case ReportType.COMPARISON_REPORT:
        return this.translate.instant('report_selection_error.sub_title.comparison_report');
    }
  }
}
