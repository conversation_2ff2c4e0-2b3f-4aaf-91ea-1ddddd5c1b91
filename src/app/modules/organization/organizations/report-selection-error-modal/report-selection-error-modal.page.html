<ion-content class="design-v2">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col class="ion-no-padding">
        <h1>{{ 'report_selection_error.title' | translate }}</h1>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col class="ion-no-padding">
        <ion-text> {{ getSubtitle() }} </ion-text>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col class="ion-no-padding">
        <div class="error-cases">
          <ion-text class="error-list-item bold">
            {{ getConditionFor(surveyType.ORGANIZATION_SURVEY) }}
          </ion-text>
          <ion-text *ngIf="type !== reportType.COMPARISON_REPORT" class="error-list-item">
            {{ 'reports.front_page.organization_survey' | translate }}
          </ion-text>
          <ion-text *ngIf="type === reportType.COMPARISON_REPORT" class="error-list-item">
            {{ 'reports.front_page.organization_surveys' | translate }}
          </ion-text>
        </div>
        <div class="error-cases">
          <ion-text class="error-list-item bold">
            {{ getConditionFor(surveyType.ADMINISTRATION_SURVEY) }}
          </ion-text>
          <ion-text *ngIf="type !== reportType.COMPARISON_REPORT" class="error-list-item">
            {{ 'reports.front_page.administration_survey' | translate }}
          </ion-text>
          <ion-text *ngIf="type === reportType.COMPARISON_REPORT" class="error-list-item">
            {{ 'reports.front_page.administration_surveys' | translate }}
          </ion-text>
          <ion-text class="error-list-item bold" *ngIf="type !== reportType.ORGANIZATION_REPORT">
            {{ 'report_selection_error.location_postfix' | translate }}
          </ion-text>
        </div>
        <div class="error-cases">
          <ion-text class="error-list-item bold">
            {{ getConditionFor(surveyType.PARENTAL_SURVEY) }}
          </ion-text>
          <ion-text class="error-list-item">
            {{ 'report_selection_error.parental_surveys' | translate }}
          </ion-text>
          <ion-text class="error-list-item bold" *ngIf="type !== reportType.ORGANIZATION_REPORT">
            {{ 'report_selection_error.location_postfix' | translate }}
          </ion-text>
        </div>
        <div class="error-cases">
          <ion-text class="error-list-item bold">
            {{ getConditionFor(surveyType.STAFF_SURVEY) }}
          </ion-text>
          <ion-text class="error-list-item">
            {{ 'report_selection_error.staff_surveys' | translate }}
          </ion-text>
          <ion-text class="error-list-item bold" *ngIf="type !== reportType.ORGANIZATION_REPORT">
            {{ 'report_selection_error.location_postfix' | translate }}
          </ion-text>
        </div>
        <div class="error-cases" *ngIf="type !== reportType.COMPARISON_REPORT">
          <ion-text
            class="error-list-item"
            [innerHTML]="'report_selection_error.child_evaluation' | translate"
          >
          </ion-text>
        </div>
        <div class="error-cases" *ngIf="type === reportType.COMPARISON_REPORT">
          <ion-text
            class="error-list-item"
            [innerHTML]="'report_selection_error.evaluation.onlyChooseBasicV2' | translate"
          >
          </ion-text>
        </div>
      </ion-col>
    </ion-row>
    <ion-row class="bottom-row">
      <ion-col>
        <app-primary-button
          type="submit"
          expand="block"
          (onClick)="dismissModal()"
          [label]="'global.alert.close' | translate"
          [buttonType]="buttonType.GRADIENT"
        >
        </app-primary-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
