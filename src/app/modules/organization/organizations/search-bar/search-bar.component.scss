@import './../../../../../theme/variables_v2';

:host ::ng-deep app-input-with-heading-and-suggestions {
  .input-container {
    border-color: $color-info-box-background;
    background-color: $color-info-box-background;

    input {
      height: 48px;
    }
  }
}

.custom-chip {
  color: var(--ion-color-primary-V2);
  background-color: $color-info-box-background;
  border-radius: 7px;
  margin: 12px 12px 0px 0px;
  padding: 0px 16px 0px 16px;
  height: 48px;

  ion-label {
    font-size: 16px;
  }

  ion-icon {
    color: var(--ion-color-primary-V2);
  }

  img {
    margin-left: 16px;
    height: 18px;
  }
}
