import { Component, EventEmitter, Output } from '@angular/core';
import { AdminService } from '../../../../services/admin/admin.service';

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
})
export class SearchBarComponent {
  @Output() tagsChanged = new EventEmitter<string[]>();

  public tags: string[] = [];
  public tagSuggestions: string[] = [];

  constructor(private adminService: AdminService) {}

  public removeTag(tag: string): void {
    const index = this.tags.indexOf(tag);
    if (index !== -1) {
      this.tags.splice(index, 1);
    }
    this.tagsChanged.emit(this.tags);
  }

  public addTag(tag: string): void {
    if (this.tagSuggestions.includes(tag) && !this.tags.includes(tag)) {
      this.tags.push(tag);
      this.tagsChanged.emit(this.tags);
    }
    this.clearSuggestions();
  }

  async updateTagSuggestions(query: string): Promise<void> {
    if (query === '' || query === null) {
      this.clearSuggestions();
      return;
    }
    return new Promise<void>((resolve) => {
      this.adminService.retrieveTagsLike(query).subscribe(async (result) => {
        if (result.success) {
          this.tagSuggestions = result.data.result.sort((a, b) => a.localeCompare(b));
        } else {
          this.clearSuggestions();
        }
        resolve();
      });
    });
  }

  clearSuggestions(): void {
    this.tagSuggestions = [];
  }
}
