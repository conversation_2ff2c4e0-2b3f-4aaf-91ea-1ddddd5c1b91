<ion-grid class="ion-no-padding">
  <ion-row class="ion-no-margin">
    <ion-col class="ion-no-padding ion-no-margin">
      <app-input-with-heading-and-suggestions
        type="text"
        [suggestions]="tagSuggestions"
        [iconPath]="'assets/svg/ic_label.svg'"
        [placeholder]="'organization.search.labels' | translate"
        (inputChanged)="updateTagSuggestions($event)"
        (submit)="addTag($event)"
      ></app-input-with-heading-and-suggestions>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col class="ion-no-padding">
      <ion-chip *ngFor="let tag of tags" class="custom-chip">
        <ion-label>{{ tag }}</ion-label>
        <img
          src="../../../../../assets/svg/ic_cancel.svg"
          alt="open"
          class="menu-toggle"
          (click)="removeTag(tag)"
        />
      </ion-chip>
    </ion-col>
  </ion-row>
</ion-grid>
