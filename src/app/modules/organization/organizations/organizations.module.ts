import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTreeModule } from '@angular/material/tree';
import { RouterModule, Routes } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { GraziasMissingTranslationHandler } from '../../../utils/missingTranslationHandler';
import { SharedModule } from '../../shared/shared.module';
import { ComparisonToggleComponent } from './comparison-toggle/comparison-toggle.component';
import { CreateMenuComponent } from './create-menu/create-menu.component';
import { OrganizationsPage } from './organizations.page';
import { PaginatorComponent } from './paginator/paginator.component';
import { SearchBarComponent } from './search-bar/search-bar.component';

const routes: Routes = [
  {
    path: '',
    component: OrganizationsPage,
  },
];

@NgModule({
  imports: [
    MatTreeModule,
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
    MatIconModule,
    SharedModule,
    MatMenuModule,
    MatButtonModule,
    MatTooltipModule,
  ],
  declarations: [
    OrganizationsPage,
    CreateMenuComponent,
    SearchBarComponent,
    ComparisonToggleComponent,
    PaginatorComponent,
  ],
})
export class OrganizationsPageModule {}
