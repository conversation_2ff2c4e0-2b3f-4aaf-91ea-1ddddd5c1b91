export enum OrganizationNodeType {
  OR<PERSON><PERSON>ZATION,
  LOCATION,
  <PERSON><PERSON><PERSON>,
  ORGANI<PERSON><PERSON>ION_SURVEY,
  ADMINISTRATION_SURVEY,
  PARENTAL_SURVEY,
  STAFF_SURVEY,
  EVALUATION,
  CHILD_EVALUATION,
}

export namespace OrganizationNodeType {
  export function getTranslationKey(type: OrganizationNodeType): string {
    switch (type) {
      case OrganizationNodeType.ORGANIZATION_SURVEY:
        return 'organization.organization-survey';
      case OrganizationNodeType.ADMINISTRATION_SURVEY:
        return 'organization.administration-survey';
      case OrganizationNodeType.PARENTAL_SURVEY:
        return 'organization.parental-survey';
      case OrganizationNodeType.STAFF_SURVEY:
        return 'organization.staff-survey';

      default:
        break;
    }
  }
}

export enum ActionType {
  NEW_LOCATION = 'new-location',
  NEW_GROUP = 'new-group',
  NEW_ORGANIZATION_SURVEY = 'new-organization-survey',
  NEW_ADMINISTRATION_SURVEY = 'new-administration-survey',
  NEW_PARENTAL_SURVEY = 'new-parental-survey',
  NEW_STAFF_SURVEY = 'new-staff-survey',
  ASSOCIATE_CHILD_EVALUATION = 'associate-child-evaluation',
  ASSOCIATE_EVALUATION = 'associate-evaluation',
  NEW_ORGANIZATION_REPORT = 'new-organization-report',
  NEW_LOCATION_REPORT = 'new-location-report',
  NEW_COMPARISON_REPORT = 'new-comparison-report',
}

export enum EvaluationType {
  EVALUATION = 'EVALUATION',
  CHILD_EVALUATION = 'CHILD_EVALUATION',
}
