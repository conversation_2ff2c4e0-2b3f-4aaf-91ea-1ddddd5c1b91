import { NestedTreeControl } from '@angular/cdk/tree';
import { HttpParams } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { <PERSON><PERSON><PERSON><PERSON>roller, ModalController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import _ from 'lodash';
import moment from 'moment';
import {
  AdminService,
  ComparisonReportDataRequest,
  GenerateReportRequest,
  OrganizationReportDataRequest,
  OrganizationsWithLocationsAndGroupsResponse,
  SurveyState,
} from '../../../services/admin/admin.service';
import { Scale } from '../../../services/evaluation/models/scale';
import {
  ScaleAgeDefinition,
  ScaleDefinition,
} from '../../../services/evaluation/models/scale-definition';
import {
  ScaleTypeDefinition,
  getScaleTypeFromDefinition,
} from '../../../services/evaluation/models/scale-type-definition';
import { SurveyTokenService } from '../../../services/jwt/survey-token.service';
import { Languages } from '../../../utils/language-helper';
import { Utils } from '../../../utils/utils';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import { ReportLanguagePopUpComponent } from '../../shared/components/report-language-pop-up/report-language-pop-up.component';
import {
  OrganizationNode,
  OrganizationRepository,
  SurveyUrlParam,
} from '../organizations.repository';
import { AssociateEvaluationModalPage } from './associate-evaluation-modal/associate-evaluation-modal.page';
import { ComparisonSelection } from './comparison-toggle/comparison-toggle.component';
import { ReportType } from './models';
import { ReportSelectionErrorModalPage } from './report-selection-error-modal/report-selection-error-modal.page';
import { ActionType, EvaluationType, OrganizationNodeType } from './utils';
import {ActivatedRoute} from '@angular/router';

interface PageInfo {
  offset: number;
  count: number;
  totalCount: number;
}

@Component({
  selector: 'app-organizations',
  templateUrl: './organizations.page.html',
  styleUrls: ['./organizations.page.scss'],
})
export class OrganizationsPage implements OnInit {
  constructor(
    public translate: TranslateService,
    public loadingController: LoadingController,
    private adminService: AdminService,
    private toastController: ToastController,
    private navController: NavController,
    private organizationRepo: OrganizationRepository,
    private surveyTokenService: SurveyTokenService,
    private modalController: ModalController,
    private route: ActivatedRoute,
  ) {}

  public reportType: ReportType;
  public buttonType = PrimaryButtonType;
  public loadingUi: HTMLIonLoadingElement = null;
  treeControl = new NestedTreeControl<OrganizationNode>((node) => node.children);
  dataSource = new MatTreeNestedDataSource<OrganizationNode>();
  pageInfo: PageInfo = {
    offset: 0,
    totalCount: 0,
    count: 10,
  };
  isLoading = true;
  isSearchActive = false;

  private organizationIdOfCurrentReportSelection: number = null;
  private selectedTags: string[] = [];

  private static resolveType(type: string): OrganizationNodeType {
    switch (type) {
      case 'ORGANIZATION_SURVEY':
        return OrganizationNodeType.ORGANIZATION_SURVEY;
      case 'ADMINISTRATION_SURVEY':
        return OrganizationNodeType.ADMINISTRATION_SURVEY;
      case 'PARENTAL_SURVEY':
        return OrganizationNodeType.PARENTAL_SURVEY;
      case 'STAFF_SURVEY':
        return OrganizationNodeType.STAFF_SURVEY;
    }
  }

  async ngOnInit(): Promise<void> {
    this.route.queryParams.subscribe(async params => {
      const initialPage = +params['initialPage'];
      if (initialPage) {
        this.pageInfo.offset = (initialPage-1) * 10;
      }
    });

    await this.fetchOrganizations();
    this.expandCreatedNode();
  }

  isSurveyOrChildEvaluatorAccess(_: number, node: OrganizationNode): boolean {
    return (
      node.type !== OrganizationNodeType.ORGANIZATION &&
      node.type !== OrganizationNodeType.LOCATION &&
      node.type !== OrganizationNodeType.GROUP
    );
  }

  hasChild(node: OrganizationNode): boolean {
    return !!node.children && node.children.length > 0;
  }

  isOrganizationNode(node: OrganizationNode): boolean {
    return node.type === OrganizationNodeType.ORGANIZATION;
  }

  getIdIfOrganizationsNode(node: OrganizationNode): number {
    return this.isOrganizationNode(node) ? node.id : null;
  }

  getTooltipTextFromArray(tags: Array<string>): string {
    return tags.map((tag) => ` ${tag}`).toString();
  }

  expandCreatedNode(): void {
    if (this.organizationRepo && this.organizationRepo.newlyCreatedNode) {
      this.expandNode(this.organizationRepo.newlyCreatedNode);
    }
  }

  expandNode(node: OrganizationNode): void {
    if (node === null || node.id === null || node.id === -1) {
      return;
    }
    let org: OrganizationNode;
    let loc: OrganizationNode;
    let group: OrganizationNode;
    if (node.orgId !== -1) {
      org = this.dataSource.data.find(
        (value) => value.id === node.orgId && value.type === OrganizationNodeType.ORGANIZATION,
      );
      this.treeControl.expand(org);

      if (node.type === OrganizationNodeType.ORGANIZATION_SURVEY && node.id !== -1 && org != null) {
        this.treeControl.expand(
          org.children.find(
            (value) =>
              value.id === node.id && value.type === OrganizationNodeType.ORGANIZATION_SURVEY,
          ),
        );
      }

      if (node.locId !== -1 && org != null) {
        loc = org.children.find(
          (value) => value.id === node.locId && value.type === OrganizationNodeType.LOCATION,
        );
        this.treeControl.expand(loc);

        if (node.type === OrganizationNodeType.ADMINISTRATION_SURVEY && loc != null) {
          this.treeControl.expand(
            loc.children.find(
              (value) =>
                value.id === node.id && value.type === OrganizationNodeType.ADMINISTRATION_SURVEY,
            ),
          );
        }

        if (node.type === OrganizationNodeType.PARENTAL_SURVEY && loc != null) {
          this.treeControl.expand(
            loc.children.find(
              (value) =>
                value.id === node.id && value.type === OrganizationNodeType.PARENTAL_SURVEY,
            ),
          );
        }

        if (node.groupId !== -1 && loc != null) {
          group = loc.children.find(
            (value) => value.id === node.groupId && value.type === OrganizationNodeType.GROUP,
          );
          this.treeControl.expand(group);

          if (node.type === OrganizationNodeType.STAFF_SURVEY && node.id !== -1 && group != null) {
            this.treeControl.expand(
              loc.children.find(
                (value) => value.id === node.id && node.type === OrganizationNodeType.STAFF_SURVEY,
              ),
            );
          }
        }
      }
    }

    if (org) {
      setTimeout(() => {
        document
          .getElementById(org.id.toString())
          .scrollIntoView({ block: 'center', behavior: 'smooth' });
      }, 300);
    }

    this.organizationRepo.newlyCreatedNode = null;
  }

  async fetchOrganizations(): Promise<void> {
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();

    this.isSearchActive = this.selectedTags.length > 0;
    return new Promise<void>((resolve) => {
      this.adminService
        .getOrganizationsWithLocationsAndGroups(
          this.selectedTags,
          this.pageInfo.offset,
          this.pageInfo.count,
        )
        .subscribe((resp) => {
          if (resp.success) {
            this.pageInfo.totalCount = resp.data.totalCount;
            this.initTreeView(resp.data);
          } else {
            this.showToast(this.translate.instant('admin.dashboard.label.loadingError'), 'danger');
          }

          this.loadingUi.dismiss();
          this.isLoading = false;
          resolve();
        });
    });
  }

  public getCurrentPage(): number {
    return this.pageInfo.offset / this.pageInfo.count + 1;
  }

  public getLastPageNumber(): number {
    return Math.ceil(this.pageInfo.totalCount / this.pageInfo.count);
  }

  public onTagsChanged(tags: string[]): void {
    this.pageInfo.offset = 0;
    this.selectedTags = tags;
    this.fetchOrganizations();
  }

  async changePage(page: number): Promise<void> {
    const lastPageNumber = this.getLastPageNumber();
    if (page === 0 || page === -1) {
      this.pageInfo.offset = 0;
    } else if (page >= lastPageNumber) {
      this.pageInfo.offset = (lastPageNumber - 1) * this.pageInfo.count;
    } else {
      this.pageInfo.offset = (page - 1) * this.pageInfo.count;
    }
    this.fetchOrganizations();
  }

  private initTreeView(data: OrganizationsWithLocationsAndGroupsResponse): void {
    this.dataSource.data = data.result
      .map(
        (org) =>
          (<OrganizationNode>{
            // organizations
            name: org.organization.organizationName,
            id: org.organization.id,
            tags: org.organization.tags,
            orgId: org.organization.id,
            locId: -1,
            groupId: -1,
            type: OrganizationNodeType.ORGANIZATION,
            children: org.organization.surveys
              .map(
                (survey) =>
                  (<OrganizationNode>{
                    // organizational surveys
                    name: this.translate.instant(
                      this.getTitleByType(OrganizationsPage.resolveType(survey.type.toString())),
                    ),
                    id: survey.id,
                    tags: survey.tags,
                    orgId: org.organization.id,
                    locId: -1,
                    groupId: -1,
                    type: OrganizationsPage.resolveType(survey.type.toString()),
                    surveyToken: survey.token,
                    surveyState: survey.state,
                    children: [],
                    validFrom: survey.validFrom,
                    validUntil: survey.validUntil,
                  }) as OrganizationNode,
              )
              .concat(
                ...org.locations.map(
                  (loc) =>
                    (<OrganizationNode>{
                      // locations
                      name: loc.location.locationName,
                      id: loc.location.id,
                      tags: loc.location.tags,
                      orgId: org.organization.id,
                      locId: loc.location.id,
                      groupId: -1,
                      type: OrganizationNodeType.LOCATION,
                      children: loc.location.surveys
                        .map(
                          (survey) =>
                            (<OrganizationNode>{
                              // locational surveys
                              name: this.translate.instant(
                                this.getTitleByType(
                                  OrganizationsPage.resolveType(survey.type.toString()),
                                ),
                              ),
                              id: survey.id,
                              tags: survey.tags,
                              orgId: org.organization.id,
                              locId: loc.location.id,
                              groupId: -1,
                              type: OrganizationsPage.resolveType(survey.type.toString()),
                              surveyToken: survey.token,
                              surveyState: survey.state,
                              children: [],
                              validFrom: survey.validFrom,
                              validUntil: survey.validUntil,
                            }) as OrganizationNode,
                        )
                        .concat(
                          loc.groups.map(
                            (grp) =>
                              (<OrganizationNode>{
                                // groups
                                name: grp.groupName,
                                id: grp.id,
                                tags: grp.tags,
                                orgId: org.organization.id,
                                locId: loc.location.id,
                                groupId: grp.id,
                                type: OrganizationNodeType.GROUP,
                                children: grp.surveys
                                  .map(
                                    (survey) =>
                                      (<OrganizationNode>{
                                        // group surveys
                                        name: this.translate.instant(
                                          this.getTitleByType(
                                            OrganizationsPage.resolveType(survey.type.toString()),
                                          ),
                                        ),
                                        id: survey.id,
                                        tags: survey.tags,
                                        orgId: org.organization.id,
                                        locId: loc.location.id,
                                        groupId: grp.id,
                                        type: OrganizationsPage.resolveType(survey.type.toString()),
                                        surveyToken: survey.token,
                                        surveyState: survey.state,
                                        children: [],
                                        validFrom: survey.validFrom,
                                        validUntil: survey.validUntil,
                                      }) as OrganizationNode,
                                  )
                                  .concat(
                                    grp.childEvaluations.map(
                                      (childEval) =>
                                        (<OrganizationNode>{
                                          // child-evaluations
                                          name: this.translate.instant(
                                            'organization.child-evaluation.title',
                                          ),
                                          detail: `${childEval.evaluatorFirstName} ${childEval.evaluatorLastName}`,
                                          id: -1,
                                          tags: [],
                                          orgId: org.organization.id,
                                          locId: loc.location.id,
                                          groupId: grp.id,
                                          type: OrganizationNodeType.CHILD_EVALUATION,
                                          children: [],
                                          evaluationId: childEval.id,
                                        }) as OrganizationNode,
                                    ),
                                  )
                                  .concat(
                                    grp.evaluations.map(
                                      (evaluation) =>
                                        (<OrganizationNode>{
                                          // evaluations
                                          name: this.translate.instant(
                                            'organization.evaluation.title',
                                          ),
                                          detail: `${evaluation.evaluatorFirstName} ${evaluation.evaluatorLastName}`,
                                          id: -1,
                                          tags: [],
                                          orgId: org.organization.id,
                                          locId: loc.location.id,
                                          groupId: grp.id,
                                          type: OrganizationNodeType.EVALUATION,
                                          children: [],
                                          evaluationId: evaluation.id,
                                          evaluatedScales: evaluation.scales,
                                          scales: this.getScaleTypeDefinition(evaluation.scales),
                                          scaleAgeDefinition: Scale.getScaleAgeFromDefinition(
                                            evaluation.scales[0],
                                          ),
                                        }) as OrganizationNode,
                                    ),
                                  ),
                              }) as OrganizationNode,
                          ),
                        ),
                    }) as OrganizationNode,
                ),
              ),
          }) as OrganizationNode,
      );
    this.treeControl.dataNodes = this.dataSource.data;
  }

  public getScaleIconFrom(scaleType: ScaleTypeDefinition): string {
    switch (scaleType) {
      case ScaleTypeDefinition.DIDACTICS:
        return 'scale-icon didactics';
      case ScaleTypeDefinition.GRAZIAS:
        return 'scale-icon grazias';
      case ScaleTypeDefinition.MATH:
        return 'scale-icon math';
      case ScaleTypeDefinition.NATURE:
        return 'scale-icon nature';
      case ScaleTypeDefinition.GRAZIAS_V2:
        return 'scale-icon grazias-v2';
      default:
        return '';
    }
  }

  public getScaleAgeFromDefinition(scaleAgeDefinition: ScaleAgeDefinition): string {
    switch (scaleAgeDefinition) {
      case ScaleAgeDefinition.ZERO_TO_THREE:
        return '0 - 3';
      case ScaleAgeDefinition.THREE_TO_SIX:
        return '3 - 6';
      case ScaleAgeDefinition.ZERO_TO_SIX:
        return '0 - 6';
      default:
        return '';
    }
  }

  public isSurveyStateExpiredWithoutResults(node: OrganizationNode): boolean {
    return this.isSurvey(node) && node.surveyState === SurveyState.NO_RESULTS_AVAILABLE;
  }

  public getSurveyPeriod(node: OrganizationNode): string {
    const fromDate = moment(node.validFrom).format('MM.yyyy');
    const untilDate = moment(node.validUntil).format('MM.yyyy');
    return fromDate === untilDate ? fromDate : `${fromDate} - ${untilDate}`;
  }

  getTitleByType(type: OrganizationNodeType): string {
    return OrganizationNodeType.getTranslationKey(type);
  }

  async showToast(message: string, color: string): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color,
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    toast.present();
  }

  handleClickAction(parent: OrganizationNode, actionType: ActionType): void {
    let url;
    switch (actionType) {
      case ActionType.NEW_LOCATION:
        url = `/admin/organizations/upsert-location-form/${parent.orgId}/-1`;
        break;
      case ActionType.NEW_GROUP:
        url = `/admin/organizations/upsert-group-form/${parent.orgId}/${parent.locId}/-1`;
        break;
      case ActionType.NEW_ORGANIZATION_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.organization}/${parent.orgId}/${parent.locId}/${parent.groupId}/-1`;
        break;
      case ActionType.NEW_ADMINISTRATION_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.administration}/${parent.orgId}/${parent.locId}/${parent.groupId}/-1`;
        break;
      case ActionType.NEW_PARENTAL_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.parental}/${parent.orgId}/${parent.locId}/${parent.groupId}/-1`;
        break;
      case ActionType.NEW_STAFF_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.staff}/${parent.orgId}/${parent.locId}/${parent.groupId}/-1`;
        break;
      case ActionType.ASSOCIATE_EVALUATION:
        this.presentEvaluationAssociationDialog(EvaluationType.EVALUATION, parent.id);
        return;
      case ActionType.ASSOCIATE_CHILD_EVALUATION:
        this.presentEvaluationAssociationDialog(EvaluationType.CHILD_EVALUATION, parent.id);
        return;
      case ActionType.NEW_ORGANIZATION_REPORT:
        this.reportType = ReportType.ORGANIZATION_REPORT;
        this.setReportSelectionMode(parent, true);
        return;
      case ActionType.NEW_LOCATION_REPORT:
        this.reportType = ReportType.LOCATION_REPORT;
        this.setReportSelectionMode(parent, true);
        return;
      case ActionType.NEW_COMPARISON_REPORT:
        this.reportType = ReportType.COMPARISON_REPORT;
        this.setComparisonReportSelectionMode(parent, true);
        return;
      default:
        url = null;
    }
    if (url !== null) {
      this.navController.navigateForward(`${url}?page=${this.getCurrentPage()}`);
    }
  }

  getClickActions(nodeType: OrganizationNodeType): ActionType[] {
    switch (nodeType) {
      case OrganizationNodeType.ORGANIZATION:
        return [
          ActionType.NEW_LOCATION,
          ActionType.NEW_ORGANIZATION_SURVEY,
          ActionType.NEW_ORGANIZATION_REPORT,
        ];
      case OrganizationNodeType.LOCATION:
        return [
          ActionType.NEW_GROUP,
          ActionType.NEW_ADMINISTRATION_SURVEY,
          ActionType.NEW_PARENTAL_SURVEY,
          ActionType.NEW_LOCATION_REPORT,
          ActionType.NEW_COMPARISON_REPORT,
        ];
      case OrganizationNodeType.GROUP:
        return [
          ActionType.NEW_STAFF_SURVEY,
          ActionType.ASSOCIATE_EVALUATION,
          ActionType.ASSOCIATE_CHILD_EVALUATION,
        ];
      default:
        break;
    }
  }

  editOrganizationalData(node: OrganizationNode): void {
    let url;
    switch (node.type) {
      case OrganizationNodeType.ORGANIZATION:
        url = `/admin/organizations/upsert-organization-form/${node.id}`;
        break;
      case OrganizationNodeType.LOCATION:
        url = `/admin/organizations/upsert-location-form/${node.orgId}/${node.id}`;
        break;
      case OrganizationNodeType.GROUP:
        url = `/admin/organizations/upsert-group-form/${node.orgId}/${node.locId}/${node.id}`;
        break;
      case OrganizationNodeType.ORGANIZATION_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.organization}/${node.orgId}/${node.locId}/${node.groupId}/${node.id}`;
        break;
      case OrganizationNodeType.ADMINISTRATION_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.administration}/${node.orgId}/${node.locId}/${node.groupId}/${node.id}`;
        break;
      case OrganizationNodeType.PARENTAL_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.parental}/${node.orgId}/${node.locId}/${node.groupId}/${node.id}`;
        break;
      case OrganizationNodeType.STAFF_SURVEY:
        url = `/admin/organizations/survey-form/${SurveyUrlParam.staff}/${node.orgId}/${node.locId}/${node.groupId}/${node.id}`;
        break;
      case OrganizationNodeType.CHILD_EVALUATION:
        url = `/admin/organizations/evaluation-detail/${EvaluationType.CHILD_EVALUATION}/${node.evaluationId}`;
        break;
      case OrganizationNodeType.EVALUATION:
        url = `/admin/organizations/evaluation-detail/${EvaluationType.EVALUATION}/${node.evaluationId}`;
        break;
      default:
        url = null;
        this.showToast(this.translate.instant('global.error.generic'), 'danger');
    }
    if (url !== null) {
      this.navController.navigateForward(`${url}?page=${this.getCurrentPage()}`);
    }
  }

  navigateOrganizationForm(orgId: number = -1): void {
    this.navController.navigateForward(`/admin/organizations/upsert-organization-form/${orgId}`);
  }

  public getSurveyOrEvaluationNodeCssClass(node: OrganizationNode): string {
    let cssClass = 'tree-node-child';

    if (node.type === OrganizationNodeType.CHILD_EVALUATION) {
      cssClass += ' child-evaluation';
      return cssClass;
    }

    if (node.type === OrganizationNodeType.EVALUATION) {
      cssClass += ' evaluation';
      return cssClass;
    }

    if (
      typeof node.surveyToken === 'string' &&
      node.surveyToken !== '' &&
      node.surveyToken != null
    ) {
      cssClass +=
        ' survey ' + this.surveyTokenService.getSurveyTypeFromToken(node.surveyToken) + '-survey';
    }

    cssClass += node.surveyState === SurveyState.OPEN ? ' survey-state-open' : '';
    return cssClass;
  }

  public copyToClipboard(node: OrganizationNode): void {
    if (
      typeof node.surveyToken === 'string' &&
      node.surveyToken !== '' &&
      node.surveyToken != null
    ) {
      Utils.copyToClipboard(this.surveyTokenService.getLinkToQuestionnaire(node.surveyToken));
      this.showSuccessMessage('surveyToken.copied');
    }
  }

  public showCopyToClipboard(node: OrganizationNode): boolean {
    switch (node.type) {
      case OrganizationNodeType.ORGANIZATION_SURVEY:
      case OrganizationNodeType.ADMINISTRATION_SURVEY:
      case OrganizationNodeType.PARENTAL_SURVEY:
      case OrganizationNodeType.STAFF_SURVEY:
        return true;
      default:
        return false;
    }
  }

  public getNodeDetail(node: OrganizationNode): string {
    switch (node.type) {
      case OrganizationNodeType.CHILD_EVALUATION:
        return node.detail;
      case OrganizationNodeType.EVALUATION:
        return node.detail;
      default:
        return '';
    }
  }

  public getSeparatorCss(node: OrganizationNode): string {
    switch (node.type) {
      case OrganizationNodeType.CHILD_EVALUATION:
        return 'separator child-evaluation';
      case OrganizationNodeType.EVALUATION:
        return 'separator evaluation';
      case OrganizationNodeType.ORGANIZATION_SURVEY:
      case OrganizationNodeType.ADMINISTRATION_SURVEY:
      case OrganizationNodeType.PARENTAL_SURVEY:
      case OrganizationNodeType.STAFF_SURVEY:
        return 'separator survey';
      default:
        return 'display-none';
    }
  }

  async showSuccessMessage(translationKey: string): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant(translationKey),
      color: 'success',
      position: 'bottom',
      duration: 5000,
      cssClass: 'global-toast',
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }

  async presentEvaluationAssociationDialog(type: EvaluationType, groupId: number): Promise<void> {
    const modal = await this.modalController.create({
      component: AssociateEvaluationModalPage,
      componentProps: {
        groupId,
        type,
      },
      backdropDismiss: false,
      animated: false,
      showBackdrop: true,
      cssClass: 'eval-to-group-association-modal',
    });

    modal.onDidDismiss().then((data) => {
      if (data.data.submitted) {
        this.showSuccessMessage('organization.associate-evaluations.success-message');
        this.fetchOrganizations();
      }
    });

    return await modal.present();
  }

  async presentReportSelectionErrorModal(type: ReportType): Promise<void> {
    const modal = await this.modalController.create({
      component: ReportSelectionErrorModalPage,
      componentProps: {
        type,
      },
      backdropDismiss: false,
      animated: false,
      showBackdrop: true,
      cssClass: 'eval-to-group-association-modal',
    });

    modal.onDidDismiss().then((data) => {
      if (data.data.submitted) {
        this.showSuccessMessage('organization.associate-evaluations.success-message');
        this.fetchOrganizations();
      }
    });

    return await modal.present();
  }

  public hasReportControl(node: OrganizationNode): boolean {
    return (
      (node.type === OrganizationNodeType.ORGANIZATION ||
        node.type === OrganizationNodeType.LOCATION) &&
      (node.reportSelectionActive || node.comparisonReportSelectionActive)
    );
  }

  async setComparisonReportSelectionMode(node: OrganizationNode, activate: boolean): Promise<void> {
    if (activate) {
      await this.resetCurrentReportSelections(node);
    }

    const parentNode = this.dataSource.data.find((value) => value.id === node.orgId);
    const orgSurveys = parentNode.children.filter(
      (org) =>
        org.type === OrganizationNodeType.ORGANIZATION_SURVEY &&
        org.surveyState !== SurveyState.OPEN &&
        org.surveyState !== SurveyState.NO_RESULTS_AVAILABLE,
    );
    orgSurveys.forEach((survey) => (survey.comparisonReportSelectionActive = activate));

    this.setComparisonSelectionModeForNodeChildren(node, node.type, activate);
  }

  async setComparisonSelectionModeForNodeChildren(
    node: OrganizationNode,
    reportBaseNode: OrganizationNodeType,
    activate: boolean,
  ): Promise<void> {
    node.comparisonReportSelectionActive =
      this.isNodeTypeSelectableForReport(node, reportBaseNode) && activate;
    if (!activate) {
      node.comparisonReportSelection = ComparisonSelection.NONE;
    }
    node.children.forEach((nodeChild) => {
      if (!activate) {
        nodeChild.comparisonReportSelection = ComparisonSelection.NONE;
      }
      nodeChild.comparisonReportSelectionActive =
        this.isNodeTypeSelectableForReport(nodeChild, reportBaseNode) && activate;
      nodeChild.children.forEach((sub) =>
        this.setComparisonSelectionModeForNodeChildren(sub, reportBaseNode, activate),
      );
    });
    this.expandNode(node);
  }

  async setReportSelectionMode(node: OrganizationNode, activate: boolean): Promise<void> {
    if (activate) {
      await this.resetCurrentReportSelections(node);
    }

    if (this.reportType === ReportType.LOCATION_REPORT) {
      this.setParentOrganizationSurveySelection(node, activate);
    }

    await this.setReportSelectionModeForNodeChildren(node, node.type, activate);
  }

  deactivateCurrentReportSelectionMode(node: OrganizationNode): void {
    if (
      this.reportType === ReportType.LOCATION_REPORT ||
      this.reportType === ReportType.ORGANIZATION_REPORT
    ) {
      this.setReportSelectionMode(node, false);
    } else {
      this.setComparisonReportSelectionMode(node, false);
    }
  }

  async setReportSelectionModeForNodeChildren(
    node: OrganizationNode,
    reportBaseNode: OrganizationNodeType,
    activate: boolean,
  ): Promise<void> {
    node.reportSelectionActive =
      this.isNodeTypeSelectableForReport(node, reportBaseNode) && activate;
    if (!activate) {
      node.isSelected = false;
    }
    node.children.forEach((nodeChild) => {
      if (!activate) {
        nodeChild.isSelected = false;
      }
      nodeChild.reportSelectionActive =
        this.isNodeTypeSelectableForReport(nodeChild, reportBaseNode) && activate;
      nodeChild.children.forEach((sub) =>
        this.setReportSelectionModeForNodeChildren(sub, reportBaseNode, activate),
      );
    });
    this.expandNode(node);
  }

  setComparisonReportAssignment(selection: ComparisonSelection, node: OrganizationNode): void {
    node.comparisonReportSelection = selection;
  }

  private async resetCurrentReportSelections(node: OrganizationNode): Promise<void> {
    if (this.organizationIdOfCurrentReportSelection != null) {
      const nodeToDeactivate = this.dataSource.data.find(
        (value) => value.id === this.organizationIdOfCurrentReportSelection,
      );
      if (nodeToDeactivate != null) {
        await this.setReportSelectionModeForNodeChildren(
          nodeToDeactivate,
          nodeToDeactivate.type,
          false,
        );
        await this.setComparisonSelectionModeForNodeChildren(
          nodeToDeactivate,
          nodeToDeactivate.type,
          false,
        );
      }
    }
    this.organizationIdOfCurrentReportSelection =
      node.type === OrganizationNodeType.LOCATION ? node.orgId : node.id;
  }

  private setParentOrganizationSurveySelection(node: OrganizationNode, activate: boolean): void {
    const organizationSurveys = this.getParentNodesOfType(
      OrganizationNodeType.ORGANIZATION_SURVEY,
      node,
    );
    organizationSurveys.forEach((survey) => (survey.reportSelectionActive = activate));
  }

  private getParentNodesOfType(
    type: OrganizationNodeType,
    node: OrganizationNode,
  ): Array<OrganizationNode> {
    const parentNode = this.dataSource.data.find((value) => value.id === node.orgId);
    return parentNode.children.filter(
      (org) => org.type === type && org.surveyState !== SurveyState.OPEN,
    );
  }

  private isNodeTypeSelectableForReport(
    node: OrganizationNode,
    reportBaseNode: OrganizationNodeType,
  ): boolean {
    const selectableNodeTypes: Array<OrganizationNodeType> = [
      OrganizationNodeType.ORGANIZATION_SURVEY,
      OrganizationNodeType.STAFF_SURVEY,
      OrganizationNodeType.PARENTAL_SURVEY,
      OrganizationNodeType.ADMINISTRATION_SURVEY,
      OrganizationNodeType.EVALUATION,
      OrganizationNodeType.CHILD_EVALUATION,
    ];
    selectableNodeTypes.push(reportBaseNode);
    return (
      selectableNodeTypes.includes(node.type) &&
      node.surveyState !== SurveyState.OPEN &&
      node.surveyState !== SurveyState.NO_RESULTS_AVAILABLE
    );
  }

  async openLanguageSelectionPopup(node: OrganizationNode): Promise<void> {
    const modal = await this.modalController.create({
      component: ReportLanguagePopUpComponent,
      componentProps: {
        callbackFunction: (language: Languages) => {
          this.validateAndGenerateReport(node, language);
        },
        languages: [Languages.DE, Languages.EN, Languages.ES, Languages.FR, Languages.PT],
      },
      cssClass: 'report-language-modal',
    });

    return await modal.present();
  }

  public async validateAndGenerateReport(
    node: OrganizationNode,
    language: Languages,
  ): Promise<void> {
    const selectedNodes = this.getSelectedNodes(node);
    const nodeName = this.getNodeNamesForReport(node).join(' • ');
    let reportRequestParams: { [param: string]: string | string[] } = null;
    let url: string;
    switch (this.reportType) {
      case ReportType.ORGANIZATION_REPORT:
        const organizationReportRequest =
          this.validateAndCreateOrganizationReportRequest(selectedNodes);
        reportRequestParams =
          organizationReportRequest === null
            ? null
            : organizationReportRequest.toRequestParameters(nodeName);
        url = `/reports/organization`;
        break;
      case ReportType.LOCATION_REPORT:
        const locationReportRequest = this.validateAndCreateLocationReportRequest(selectedNodes);
        reportRequestParams =
          locationReportRequest === null
            ? null
            : locationReportRequest.toRequestParameters(nodeName);
        url = `/reports/location`;
        break;
      case ReportType.COMPARISON_REPORT:
        const comparisonReportRequest =
          this.validateAndCreateComparisonReportRequest(selectedNodes);
        reportRequestParams =
          comparisonReportRequest === null
            ? null
            : comparisonReportRequest.toRequestParameters(nodeName);
        url = `/reports/comparison`;
        break;
    }

    if (reportRequestParams !== null) {
      const request: GenerateReportRequest = {
        urlSegment: `${url}?${new HttpParams({ fromObject: reportRequestParams }).toString()}`,
        name: this.getNodeNamesForReport(node).join('_'),
      };

      this.loadingUi = await this.loadingController.create(
        Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
      );
      await this.loadingUi.present();
      this.adminService.generateReport(request, language).subscribe(async (resp) => {
        if (resp.success) {
          this.deactivateCurrentReportSelectionMode(node);
          this.showToast(
            this.translate.instant('admin.dashboard.create_report.success_message'),
            'success',
          );
        } else {
          this.showToast(
            this.translate.instant('admin.dashboard.create_report.error_message'),
            'danger',
          );
        }
        await this.loadingUi.dismiss();
        this.isLoading = false;
      });
    } else {
      this.presentReportSelectionErrorModal(this.reportType);
    }
  }

  public hasScales(node: OrganizationNode): boolean {
    return node.type === OrganizationNodeType.EVALUATION && node.scales.length !== 0;
  }

  public isSurvey(node: OrganizationNode): boolean {
    return (
      node.type === OrganizationNodeType.ORGANIZATION_SURVEY ||
      node.type === OrganizationNodeType.ADMINISTRATION_SURVEY ||
      node.type === OrganizationNodeType.PARENTAL_SURVEY ||
      node.type === OrganizationNodeType.STAFF_SURVEY
    );
  }

  private getScaleTypeDefinition(scales: Array<ScaleDefinition>): Array<ScaleTypeDefinition> {
    const scaleTypes: Array<ScaleTypeDefinition> = [];
    scales.forEach((scale) => {
      scaleTypes.push(getScaleTypeFromDefinition(scale));
    });
    return scaleTypes;
  }

  private getSelectedNodes(node: OrganizationNode): Array<OrganizationNode> {
    const flatten = (selected) => {
      return [selected, _.flatMapDeep(selected.children, flatten)];
    };
    const isComparisonReport = this.reportType === ReportType.COMPARISON_REPORT;
    const selectedNodes: Array<OrganizationNode> = _.flatMapDeep(node.children, flatten).filter(
      (selected) =>
        isComparisonReport
          ? selected.comparisonReportSelection === ComparisonSelection.A ||
            selected.comparisonReportSelection === ComparisonSelection.B
          : selected.isSelected,
    );

    if (
      this.reportType === ReportType.LOCATION_REPORT ||
      this.reportType === ReportType.COMPARISON_REPORT
    ) {
      const organizationSurveys = this.getParentNodesOfType(
        OrganizationNodeType.ORGANIZATION_SURVEY,
        node,
      );
      const orgSurveyNodes = organizationSurveys.filter((selected) =>
        isComparisonReport
          ? selected.comparisonReportSelection === ComparisonSelection.A ||
            selected.comparisonReportSelection === ComparisonSelection.B
          : selected.isSelected,
      );

      orgSurveyNodes.forEach((it) => selectedNodes.push(it));
    }

    return selectedNodes;
  }

  private getNodeNamesForReport(node: OrganizationNode): string[] {
    if (node.type === OrganizationNodeType.ORGANIZATION) {
      return [node.name];
    } else if (node.type === OrganizationNodeType.LOCATION) {
      const parentNode = this.dataSource.data.find((value) => value.id === node.orgId);
      return [parentNode.name, node.name];
    }
  }

  private validateAndCreateOrganizationReportRequest(
    selectedNodes: Array<OrganizationNode>,
  ): OrganizationReportDataRequest | null {
    const hasZeroOrOneOrganizationSurvey =
      selectedNodes.filter((node) => node.type === OrganizationNodeType.ORGANIZATION_SURVEY)
        .length <= 1;
    const locIdsFromAdministrationSurveys = selectedNodes
      .filter((node) => node.type === OrganizationNodeType.ADMINISTRATION_SURVEY)
      .map((it) => it.locId);
    const hasNoMoreThanOneAdministrationSurveyPerLocation =
      new Set(locIdsFromAdministrationSurveys).size === locIdsFromAdministrationSurveys.length;
    const allSurveyStatesAreClosed = this.isSurveyStatesAllClosed(selectedNodes);
    const allEvaluationHasSameScales = this.areScalesFromSameType(
      selectedNodes.filter((node) => node.type === OrganizationNodeType.EVALUATION),
    );

    if (
      hasZeroOrOneOrganizationSurvey &&
      hasNoMoreThanOneAdministrationSurveyPerLocation &&
      allSurveyStatesAreClosed &&
      allEvaluationHasSameScales
    ) {
      return this.getReportRequestFromSelectedNodes(selectedNodes);
    } else {
      return null;
    }
  }

  private validateAndCreateLocationReportRequest(
    selectedNodes: Array<OrganizationNode>,
  ): OrganizationReportDataRequest | null {
    const hasZeroOrOneOrganizationSurvey =
      selectedNodes.filter((node) => node.type === OrganizationNodeType.ORGANIZATION_SURVEY)
        .length <= 1;
    const hasZeroOrOneAdministrationSurvey =
      selectedNodes.filter((node) => node.type === OrganizationNodeType.ADMINISTRATION_SURVEY)
        .length <= 1;
    const allSurveyStatesAreClosed = this.isSurveyStatesAllClosed(selectedNodes);
    const allEvaluationHasSameScales = this.areScalesFromSameType(
      selectedNodes.filter((node) => node.type === OrganizationNodeType.EVALUATION),
    );

    if (
      hasZeroOrOneOrganizationSurvey &&
      hasZeroOrOneAdministrationSurvey &&
      allSurveyStatesAreClosed &&
      allEvaluationHasSameScales
    ) {
      return this.getReportRequestFromSelectedNodes(selectedNodes);
    } else {
      return null;
    }
  }

  private validateAndCreateComparisonReportRequest(
    selectedNodes: Array<OrganizationNode>,
  ): ComparisonReportDataRequest | null {
    const organizationSurveySelections = this.getSelectionsForNodeType(
      OrganizationNodeType.ORGANIZATION_SURVEY,
      selectedNodes,
    );
    const hasNoOrganizationSurvey = organizationSurveySelections.length === 0;
    const hasOneOrganizationSurveyPerAssignment = this.hasOneSurveyPerAssignment(
      organizationSurveySelections,
    );

    const administrationSurveySelections = this.getSelectionsForNodeType(
      OrganizationNodeType.ADMINISTRATION_SURVEY,
      selectedNodes,
    );
    const hasNoAdministrationSurvey = administrationSurveySelections.length === 0;
    const hasOneAdministrationSurveyPerAssignment = this.hasOneSurveyPerAssignment(
      administrationSurveySelections,
    );

    const parentalSurveySelections = this.getSelectionsForNodeType(
      OrganizationNodeType.PARENTAL_SURVEY,
      selectedNodes,
    );
    const hasNoParentalSurvey = parentalSurveySelections.length === 0;
    const hasOneParentalSurveyPerAssignment =
      this.hasOneSurveyPerAssignment(parentalSurveySelections);

    const staffSurveySelections = this.getSelectionsForNodeType(
      OrganizationNodeType.STAFF_SURVEY,
      selectedNodes,
    );
    const hasNoStaffSurvey = staffSurveySelections.length === 0;
    const hasOneStaffSurveyPerAssignment = this.hasOneSurveyPerAssignment(staffSurveySelections);

    const childEvaluationSelections = this.getSelectionsForNodeType(
      OrganizationNodeType.CHILD_EVALUATION,
      selectedNodes,
    );
    const hasNoChildEvaluations = childEvaluationSelections.length === 0;
    const hasOneOrMoreChildEvaluationsPerAssignment =
      this.hasOneOrMoreItemsPerAssignment(childEvaluationSelections);

    const evaluationSelections = this.getSelectionsForNodeType(
      OrganizationNodeType.EVALUATION,
      selectedNodes,
    );
    const hasNoEvaluations = evaluationSelections.length === 0;
    const hasOneOrMoreEvaluationsPerAssignment =
      this.hasOneOrMoreItemsPerAssignment(evaluationSelections);

    const isOnlyScaleBasicV2 = this.areEvaluationsOnlyFromScaleTypeBasicV2(
      selectedNodes.filter((node) => node.type === OrganizationNodeType.EVALUATION),
    );

    const allSurveyStatesAreClosed = this.isSurveyStatesAllClosed(selectedNodes);

    if (
      (hasNoOrganizationSurvey || hasOneOrganizationSurveyPerAssignment) &&
      (hasNoAdministrationSurvey || hasOneAdministrationSurveyPerAssignment) &&
      (hasNoParentalSurvey || hasOneParentalSurveyPerAssignment) &&
      (hasNoStaffSurvey || hasOneStaffSurveyPerAssignment) &&
      (hasNoChildEvaluations || hasOneOrMoreChildEvaluationsPerAssignment) &&
      (hasNoEvaluations || hasOneOrMoreEvaluationsPerAssignment) &&
      allSurveyStatesAreClosed &&
      isOnlyScaleBasicV2
    ) {
      return this.getComparisonReportRequestFromSelectedNodes(selectedNodes);
    } else {
      return null;
    }
  }

  private areScalesFromSameType(evaluationNodes: Array<OrganizationNode>): boolean {
    let equals = true;
    if (evaluationNodes.length === 0) {
      return equals;
    }
    const scale = evaluationNodes[0].evaluatedScales;

    evaluationNodes.forEach((evaluation) => {
      equals = scale.every((element, index) => element === evaluation.evaluatedScales[index]);

      if (scale.length !== evaluation.evaluatedScales.length) {
        equals = false;
        return;
      }

      if (!equals) {
        return;
      }
    });
    return equals;
  }

  private areEvaluationsOnlyFromScaleTypeBasicV2(
    evaluationNodes: Array<OrganizationNode>,
  ): boolean {
    const scalesOtherThanV2 = _.flatten(
      evaluationNodes.map((evaluation) => evaluation.evaluatedScales),
    ).filter((it) => it !== ScaleDefinition.GRAZIAS_V2_ZERO_TO_SIX);
    return scalesOtherThanV2.length === 0;
  }

  private getReportRequestFromSelectedNodes(
    selectedNodes: Array<OrganizationNode>,
  ): OrganizationReportDataRequest {
    const surveyIds = selectedNodes
      .filter(
        (node) =>
          node.type === OrganizationNodeType.ORGANIZATION_SURVEY ||
          node.type === OrganizationNodeType.ADMINISTRATION_SURVEY ||
          node.type === OrganizationNodeType.PARENTAL_SURVEY ||
          node.type === OrganizationNodeType.STAFF_SURVEY,
      )
      .map((it) => it.id);
    const childEvaluationIds = selectedNodes
      .filter((node) => node.type === OrganizationNodeType.CHILD_EVALUATION)
      .map((it) => it.evaluationId);
    const evaluationIds = selectedNodes
      .filter((node) => node.type === OrganizationNodeType.EVALUATION)
      .map((it) => it.evaluationId);
    return new OrganizationReportDataRequest(surveyIds, childEvaluationIds, evaluationIds);
  }

  private getComparisonReportRequestFromSelectedNodes(
    selectedNodes: Array<OrganizationNode>,
  ): ComparisonReportDataRequest {
    const isSurvey = (nodeType: OrganizationNodeType) =>
      nodeType === OrganizationNodeType.ORGANIZATION_SURVEY ||
      nodeType === OrganizationNodeType.ADMINISTRATION_SURVEY ||
      nodeType === OrganizationNodeType.PARENTAL_SURVEY ||
      nodeType === OrganizationNodeType.STAFF_SURVEY;
    const isSetA = (selection: ComparisonSelection) => selection === ComparisonSelection.A;
    const isSetB = (selection: ComparisonSelection) => selection === ComparisonSelection.B;

    const surveyIdsSetA = selectedNodes
      .filter((node) => isSurvey(node.type) && isSetA(node.comparisonReportSelection))
      .map((it) => it.id);
    const surveyIdsSetB = selectedNodes
      .filter((node) => isSurvey(node.type) && isSetB(node.comparisonReportSelection))
      .map((it) => it.id);
    const childEvaluationIdsSetA = selectedNodes
      .filter(
        (node) =>
          node.type === OrganizationNodeType.CHILD_EVALUATION &&
          isSetA(node.comparisonReportSelection),
      )
      .map((it) => it.evaluationId);
    const childEvaluationIdsSetB = selectedNodes
      .filter(
        (node) =>
          node.type === OrganizationNodeType.CHILD_EVALUATION &&
          isSetB(node.comparisonReportSelection),
      )
      .map((it) => it.evaluationId);
    const evaluationIdsSetA = selectedNodes
      .filter(
        (node) =>
          node.type === OrganizationNodeType.EVALUATION && isSetA(node.comparisonReportSelection),
      )
      .map((it) => it.evaluationId);
    const evaluationIdsSetB = selectedNodes
      .filter(
        (node) =>
          node.type === OrganizationNodeType.EVALUATION && isSetB(node.comparisonReportSelection),
      )
      .map((it) => it.evaluationId);
    return new ComparisonReportDataRequest(
      new OrganizationReportDataRequest(surveyIdsSetA, childEvaluationIdsSetA, evaluationIdsSetA),
      new OrganizationReportDataRequest(surveyIdsSetB, childEvaluationIdsSetB, evaluationIdsSetB),
    );
  }

  private getSelectionsForNodeType(
    type: OrganizationNodeType,
    nodes: Array<OrganizationNode>,
  ): Array<ComparisonSelection> {
    return nodes
      .filter((node) => node.type === type)
      .filter(
        (node) =>
          node.comparisonReportSelection === ComparisonSelection.A ||
          node.comparisonReportSelection === ComparisonSelection.B,
      )
      .map((it) => it.comparisonReportSelection);
  }

  private hasOneSurveyPerAssignment(selections: Array<ComparisonSelection>): boolean {
    return (
      selections.includes(ComparisonSelection.A) &&
      selections.includes(ComparisonSelection.B) &&
      selections.length === 2
    );
  }

  private hasOneOrMoreItemsPerAssignment(selections: Array<ComparisonSelection>): boolean {
    return selections.includes(ComparisonSelection.A) && selections.includes(ComparisonSelection.B);
  }

  private isSurveyStatesAllClosed(nodes: Array<OrganizationNode>): boolean {
    return nodes.filter((node) => node.surveyState === SurveyState.OPEN).length === 0;
  }
}
