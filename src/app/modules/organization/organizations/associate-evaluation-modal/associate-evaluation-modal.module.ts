import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { AssociateEvaluationModalPage } from './associate-evaluation-modal.page';
import { TranslateModule } from '@ngx-translate/core';
import { GraziasMissingTranslationHandler } from '../../../../utils/missingTranslationHandler';
import { SharedModule } from '../../../shared/shared.module';

const routes: Routes = [
  {
    path: '',
    component: AssociateEvaluationModalPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
    SharedModule,
  ],
  exports: [AssociateEvaluationModalPage],
  declarations: [AssociateEvaluationModalPage],
})
export class AssociateEvaluationModalPageModule {}
