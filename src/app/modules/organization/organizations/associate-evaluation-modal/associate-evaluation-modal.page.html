<ion-content class="design-v2">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col class="ion-no-padding">
        <h1>{{ getTitle() }}</h1>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col class="ion-no-padding">
        <ion-text class="message">
          {{ 'organization.associate-evaluations.message' | translate}}
        </ion-text>
      </ion-col>
    </ion-row>
    <div class="spacer"></div>
    <ion-row>
      <ion-col class="ion-no-padding">
        <form [formGroup]="customerServiceIdForm" (ngSubmit)="associate()">
          <app-input-with-heading
            formControlName="customerServiceId"
            type="text"
            [maxLength]="customerServiceIdLengthNoPrefix"
            [setFocus]="true"
            [inputValue]="customerServiceIdForm.controls.customerServiceId.value.toUpperCase()"
            [errorMessage]="null"
            prefix="#ERH-"
          >
          </app-input-with-heading>
        </form>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col class="ion-no-padding result-message">
        <ion-text>{{ evaluationInfoResultMessage }}</ion-text>
        <ion-text *ngIf="isAlreadyAssociated" class="associated-message"
          >{{ 'organization.associate-evaluations.already-associated' | translate }}</ion-text
        >
      </ion-col>
    </ion-row>
    <div class="spacer"></div>
    <ion-row class="bottom-row">
      <ion-col>
        <app-primary-button
          expand="block"
          (onClick)="dismissModal()"
          [isDisabled]="false"
          [isLoading]="false"
          [border]="true"
          [label]="'global.button.cancel' | translate"
        >
        </app-primary-button>
      </ion-col>
      <ion-col>
        <app-primary-button
          type="submit"
          expand="block"
          (onClick)="associate()"
          [isDisabled]="!isSubmitButtonActive"
          [isLoading]="false"
          [label]="'organization.associate-evaluations.submit' | translate"
          [buttonType]="buttonType.GRADIENT"
        >
        </app-primary-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
