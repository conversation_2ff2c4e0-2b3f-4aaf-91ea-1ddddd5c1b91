import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { IsoDatePipe } from 'src/app/pipes/iso-date.pipe';
import { AdminService, EvaluationGroupInformation } from '../../../../services/admin/admin.service';
import { PrimaryButtonType } from '../../../shared/components/primary-button/primary-button.component';
import { EvaluationType } from '../utils';

@Component({
  selector: 'app-associate-evaluation-modal',
  templateUrl: './associate-evaluation-modal.page.html',
  styleUrls: [
    './associate-evaluation-modal.page.scss',
    '../report-selection-error-modal/common.scss',
  ],
})
export class AssociateEvaluationModalPage implements OnInit {
  public buttonType = PrimaryButtonType;
  public customerServiceIdForm: UntypedFormGroup;
  public evaluationInfoResultMessage: string;
  public isSubmitButtonActive = false;
  public isAlreadyAssociated = false;
  public customerServiceIdLengthNoPrefix = 14;
  private evaluationInformation: EvaluationGroupInformation;

  @Input() groupId: number;
  @Input() type: EvaluationType;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private adminService: AdminService,
    private modalController: ModalController,
    private translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.customerServiceIdForm = this.formBuilder.group({
      customerServiceId: ['', [Validators.required]],
    });

    this.registerOnChangeObservable();
  }

  private registerOnChangeObservable(): void {
    const control = this.customerServiceIdForm.get('customerServiceId');
    control.valueChanges.subscribe((val) => {
      this.isSubmitButtonActive = false;
      this.clearEvaluationInfoResultMessage();
      if (val.toString().length == this.customerServiceIdLengthNoPrefix) {
        this.findEvaluationByCustomerServiceId(val.toString());
      }
    });
  }

  public async dismissModal(submitted?: boolean): Promise<void> {
    await this.modalController.dismiss({ submitted });
  }

  public associate(): void {
    if (this.evaluationInformation != null && this.groupId != null) {
      this.evaluationInformation.groupId = this.groupId;
      this.adminService
        .associateEvaluationWithGroup(this.evaluationInformation)
        .subscribe(async (result) => {
          if (result.success) {
            await this.dismissModal(true);
          }
        });
    }
  }

  public getTitle(): string {
    switch (this.type) {
      case EvaluationType.CHILD_EVALUATION: {
        return this.translate.instant('organization.associate-child-evaluations.title');
      }
      case EvaluationType.EVALUATION: {
        return this.translate.instant('organization.associate-evaluations.title');
      }
    }
  }

  private findEvaluationByCustomerServiceId(customerServiceId: string): void {
    if (!/[a-z]/.test(customerServiceId)) {
      this.adminService
        .getEvalInfoByCustomerServiceID(this.type, customerServiceId)
        .subscribe(async (result) => {
          if (result.success && result.data.result != null) {
            this.evaluationInformation = result.data.result;
            this.isSubmitButtonActive = true;

            const dateOfSurvey = new IsoDatePipe(this.translate.currentLang).transform(
              result.data.result.dateOfSurvey,
            );
            this.evaluationInfoResultMessage = this.translate.instant(
              'organization.associate-evaluations.evaluator-and-date-info',
              {
                dateOfSurvey,
                evaluator: result.data.result.evaluator,
              },
            );

            this.isAlreadyAssociated = result.data.result.groupId != null;
          } else {
            this.clearEvaluationInfoResultMessage();
            this.evaluationInfoResultMessage = this.translate.instant(
              'organization.associate-evaluations.no-evaluation',
            );
          }
        });
    }
  }

  private clearEvaluationInfoResultMessage(): void {
    this.evaluationInfoResultMessage = null;
    this.isAlreadyAssociated = false;
  }
}
