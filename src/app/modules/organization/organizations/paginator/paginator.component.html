<div class="container">
  <button (click)="paginate(-1)" class="paging-action">
    <ion-img src="assets/img/left_double.png" alt="Start"></ion-img>
  </button>
  <button (click)="paginate(currentPage - 1)" class="paging-action">
    <ion-img src="assets/img/left.png" alt="Left"></ion-img>
  </button>
  <div class="page-display-container">
    <p class="current-page">{{ currentPage }}</p>
    <p class="page-display-divider">/</p>
    <p>{{ totalPageCount }}</p>
  </div>
  <button (click)="paginate(currentPage + 1)" class="paging-action">
    <ion-img src="assets/img/right.png" alt="Right"></ion-img>
  </button>
  <button (click)="paginate(totalPageCount)" class="paging-action">
    <ion-img src="assets/img/right_double.png" alt="End"></ion-img>
  </button>
</div>
