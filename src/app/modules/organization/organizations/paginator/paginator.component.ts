import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-paginator',
  templateUrl: './paginator.component.html',
  styleUrls: ['./paginator.component.scss'],
})
export class PaginatorComponent {
  @Input() currentPage: number;
  @Input() totalPageCount: number;
  @Output() changeEvent = new EventEmitter<number>();

  constructor() {}

  paginate(eventType: number): void {
    this.changeEvent.emit(eventType);
  }
}
