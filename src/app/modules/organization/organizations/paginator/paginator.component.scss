@import 'src/theme/_variables_v2.scss';

.container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-left: 94px;
  padding-right: 79px;
  padding-bottom: 4vw;
}

.page-display-container {
  border: 1px solid black;
  cursor: default;
  font-size: 1.1rem;
}

.page-display-divider {
  padding-left: 0.2rem;
  padding-right: 0.2rem;
}

.current-page {
  font-weight: bold;
}

.paging-action {
  border: solid $color-light-gray 1px;
}

.paging-action:hover {
  border: 1px solid black;
  color: black;
}

.paging-action,
.page-display-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;

  border-radius: 8px;
  background-color: white;
  height: 40px;
  min-width: 40px;
  padding: 0.5vw;
}

ion-img {
  height: 13px;
  width: 13px;
}
