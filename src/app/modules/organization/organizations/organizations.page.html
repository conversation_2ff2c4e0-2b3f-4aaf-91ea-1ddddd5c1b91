<div class="main-content">
  <ion-header class="header-v2">
    <ion-toolbar class="toolbar-v2">
      <div class="flex">
        <ion-title>{{ 'organization.title' | translate}}</ion-title>
        <app-primary-button
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="navigateOrganizationForm()"
          [isDisabled]="false"
          [isLoading]="false"
          [label]="'organization.new-organization' | translate"
        >
        </app-primary-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <ion-content class="design-v2 content">
    <div class="searchbar-container">
      <app-search-bar (tagsChanged)="this.onTagsChanged($event)"> </app-search-bar>
    </div>

    <!-- Empty view without search -->
    <app-empty-view
      *ngIf="dataSource.data.length === 0 && !isLoading && !isSearchActive"
      [title]="'organization.empty_view.title' | translate"
      [text]="'organization.empty_view.text' | translate"
      imagePath="assets/svg/big_empty_treeview.svg"
    ></app-empty-view>

    <!-- Empty view with search -->
    <app-empty-view
      *ngIf="dataSource.data.length === 0 && !isLoading && isSearchActive"
      [title]="'organization.empty_view.title_search' | translate"
      [text]="'organization.empty_view.text_search' | translate"
      imagePath="assets/svg/empty_view_search.svg"
    ></app-empty-view>
    <!-- Survey -->
    <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="tree-padding">
      <mat-tree-node
        [ngClass]="getSurveyOrEvaluationNodeCssClass(node)"
        *matTreeNodeDef="let node; when: isSurveyOrChildEvaluatorAccess"
      >
        <ion-col class="column ion-no-padding">
          <div class="image-text-flex">
            <app-comparison-toggle
              *ngIf="node.comparisonReportSelectionActive"
              (selectionChange)="setComparisonReportAssignment($event, node)"
            ></app-comparison-toggle>
            <ion-checkbox
              *ngIf="node.reportSelectionActive"
              [(ngModel)]="node.isSelected"
              class="check-box"
            ></ion-checkbox>
            <b class="title-survey list-item" (click)="editOrganizationalData(node)">
              <span class="main-node-name">{{node.name}}</span>
              <span class="node-detail">
                <span [class]="getSeparatorCss(node)">|</span>
                {{ getNodeDetail(node) }}
              </span>
              <span *ngIf="isSurvey(node)" class="list-item-survey-date">
                {{ getSurveyPeriod(node) }}
              </span>
              <span *ngIf="isSurveyStateExpiredWithoutResults(node)">
                <span class="icon-wrapper">
                  <ion-badge class="no-results-badge">
                    <span class="no-results-available-message">
                      {{ 'surveys.no_results' | translate}}
                    </span>
                  </ion-badge>
                </span>
              </span>
              <span *ngIf="hasScales(node)" class="scales">
                <span class="node-detail">
                  <span [class]="getSeparatorCss(node)">|</span>
                  <span class="list-item-sub-title">
                    {{getScaleAgeFromDefinition(node.scaleAgeDefinition)}}
                  </span>
                  <span class="icon-wrapper">
                    <span
                      *ngFor="let scale of node.scales"
                      [ngClass]="getScaleIconFrom(scale)"
                    ></span>
                  </span>
                </span>
              </span>
            </b>
          </div>
          <ion-badge
            *ngIf="node.tags.length"
            class="custom-badge flex"
            [matTooltip]="getTooltipTextFromArray(node.tags)"
            >{{ node.tags.length }}</ion-badge
          >
          <span *ngIf="showCopyToClipboard(node)" class="ion-activatable">
            <ion-img
              class="copy-icon"
              src="assets/svg/copy.svg"
              (click)="copyToClipboard(node)"
            ></ion-img>
            <ion-ripple-effect type="unbounded"></ion-ripple-effect>
          </span>
        </ion-col>
      </mat-tree-node>

      <mat-nested-tree-node
        [id]="getIdIfOrganizationsNode(node)"
        [class]="isOrganizationNode(node) ? 'tree-node' : 'tree-node-child'"
        *matTreeNodeDef="let node"
      >
        <ion-col class="column ion-no-padding">
          <div class="list-item">
            <ion-img
              class="toggle-icon"
              matTreeNodeToggle
              [src]="treeControl.isExpanded(node) ? 'assets/svg/disclosure_up.svg' : 'assets/svg/disclosure_down.svg'"
            >
            </ion-img>
            <div class="title" (click)="editOrganizationalData(node)">{{node.name}}</div>
            <div class="flex-container">
              <app-primary-button
                *ngIf="hasReportControl(node)"
                class="report-control"
                [label]="'global.button.cancel' | translate"
                [border]="true"
                [small]="true"
                (click)="deactivateCurrentReportSelectionMode(node)"
                [buttonType]="buttonType.BRIGHT"
              ></app-primary-button>
              <app-primary-button
                *ngIf="hasReportControl(node)"
                class="report-control"
                [label]="'global.create' | translate"
                [border]="true"
                [small]="true"
                (click)="openLanguageSelectionPopup(node)"
                [buttonType]="buttonType.DARK"
              ></app-primary-button>

              <ion-badge
                *ngIf="node.tags.length"
                class="custom-badge"
                [matTooltip]="getTooltipTextFromArray(node.tags)"
                >{{ node.tags.length }}</ion-badge
              >
              <app-create-menu
                *ngIf="getClickActions(node.type) != null"
                [actions]="getClickActions(node.type)"
                (menuEntryClicked)="handleClickAction(node, $event)"
              >
              </app-create-menu>
            </div>
          </div>
          <div
            class="tree-indent"
            *ngIf="hasChild(node)"
            [class.example-tree-invisible]="!treeControl.isExpanded(node)"
            role="group"
          >
            <ng-container matTreeNodeOutlet></ng-container>
          </div>
          <div
            *ngIf="!hasChild(node)"
            [class.example-tree-invisible]="!treeControl.isExpanded(node)"
            role="group"
          >
            <div class="empty-view">{{'organization.no_entry' | translate}}</div>
          </div>
        </ion-col>
      </mat-nested-tree-node>
    </mat-tree>
    <app-paginator
      [currentPage]="getCurrentPage()"
      [totalPageCount]="getLastPageNumber()"
      (changeEvent)="changePage($event)"
    ></app-paginator>
  </ion-content>
</div>
