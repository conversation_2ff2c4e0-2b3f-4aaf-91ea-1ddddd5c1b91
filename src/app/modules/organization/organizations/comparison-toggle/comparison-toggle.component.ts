import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-comparison-toggle',
  templateUrl: './comparison-toggle.component.html',
  styleUrls: ['./comparison-toggle.component.scss'],
})
export class ComparisonToggleComponent {
  @Output() selectionChange = new EventEmitter<ComparisonSelection>();

  public comparisonToggleState = ComparisonSelection.NONE;

  public toggleA(): void {
    if (this.comparisonToggleState === ComparisonSelection.A) {
      this.comparisonToggleState = ComparisonSelection.NONE;
    } else {
      this.comparisonToggleState = ComparisonSelection.A;
    }
    this.selectionChange.emit(this.comparisonToggleState);
  }

  public toggleB(): void {
    if (this.comparisonToggleState === ComparisonSelection.B) {
      this.comparisonToggleState = ComparisonSelection.NONE;
    } else {
      this.comparisonToggleState = ComparisonSelection.B;
    }
    this.selectionChange.emit(this.comparisonToggleState);
  }

  public isAActive(): boolean {
    return this.comparisonToggleState === ComparisonSelection.A;
  }

  public isBActive(): boolean {
    return this.comparisonToggleState === ComparisonSelection.B;
  }

  public getIconCssClass(assignment: string): string {
    const cssBaseClass = 'selection-icon';
    switch (assignment) {
      case 'A':
        return this.isAActive() ? cssBaseClass + ' one active' : cssBaseClass + ' one';
      case 'B':
        return this.isBActive() ? cssBaseClass + ' two active' : cssBaseClass + ' two';
    }
  }
}

export enum ComparisonSelection {
  A = 'A',
  B = 'B',
  NONE = 'NONE',
}
