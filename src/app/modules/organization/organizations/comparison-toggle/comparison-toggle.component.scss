.toggle-container {
  width: 4rem;
  margin-left: 0.8rem;
}

ion-col {
  padding-left: 0;
  padding-right: 0;
}

.selection-icon {
  height: 1.5rem;
  width: 1.5rem;

  &.one {
    background: url('/assets/svg/checkbox_one_inactive.svg') top center / contain no-repeat;
    &.active {
      background: url('/assets/svg/checkbox_one_active.svg') top center / contain no-repeat;
    }
  }

  &.two {
    background: url('/assets/svg/checkbox_two_inactive.svg') top center / contain no-repeat;
    &.active {
      background: url('/assets/svg/checkbox_two_active.svg') top center / contain no-repeat;
    }
  }
}
