import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ActionType } from '../utils';

@Component({
  selector: 'app-create-menu',
  templateUrl: './create-menu.component.html',
  styleUrls: ['./create-menu.component.scss'],
})
export class CreateMenuComponent {
  @Input() actions: [ActionType];
  @Output() menuEntryClicked = new EventEmitter<ActionType>();

  onMenuEntryClick(type: ActionType): void {
    this.menuEntryClicked.emit(type);
  }
}
