<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{ 'organization.title' | translate }}</b>
      </ion-row>
      <ion-title>{{ getTitleByType(type) | translate }}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>
<ion-content #content class="design-v2 content">
  <ion-grid class="indentation" *ngIf="!isLoading">
    <div class="horizontal-spacer"></div>
    <app-survey-detail-info-box
      [surveyAccessId]="getSurveyAccessToken()"
      [organization]="surveyOrganization"
      [location]="surveyLocation"
      [group]="surveyGroup"
      [createdAt]="surveyCreatedAt"
      [tokenRegeneratedAt]="surveyUpdatedAt"
      [availableAnswers]="availableAnswers"
      [surveyType]="getSurveyType(type)"
    >
    </app-survey-detail-info-box>
    <div class="horizontal-spacer"></div>
    <ion-row>
      <ion-col>
        <form [formGroup]="surveyForm" (ngSubmit)="submitForm()">
          <ion-grid class="form-spacing">
            <ion-row>
              <ion-col>
                <app-questionnaire-token-box
                  [isSurveyTokenLoading]="isSurveyTokenLoading"
                  [token]="surveyToken"
                  (onRefresh)="refreshToken()"
                >
                </app-questionnaire-token-box>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col>
                <app-datepicker-with-heading
                  formControlName="startDate"
                  [label]="'organization.survey.start' | translate"
                  [errorMessage]="startDateErrorMessage"
                  [inputValue]="surveyForm.controls.startDate.value"
                >
                </app-datepicker-with-heading>
              </ion-col>
              <ion-col>
                <app-datepicker-with-heading
                  formControlName="endDate"
                  [label]="'organization.survey.end' | translate"
                  [errorMessage]="endDateErrorMessage"
                  [inputValue]="surveyForm.controls.endDate.value"
                >
                </app-datepicker-with-heading>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="contactMail"
                  type="text"
                  [label]="'global.placeholder.mail' | translate"
                  [inputValue]="surveyForm.controls.contactMail.value"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-tagging [tags]="tags" (tagsChanged)="onTagsChanged($event)"></app-tagging>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-textarea-with-heading
                  formControlName="notes"
                  [label]="'organization.upsert-organization.placeholder.notes' | translate"
                  [inputValue]="surveyForm.controls.notes.value"
                  rows="7"
                >
                </app-textarea-with-heading>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col size="6"></ion-col>
              <ion-col size="3">
                <app-primary-button
                  expand="block"
                  (onClick)="navigateBack()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'global.button.cancel' | translate"
                >
                </app-primary-button>
              </ion-col>
              <ion-col size="3">
                <app-primary-button
                  type="submit"
                  expand="block"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [label]="getSubmitButtonLabel()"
                  [buttonType]="buttonType.GRADIENT"
                >
                </app-primary-button>
              </ion-col>
            </ion-row>
            <div class="bottom-safe-area"></div>
          </ion-grid>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
