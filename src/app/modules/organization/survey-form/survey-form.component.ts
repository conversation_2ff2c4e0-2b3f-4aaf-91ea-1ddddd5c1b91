import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import {
  AdminService,
  CreateSurveyRequest,
  OrganizationStructureNamesRequest,
  Survey,
  SurveyTokenRequest,
} from '../../../services/admin/admin.service';
import { SurveyTokenService, SurveyType } from '../../../services/jwt/survey-token.service';
import { AlertButtonState, Utils } from '../../../utils/utils';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import {
  OrganizationNode,
  OrganizationRepository,
  SurveyUrlParam,
} from '../organizations.repository';
import { OrganizationNodeType } from '../organizations/utils';
import { UpsertStrategy } from '../upsert-location-form/upsert-location-form.page';

@Component({
  selector: 'app-survey-form',
  templateUrl: './survey-form.component.html',
  styleUrls: ['./survey-form.component.scss'],
})
export class SurveyFormComponent implements OnInit, AfterViewInit {
  constructor(
    public navController: NavController,
    public translate: TranslateService,
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    private loadingController: LoadingController,
    private adminService: AdminService,
    private toastController: ToastController,
    private organizationRepo: OrganizationRepository,
    private alertController: AlertController,
    private surveyTokenService: SurveyTokenService,
  ) {}

  public upsertStrategy: UpsertStrategy;
  public surveyId: number;
  public orgId: number;
  public locId: number;
  public groupId: number;
  public type: OrganizationNodeType;
  public surveyCreatedAt: Date;
  public surveyUpdatedAt: Date;
  public surveyOrganization: string;
  public surveyLocation: string;
  public surveyGroup: string;
  public startDateErrorMessage?: string;
  public endDateErrorMessage?: string;
  public surveyToken = '';
  public isSurveyTokenLoading = false;
  public isLoading = true;
  public buttonType = PrimaryButtonType;
  public tags: string[] = [];
  public availableAnswers = '-';
  @ViewChild('content') content: any;

  private loadingUi: HTMLIonLoadingElement = null;

  private pageBeforeNavigation: number = 1;

  public surveyForm = this.formBuilder.group({
    startDate: ['', [Validators.required]],
    endDate: ['', [Validators.required]],
    contactMail: [''],
    notes: [''],
  });

  private static getSurveyTypeFromUrl(type: string): OrganizationNodeType {
    switch (type) {
      case SurveyUrlParam.organization:
        return OrganizationNodeType.ORGANIZATION_SURVEY;
      case SurveyUrlParam.administration:
        return OrganizationNodeType.ADMINISTRATION_SURVEY;
      case SurveyUrlParam.parental:
        return OrganizationNodeType.PARENTAL_SURVEY;
      case SurveyUrlParam.staff:
        return OrganizationNodeType.STAFF_SURVEY;
    }
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.surveyId = +params.surveyId; // (+) converts string 'id' to a number
      this.orgId = +params.orgId;
      this.locId = +params.locationId;
      this.groupId = +params.groupId;
      this.type = SurveyFormComponent.getSurveyTypeFromUrl(params.surveyType);
      if (this.surveyId !== -1) {
        this.upsertStrategy = UpsertStrategy.UPDATE;
        this.fetchAndPrefillFormData();
        this.getSurveyInfrastructureInfoBySurveyId();
      } else {
        this.isLoading = false;
        this.surveyForm.controls.startDate.setValue(new Date(Date.now()));
        this.surveyForm.controls.endDate.setValue(new Date(Date.now()));
        this.generateToken(false);
        this.upsertStrategy = UpsertStrategy.CREATE;
        this.getInfrastructureByEntityId();
      }
    });

    this.route.queryParams.subscribe(params => {
      this.pageBeforeNavigation = +params['page'];
    });
  }

  ngAfterViewInit(): void {
    this.content.scrollToTop();
  }

  getSurveyInfrastructureInfoBySurveyId(): void {
    this.adminService.getInfrastructureBySurveyId(this.surveyId).subscribe(async (resp) => {
      if (resp.success) {
        this.surveyOrganization = resp.data.result.organizationName;
        this.surveyLocation = resp.data.result.locationName;
        this.surveyGroup = resp.data.result.groupName;
      } else {
        await this.showErrorMessage(true);
      }
    });
  }

  getInfrastructureByEntityId(): void {
    const request = new OrganizationStructureNamesRequest();
    request.organizationId = this.orgId === -1 ? null : this.orgId;
    request.locationId = this.locId === -1 ? null : this.locId;
    request.groupId = this.groupId === -1 ? null : this.groupId;

    this.adminService.getInfrastructureByEntityId(request).subscribe(async (resp) => {
      if (resp.success) {
        this.surveyOrganization = resp.data.result.organizationName;
        this.surveyLocation = resp.data.result.locationName;
        this.surveyGroup = resp.data.result.groupName;
      } else {
        await this.showErrorMessage(true);
      }
    });
  }

  getSurveyAccessToken(): string {
    // TODO: KFU21-X
    return this.upsertStrategy === UpsertStrategy.UPDATE ? '#ERH xxxx xxxx xxxx xxxx' : '-';
  }

  async refreshToken(): Promise<void> {
    const alert = await this.alertController.create(
      Utils.getAlertDialogConfig_v2(
        this.translate.instant('surveyForm.refreshTokenConfirmation.title'),
        this.translate.instant('SurveyForm.refreshTokenConfirmation.message'),
        this.translate.instant('SurveyForm.refreshTokenConfirmation.confirmButton'),
        this.translate.instant('global.alert.confirm.cancel'),
        AlertButtonState.OK,
        AlertButtonState.NONE,
        () =>
          this.upsertStrategy === UpsertStrategy.CREATE
            ? this.generateToken(true)
            : this.refreshTokenRemotely(),
      ),
    );
    await alert.present();
  }

  getSurveyType(type: OrganizationNodeType): SurveyType {
    switch (type) {
      case OrganizationNodeType.ORGANIZATION_SURVEY:
        return SurveyType.ORGANIZATION_SURVEY;
      case OrganizationNodeType.ADMINISTRATION_SURVEY:
        return SurveyType.ADMINISTRATION_SURVEY;
      case OrganizationNodeType.PARENTAL_SURVEY:
        return SurveyType.PARENTAL_SURVEY;
      case OrganizationNodeType.STAFF_SURVEY:
        return SurveyType.STAFF_SURVEY;
    }
  }

  getSurveyTypeString(type: OrganizationNodeType): string {
    switch (type) {
      case OrganizationNodeType.ORGANIZATION_SURVEY:
        return 'ORGANIZATION_SURVEY';
      case OrganizationNodeType.ADMINISTRATION_SURVEY:
        return 'ADMINISTRATION_SURVEY';
      case OrganizationNodeType.PARENTAL_SURVEY:
        return 'PARENTAL_SURVEY';
      case OrganizationNodeType.STAFF_SURVEY:
        return 'STAFF_SURVEY';
    }
  }

  getTitleByType(type: OrganizationNodeType): string {
    return OrganizationNodeType.getTranslationKey(type);
  }

  navigateBack(): void {
    this.navController.navigateRoot(`admin/organizations?initialPage=${this.pageBeforeNavigation}`);
  }

  getSubmitButtonLabel(): string {
    return this.upsertStrategy === UpsertStrategy.UPDATE
      ? this.translate.instant('global.save')
      : this.translate.instant('global.create');
  }

  onTagsChanged(tags: string[]): void {
    this.tags = tags;
  }

  async submitForm(): Promise<void> {
    this.validateDateRange();
    if (this.surveyForm.valid && this.surveyToken !== '') {
      const request = new CreateSurveyRequest();
      request.validFrom = new Date(this.surveyForm.controls.startDate.value);
      request.validTo = new Date(this.surveyForm.controls.endDate.value);
      request.email = this.surveyForm.controls.contactMail.value;
      request.note = this.surveyForm.controls.notes.value;
      request.token = this.surveyToken;
      request.tags = this.tags;
      if (this.type === OrganizationNodeType.ORGANIZATION_SURVEY) {
        request.entityId = this.orgId;
      } else if (
        this.type === OrganizationNodeType.ADMINISTRATION_SURVEY ||
        this.type === OrganizationNodeType.PARENTAL_SURVEY
      ) {
        request.entityId = this.locId;
      } else if (this.type === OrganizationNodeType.STAFF_SURVEY) {
        request.entityId = this.groupId;
      }
      request.type = this.getSurveyTypeString(this.type);
      this.upsertStrategy === UpsertStrategy.CREATE
        ? this.createSurvey(request)
        : this.updateSurvey(request);
    } else {
      this.validateDateRange();
    }
  }

  validateDateRange(): void {
    const start = new Date(this.surveyForm.controls.startDate.value);
    const end = new Date(this.surveyForm.controls.endDate.value);

    if (start >= end) {
      this.surveyForm.setErrors({ range: true });
      this.startDateErrorMessage = this.translate.instant('organization.survey.error-message');
      this.endDateErrorMessage = '';
    } else {
      this.surveyForm.setErrors(null);
      this.startDateErrorMessage = null;
      this.endDateErrorMessage = null;
    }
  }

  async showErrorMessage(shouldReturn: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('global.error.generic'),
      color: 'danger',
      position: 'bottom',
      duration: 10000,
      cssClass: 'global-toast',
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }

  private async refreshTokenRemotely(): Promise<void> {
    this.isSurveyTokenLoading = true;
    this.adminService.refreshSurveyToken(this.surveyId).subscribe(async (resp) => {
      if (resp.success) {
        this.surveyToken = resp.data.survey.token;
        await this.showSuccessMessage();
      } else {
        await this.showErrorMessage(true);
      }
      this.isSurveyTokenLoading = false;
    });
  }

  private async generateToken(refresh: boolean): Promise<void> {
    this.isSurveyTokenLoading = true;
    const request = new SurveyTokenRequest();

    request.type = this.getSurveyTypeString(this.type);
    if (this.type === OrganizationNodeType.ORGANIZATION_SURVEY) {
      request.entityId = this.orgId;
    } else if (
      this.type === OrganizationNodeType.ADMINISTRATION_SURVEY ||
      this.type === OrganizationNodeType.PARENTAL_SURVEY
    ) {
      request.entityId = this.locId;
    } else if (this.type === OrganizationNodeType.STAFF_SURVEY) {
      request.entityId = this.groupId;
    }
    this.adminService.generateSurveyToken(request).subscribe(async (resp) => {
      if (resp.success) {
        this.surveyToken = resp.data.token;
        if (refresh) {
          await this.showSuccessMessage();
        }
      } else {
        await this.showErrorMessage(true);
      }
      this.isSurveyTokenLoading = false;
    });
  }

  private async showSuccessMessage(): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('surveyToken.refresh-success-message'),
      color: 'success',
      position: 'bottom',
      duration: 5000,
      cssClass: 'global-toast',
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }

  private async fetchAndPrefillFormData(): Promise<void> {
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    this.isLoading = true;
    await this.loadingUi.present();
    this.adminService.getSurveyById(this.surveyId).subscribe(async (resp) => {
      if (resp.success) {
        const tags = resp.data.survey.tags;
        if (typeof tags !== 'undefined' && tags.length > 0) {
          this.tags = tags;
        }
        this.availableAnswers = resp.data.survey.numberOfResults.toString();
        this.surveyCreatedAt = resp.data.survey.createdAt;
        this.surveyUpdatedAt = this.surveyTokenService.getTokenIssuedAtDate(resp.data.survey.token);
        await this.getOrganizationNodeType(resp.data.survey.type.toString());
        this.prefillSurveyData(resp.data.survey);
      } else {
        await this.showErrorMessage(true);
      }
      await this.loadingUi.dismiss();
      this.isLoading = false;
    });
  }

  private prefillSurveyData(survey: Survey): void {
    this.surveyForm.controls.startDate.setValue(survey.validFrom);
    this.surveyForm.controls.endDate.setValue(survey.validUntil);
    this.surveyForm.controls.contactMail.setValue(survey.email);
    this.surveyForm.controls.notes.setValue(survey.note);
    this.surveyToken = survey.token;
    this.organizationRepo.newlyCreatedNode = {
      name: this.translate.instant(this.getTitleByType(this.type)),
      id: survey.id,
      tags: survey.tags,
      orgId: this.orgId,
      locId: this.locId,
      groupId: this.groupId,
      type: this.type,
      level: -1,
      children: [],
    } as OrganizationNode;
  }

  private async getOrganizationNodeType(type: string): Promise<void> {
    switch (type) {
      case 'ORGANIZATION_SURVEY':
        this.type = OrganizationNodeType.ORGANIZATION_SURVEY;
        break;
      case 'ADMINISTRATION_SURVEY':
        this.type = OrganizationNodeType.ADMINISTRATION_SURVEY;
        break;
      case 'PARENTAL_SURVEY':
        this.type = OrganizationNodeType.PARENTAL_SURVEY;
        break;
      case 'STAFF_SURVEY':
        this.type = OrganizationNodeType.STAFF_SURVEY;
        break;
    }
  }

  private createSurvey(request: CreateSurveyRequest): void {
    this.adminService.createSurvey(request).subscribe(async (result) => {
      if (result.success) {
        this.organizationRepo.newlyCreatedNode = {
          name: this.translate.instant(this.getTitleByType(this.type)),
          id: result.data.survey.id,
          tags: result.data.survey.tags,
          orgId: this.orgId,
          locId: this.locId,
          groupId: this.groupId,
          type: this.type,
          level: -1,
          children: [],
        } as OrganizationNode;
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }

  private updateSurvey(request: CreateSurveyRequest): void {
    this.adminService.updateSurvey(request, this.surveyId).subscribe(async (result) => {
      if (result.success) {
        this.organizationRepo.newlyCreatedNode = {
          name: this.translate.instant(this.getTitleByType(this.type)),
          id: this.surveyId,
          tags: this.tags,
          orgId: this.orgId,
          locId: this.locId,
          groupId: this.groupId,
          type: this.type,
          level: -1,
          children: [],
        } as OrganizationNode;
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }
}
