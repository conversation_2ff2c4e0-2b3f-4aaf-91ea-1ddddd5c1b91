<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'organization.title' | translate}}</b>
      </ion-row>
      <ion-title>{{getToolbarTitle()}}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content #content class="design-v2 content">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col>
        <form [formGroup]="upsertGroupForm" (ngSubmit)="submitForm()" class="input-container">
          <ion-grid class="form-spacing">
            <app-upsert-form-info-box
              [parentTitle]="'organization.location.title' | translate"
              [parentName]="locationName"
              [sectionTitle1]="'organization.upsert-form-info-box.location-management' | translate"
              [sectionValue1]="locationManagmentName"
              [sectionTitle2]="'organization.upsert-form-info-box.group_created_at' | translate"
              [sectionValue2]="groupCreatedAt"
            ></app-upsert-form-info-box>

            <div class="horizontal-spacer"></div>

            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="groupName"
                  type="text"
                  [label]="'organization.upsert_group.group_name' | translate"
                  [setFocus]="true"
                  [inputValue]="upsertGroupForm.controls.groupName.value"
                  [errorMessage]="groupNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col>
                <app-input-with-heading
                  formControlName="numberOfEvaluableEducators"
                  type="number"
                  [label]="'organization.upsert_group.number_of_evaluable_educators' | translate"
                  [inputValue]="upsertGroupForm.controls.numberOfEvaluableEducators.value"
                  [errorMessage]="numberOfEvaluableEducatorsErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <app-dropdown-with-heading
                  formControlName="childrensAgeRange"
                  [label]="'organization.upsert_group.childrens_age_range' | translate"
                  [errorMessage]="childrensAgeRangeErrorMessage"
                  [values]="childAgeRanges"
                  [inputValue]="upsertGroupForm.controls.childrensAgeRange.value"
                  [interfaceOptions]="childAgeRangesAlertOptions"
                >
                </app-dropdown-with-heading>
              </ion-col>
              <ion-col></ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-tagging [tags]="tags" (tagsChanged)="onTagsChanged($event)"></app-tagging>
              </ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col>
                <app-textarea-with-heading
                  formControlName="notes"
                  [label]="'organization.upsert_group.notes' | translate"
                  [inputValue]="upsertGroupForm.controls.notes.value"
                  [errorMessage]="notesErrorMessage"
                  rows="7"
                >
                </app-textarea-with-heading>
              </ion-col>
            </ion-row>
            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col size="6"></ion-col>
              <ion-col size="3">
                <app-primary-button
                  expand="block"
                  (onClick)="navigateBack()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'global.button.cancel' | translate"
                >
                </app-primary-button>
              </ion-col>
              <ion-col size="3">
                <app-primary-button
                  type="submit"
                  expand="block"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [label]="getSubmitButtonLabel()"
                  [buttonType]="buttonType.GRADIENT"
                >
                </app-primary-button>
              </ion-col>
            </ion-row>
            <div class="bottom-safe-area"></div>
          </ion-grid>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
