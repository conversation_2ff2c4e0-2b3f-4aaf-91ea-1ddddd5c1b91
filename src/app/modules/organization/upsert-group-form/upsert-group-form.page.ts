import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { IsoDatePipe } from 'src/app/pipes/iso-date.pipe';
import {
  AdminService,
  Childrens_Age_Range,
  CreateGroupRequest,
  GroupDo,
  LocationDo,
} from '../../../services/admin/admin.service';
import { Utils } from '../../../utils/utils';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import { FormValidationHelper } from '../helpers/form-validation-helper';
import { OrganizationNode, OrganizationRepository } from '../organizations.repository';
import { OrganizationNodeType } from '../organizations/utils';
import { LoadingUiEntry, UpsertStrategy } from '../upsert-location-form/upsert-location-form.page';

@Component({
  selector: 'app-upsert-group-form',
  templateUrl: './upsert-group-form.page.html',
  styleUrls: ['../upsert-organization-form/upsert-organization-form.page.scss'],
})
export class UpsertGroupFormPage implements OnInit, AfterViewInit {
  constructor(
    private formBuilder: UntypedFormBuilder,
    private adminService: AdminService,
    private navController: NavController,
    private route: ActivatedRoute,
    private toastController: ToastController,
    private organizationRepo: OrganizationRepository,
    public translate: TranslateService,
    public loadingController: LoadingController,
  ) {}

  private ZERO_TO_THREE = '0 - 3';
  private ZERO_TO_SIX = '0 - 6';
  private THREE_TO_SIX = '3 - 6';
  private upsertStrategy: UpsertStrategy;

  public orgId: number;
  public locId: number;
  public groupId?: number;

  public groupNameErrorMessage?: string;
  public numberOfEvaluableEducatorsErrorMessage?: string;
  public childrensAgeRangeErrorMessage?: string;
  public notesErrorMessage?: string;

  public childAgeRanges = [this.ZERO_TO_THREE, this.ZERO_TO_SIX, this.THREE_TO_SIX];

  public locationName = '-';
  public locationManagmentName = '-';
  public groupCreatedAt = '-';

  public formDataLoadingUi: HTMLIonLoadingElement = null;
  public parentDataLoadingUi: HTMLIonLoadingElement = null;

  public buttonType = PrimaryButtonType;
  public tags: string[] = [];

  private pageBeforeNavigation: number = 1;

  @ViewChild('content') content: any;

  public childAgeRangesAlertOptions = {
    header: this.translate.instant('organization.upsert_group.childrens_age_range'),
  };

  public upsertGroupForm = this.formBuilder.group({
    groupName: ['', [Validators.required]],
    numberOfEvaluableEducators: ['', [Validators.pattern(/^[0-9]*$/)]],
    childrensAgeRange: [''],
    notes: [''],
  });

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.orgId = +params.orgId; // (+) converts string 'id' to a number
      this.locId = +params.locationId;
      this.groupId = +params.groupId;
      if (this.groupId !== -1) {
        this.upsertStrategy = UpsertStrategy.UPDATE;
        this.fetchAndPrefillFormData();
      } else {
        this.upsertStrategy = UpsertStrategy.CREATE;
      }
    });
    this.fetchLocationData();

    this.route.queryParams.subscribe(params => {
      this.pageBeforeNavigation = +params['page'];
    });
  }

  ngAfterViewInit(): void {
    this.content.scrollToTop();
  }

  private async fetchAndPrefillFormData(): Promise<void> {
    await this.showLoadingUi(LoadingUiEntry.FORM_DATA);
    this.adminService.getGroupById(this.groupId).subscribe(async (resp) => {
      if (resp.success) {
        this.prefillLocationData(resp.data.group);
      } else {
        await this.showErrorMessage(true);
      }
      await this.hideLoadingUi(LoadingUiEntry.FORM_DATA);
    });
  }

  private async fetchLocationData(): Promise<void> {
    await this.showLoadingUi(LoadingUiEntry.PARENT_DATA);
    this.adminService.getLocationById(this.locId).subscribe(async (resp) => {
      if (resp.success) {
        this.setLocationData(resp.data.location);
      } else {
        await this.showErrorMessage(true);
      }
      await this.hideLoadingUi(LoadingUiEntry.PARENT_DATA);
    });
  }

  private setLocationData(location: LocationDo): void {
    this.locationName = location.locationName;
    if (
      location.primaryLocationManagementFirstName !== '' ||
      location.primaryLocationManagementLastName !== ''
    ) {
      this.locationManagmentName =
        location.primaryLocationManagementFirstName +
        ' ' +
        location.primaryLocationManagementLastName;
    }
  }

  private prefillLocationData(group: GroupDo): void {
    this.organizationRepo.newlyCreatedNode = {
      name: group.groupName,
      tags: group.tags,
      orgId: this.orgId,
      locId: this.locId,
      groupId: group.id,
      id: group.id,
      type: OrganizationNodeType.GROUP,
      level: 2,
      children: [],
    } as OrganizationNode;
    this.upsertGroupForm.controls.groupName.setValue(group.groupName);
    this.upsertGroupForm.controls.numberOfEvaluableEducators.setValue(
      group.numberOfEvaluableEducators,
    );
    this.upsertGroupForm.controls.childrensAgeRange.setValue(
      this.resolveChildrensAgeRange(group.childrensAgeRange),
    );
    this.upsertGroupForm.controls.notes.setValue(group.notes);
    if (typeof group.tags !== 'undefined' && group.tags.length > 0) {
      this.tags = group.tags;
    }

    this.groupCreatedAt = new IsoDatePipe(this.translate.currentLang).transform(group.createdAt);
  }

  private createGroup(request: CreateGroupRequest): void {
    this.adminService.createGroup(request).subscribe(async (result) => {
      if (result.success) {
        this.organizationRepo.newlyCreatedNode = {
          name: result.data.group.groupName,
          id: result.data.group.id,
          tags: result.data.group.tags,
          orgId: this.orgId,
          locId: this.locId,
          groupId: result.data.group.id,
          type: OrganizationNodeType.GROUP,
          level: 2,
          children: [],
        } as OrganizationNode;
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }

  private updateGroup(request: CreateGroupRequest): void {
    this.adminService.updateGroup(request, this.groupId).subscribe(async (result) => {
      if (result.success) {
        this.navigateBack();
      } else {
        this.showErrorMessage(false);
      }
    });
  }

  private resolveChildrensAgeRange(ageRange: string): string {
    switch (ageRange) {
      case 'ZERO_TO_THREE':
        return this.ZERO_TO_THREE;
      case 'ZERO_TO_SIX':
        return this.ZERO_TO_SIX;
      case 'THREE_TO_SIX':
        return this.THREE_TO_SIX;
    }
  }

  private async createLoadingUi(): Promise<HTMLIonLoadingElement> {
    return await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
  }

  async submitForm(): Promise<void> {
    if (this.upsertGroupForm.valid) {
      const request = new CreateGroupRequest();
      request.locationId = this.locId;
      request.groupName = this.upsertGroupForm.value.groupName;
      request.numberOfEvaluableEducators = this.upsertGroupForm.value.numberOfEvaluableEducators;
      request.childrensAgeRange = this.mapChildAgeRange(
        this.upsertGroupForm.value.childrensAgeRange,
      );
      request.notes = this.upsertGroupForm.value.notes;
      request.tags = this.tags;

      this.upsertStrategy === UpsertStrategy.CREATE
        ? this.createGroup(request)
        : this.updateGroup(request);
    } else {
      this.validateForm();
    }
  }

  validateForm(): void {
    this.groupNameErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertGroupForm.controls.groupName,
    );
    this.numberOfEvaluableEducatorsErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertGroupForm.controls.numberOfEvaluableEducators,
    );
    this.childrensAgeRangeErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertGroupForm.controls.childrensAgeRange,
    );
    this.notesErrorMessage = FormValidationHelper.handleValidationError(
      this.upsertGroupForm.controls.notes,
    );
  }

  navigateBack(): void {
    this.navController.navigateRoot(`admin/organizations?initialPage=${this.pageBeforeNavigation}`);
  }

  mapChildAgeRange(ageRange: string): Childrens_Age_Range {
    switch (ageRange) {
      case this.ZERO_TO_THREE:
        return Childrens_Age_Range.ZERO_TO_THREE;
      case this.ZERO_TO_SIX:
        return Childrens_Age_Range.ZERO_TO_SIX;
      case this.THREE_TO_SIX:
        return Childrens_Age_Range.THREE_TO_SIX;
    }
  }

  getSubmitButtonLabel(): string {
    return this.upsertStrategy === UpsertStrategy.UPDATE
      ? this.translate.instant('global.save')
      : this.translate.instant('global.create');
  }

  getToolbarTitle(): string {
    return this.upsertStrategy === UpsertStrategy.UPDATE
      ? this.translate.instant('organization.update-group')
      : this.translate.instant('organization.new-group');
  }

  onTagsChanged(tags: string[]): void {
    this.tags = tags;
  }

  async showLoadingUi(loadingUiEntity: LoadingUiEntry): Promise<void> {
    switch (loadingUiEntity) {
      case LoadingUiEntry.PARENT_DATA:
        this.parentDataLoadingUi = await this.createLoadingUi();
        await this.parentDataLoadingUi.present();
        break;
      case LoadingUiEntry.FORM_DATA:
        this.formDataLoadingUi = await this.createLoadingUi();
        await this.formDataLoadingUi.present();
        break;
    }
  }

  async hideLoadingUi(loadingUiEntity: LoadingUiEntry): Promise<void> {
    switch (loadingUiEntity) {
      case LoadingUiEntry.PARENT_DATA:
        await this.parentDataLoadingUi.dismiss();
        break;
      case LoadingUiEntry.FORM_DATA:
        await this.formDataLoadingUi.dismiss();
        break;
    }
  }

  async showErrorMessage(shouldReturn: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('global.error.generic'),
      color: 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }
}
