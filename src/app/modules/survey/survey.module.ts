import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { SurveyPage } from './pages/survey/survey.page';
import { TranslateModule } from '@ngx-translate/core';
import { GraziasMissingTranslationHandler } from '../../utils/missingTranslationHandler';
import { RunSurveyComponent } from './components/run-survey/run-survey.component';
import { WelcomeSurveyComponent } from './components/welcome/welcome-survey.component';
import { SharedModule } from '../shared/shared.module';
import { SelectLanguageComponent } from './components/select-language/select-language.component';
import { SuccessPageComponent } from './components/success-page/success-page.component';
import { MatMenuModule } from '@angular/material/menu';

@NgModule({
  declarations: [
    SurveyPage,
    RunSurveyComponent,
    WelcomeSurveyComponent,
    SelectLanguageComponent,
    SuccessPageComponent,
  ],
  imports: [
    CommonModule,
    IonicModule,
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
    SharedModule,
    MatMenuModule,
  ],
  exports: [SurveyPage, RunSurveyComponent, WelcomeSurveyComponent, SelectLanguageComponent],
})
export class SurveyModule {}
