import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SurveyTemplateRepository } from '../../repositories/survey-template.repository';
import { SurveyTokenService, SurveyType } from '../../../../services/jwt/survey-token.service';
import { AdminService } from '../../../../services/admin/admin.service';
import { getSurveyLanguage, Languages } from '../../../../utils/language-helper';
import { Utils } from '../../../../utils/utils';
import { LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-survey',
  templateUrl: './survey.page.html',
  styleUrls: ['./survey.page.scss'],
})
export class SurveyPage implements OnInit {
  public surveyStarted = false;
  public surveyFinished = false;

  public hasValidToken = false;
  public token: string;
  public surveyType: SurveyType;
  public surveyJson: any;
  public selectedLanguage = Languages.DE;

  private loadingUi: HTMLIonLoadingElement = null;
  public isLoading = true;

  constructor(
    private route: ActivatedRoute,
    private surveyTemplateRepository: SurveyTemplateRepository,
    private surveyTokenService: SurveyTokenService,
    private adminService: AdminService,
    private loadingController: LoadingController,
    private translate: TranslateService,
  ) {}

  ngOnInit(): void {
    // noinspection JSIgnoredPromiseFromCall
    this.initializeSurvey().then((_) => {
      this.isLoading = false;
      this.loadingUi.dismiss();
    });
  }

  private async initializeSurvey(): Promise<void> {
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    const requestedToken = this.route.snapshot.params.surveyToken;
    if (!(await this.validateSurveyAccess(requestedToken))) {
      this.hasValidToken = false;
      return;
    }
    this.token = requestedToken;

    const surveyType = this.surveyTokenService.getSurveyTypeFromToken(this.token);
    if (surveyType === undefined) {
      this.hasValidToken = false;
      return;
    }
    this.surveyType = surveyType;

    const surveyDefinition = this.surveyTemplateRepository.getSurveyDefinitionForType(surveyType);
    if (surveyDefinition === undefined) {
      this.hasValidToken = false;
      return;
    }
    this.surveyJson = surveyDefinition;

    this.hasValidToken = true;
  }

  async validateSurveyAccess(token: string | undefined): Promise<boolean> {
    return (
      token !== undefined &&
      (await this.adminService.canSurveyBeEvaluated(token).toPromise()).success
    );
  }

  startSurvey(): void {
    this.selectedLanguage = getSurveyLanguage(this.translate.currentLang);
    this.surveyStarted = true;
  }
}
