<ion-content *ngIf="!hasValidToken && !surveyFinished && !isLoading">
  <app-not-found></app-not-found>
</ion-content>

<ion-content *ngIf="hasValidToken && !surveyFinished">
  <app-welcome-survey *ngIf="!surveyStarted" [surveyType]="surveyType" (clicked)="startSurvey()" />
  <app-run-survey
    [surveyJson]="surveyJson"
    [surveyType]="surveyType"
    [language]="selectedLanguage"
    [token]="token"
    *ngIf="surveyStarted"
    (onFinish)="surveyFinished = true"
  />
</ion-content>

<ion-content *ngIf="surveyFinished">
  <app-success-page *ngIf="surveyFinished"></app-success-page>
</ion-content>
