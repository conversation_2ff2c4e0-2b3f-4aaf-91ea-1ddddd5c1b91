.loading-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button {
  text-transform: none;
  padding-left: 0.5rem;
}

.start-evaluation-col {
  padding-left: 37px;
}

.error-message-container {
  height: 100%;

  ion-row {
    height: 100%;
  }

  .error-message {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 80%;
    ion-img {
      margin-bottom: 10px;
      width: 70px;
      height: auto;
    }
  }

  h3 {
    margin-bottom: 0;
  }
}

.no-valid-evaluation-container {
  background: var(--ion-color-primary);
  padding: 12px;
  text-align: center;
  color: white;
  border-radius: 12px;
  width: 100%;
  text-decoration: none;
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: center;

  img {
    width: 20px;
    height: auto;
    margin-right: 5px;
  }

  .underline {
    text-decoration: underline;
  }
}

ion-content {
  .side-pad {
    margin-top: -0.5rem;
    padding-left: 2.625rem;
    padding-right: 2.625rem;
    display: flex;
    justify-content: center;

    ngx-datatable {
      flex-grow: 1;
      max-width: 100%;
      .action-buttons-cell-container {
        display: flex;
        justify-content: flex-end;

        .action-button-image {
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  }
}
