@import './../../../../../theme/variables_v2';

ion-grid,
ion-col {
  padding: 0;
  height: 100%;
}

.scene {
  position: relative;
  height: 100%;
  width: 100%;
  z-index: 1;

  background-size: cover !important;

  &.side-img {
    background: url('/assets/img/survey_welcome.webp') top;
  }
}

.background-color {
  background-color: white;
}

.content-container {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin: auto 15%;

  .language-menu {
    position: absolute;
    right: 0;
    top: 48px;
  }

  .title {
    font-size: 36px;
    margin-right: 88px;
    font-weight: 800;
    padding-bottom: 32px;
  }

  .button-row {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;

    .button {
      width: 225px;
      padding-top: 36px;
    }
  }

  .privacy-policy {
    padding-top: 24px;

    .checkbox {
      display: flex;
    }

    img {
      width: 16px;
      height: 16px;
      margin-right: 16px;
      cursor: pointer;
      margin-top: 3px;
    }

    .link {
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

.img-container {
  position: relative;
}

.scroll-content {
  overflow: scroll;
  height: 100vh;
}
