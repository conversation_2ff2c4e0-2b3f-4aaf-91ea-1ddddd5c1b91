<ion-content class="design-v2" [forceOverscroll]="false">
  <ion-grid class="h-100">
    <ion-row class="h-100">
      <ion-col size="7" class="img-container">
        <div class="scene side-img"></div>
      </ion-col>
      <ion-col size="5" class="background-color disable-scrollbar scroll-content">
        <div class="content-container">
          <app-select-language
            class="language-menu"
            (languageSelected)="setLanguage($event)"
            [selectedLanguage]="selectedLanguage"
          ></app-select-language>
          <div class="title">{{ 'surveys.welcome.' + surveyType + '.title' | translate }}</div>
          <div>{{ 'surveys.welcome.' + surveyType + '.text' | translate }}</div>
          <div class="privacy-policy">
            {{ 'surveys.privacyPolicy.accept' | translate }}
            <div class="checkbox">
              <img
                [src]="
                  privacyPolicySelected
                    ? 'assets/svg/checkbox_active_filled.svg'
                    : 'assets/svg/checkbox_inactive.svg'
                "
                alt="checkbox"
                (click)="privacyPolicySelected = !privacyPolicySelected"
              />
              <span class="link" (click)="navigatePrivacyPolicy()">{{
                'global.privacyPolicy' | translate
              }}</span>
            </div>
          </div>
          <div class="button-row">
            <app-primary-button
              class="button"
              type="submit"
              expand="block"
              [isDisabled]="!privacyPolicySelected"
              [isLoading]="false"
              label="{{ 'surveys.welcome.start-survey' | translate }}"
              (onClick)="performClickAction()"
              [buttonType]="buttonType.GRADIENT"
            >
            </app-primary-button>
          </div>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
