import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NavController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { PolicySource } from '../../../../pages/legal/terms/terms.page';
import { SurveyType } from '../../../../services/jwt/survey-token.service';
import { Languages } from '../../../../utils/language-helper';
import { PrimaryButtonType } from '../../../shared/components/primary-button/primary-button.component';

@Component({
  selector: 'app-welcome-survey',
  templateUrl: './welcome-survey.component.html',
  styleUrls: ['./welcome-survey.component.scss'],
})
export class WelcomeSurveyComponent {
  @Input() surveyType: SurveyType;
  @Output() clicked?: EventEmitter<Languages> = new EventEmitter<Languages>();
  public buttonType = PrimaryButtonType;
  public selectedLanguage: Languages = Languages.DE;
  public privacyPolicySelected = false;

  constructor(
    private navController: NavController,
    private translate: TranslateService,
  ) {}

  performClickAction(): void {
    this.clicked.emit(this.selectedLanguage);
  }

  setLanguage(language: Languages): void {
    this.translate.use(language);
    this.selectedLanguage = language;
  }

  navigatePrivacyPolicy(): void {
    this.navController.navigateForward(`/privacy-policy/${PolicySource.SURVEYS}`);
  }
}
