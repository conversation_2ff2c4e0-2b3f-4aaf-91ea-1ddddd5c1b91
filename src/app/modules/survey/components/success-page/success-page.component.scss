@import './../../../../../theme/variables_v2';

ion-grid,
ion-col {
  padding: 0;
  height: 100%;
}

.scene {
  position: relative;
  height: 100%;
  width: 100%;
  z-index: 1;

  background-size: cover !important;

  &.side-img {
    background: url('/assets/img/survey_success.webp') top;
  }
}

.background-color {
  background-color: white;
}

.content-container {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: auto 15%;

  .title {
    font-size: 36px;
    margin-right: 88px;
    font-weight: 800;
    padding-bottom: 15px;
  }
}

.img-container {
  position: relative;
}

.scroll-content {
  overflow: scroll;
  height: 100vh;
}
