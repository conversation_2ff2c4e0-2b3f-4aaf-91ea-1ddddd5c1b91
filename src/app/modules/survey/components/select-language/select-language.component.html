<app-icon-button
  [matMenuTriggerFor]="menu"
  [isLoading]="false"
  [iconPath]="'assets/svg/language.svg'"
>
</app-icon-button>
<mat-menu class="design-v2" #menu="matMenu" xPosition="before">
  <button
    *ngFor="let element of languages"
    [ngClass]="{ 'language-active': selectedLanguage === element }"
    mat-menu-item
    (click)="onLanguageSelected(element)"
  >
    {{ getLanguageTranslationKey(element) | translate }}
  </button>
</mat-menu>
