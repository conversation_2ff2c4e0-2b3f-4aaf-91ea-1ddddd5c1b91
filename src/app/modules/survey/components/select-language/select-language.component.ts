import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  getSurveyLanguages,
  Languages,
  getLanguageTranslationKey,
} from '../../../../utils/language-helper';

@Component({
  selector: 'app-select-language',
  templateUrl: './select-language.component.html',
  styleUrls: ['./select-language.component.scss'],
})
export class SelectLanguageComponent {
  public languages = Object.values(getSurveyLanguages());
  @Input() selectedLanguage: Languages = Languages.DE;
  @Output() languageSelected = new EventEmitter<Languages>();
  public getLanguageTranslationKey = getLanguageTranslationKey;

  onLanguageSelected(type: Languages): void {
    this.languageSelected.emit(type);
  }
}
