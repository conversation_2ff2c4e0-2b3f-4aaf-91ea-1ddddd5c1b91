<head>
  <script src="https://npmcdn.com/zone.js"></script>
  <script src="https://npmcdn.com/core-js@2.6.5/client/shim.min.js"></script>
  <script src="https://npmcdn.com/rxjs@5.0.0-beta.6/bundles/Rx.umd.js"></script>
  <script src="https://npmcdn.com/@angular/core@2.0.0-rc.5/bundles/core.umd.js"></script>
  <script src="https://npmcdn.com/@angular/common@2.0.0-rc.5/bundles/common.umd.js"></script>
  <script src="https://npmcdn.com/@angular/compiler@2.0.0-rc.5/bundles/compiler.umd.js"></script>
  <script src="https://npmcdn.com/@angular/platform-browser@2.0.0-rc.5/bundles/platform-browser.umd.js"></script>
  <script src="https://npmcdn.com/@angular/platform-browser-dynamic@2.0.0-rc.5/bundles/platform-browser-dynamic.umd.js"></script>
  <script src="https://unpkg.com/survey-angular@1.8.76/survey.angular.min.js"></script>
  <link
    href="https://unpkg.com/survey-core@1.8.76/modern.min.css"
    type="text/css"
    rel="stylesheet"
  />
</head>

<ion-content class="design-v2 background-{{ surveyType }}-survey">
  <div class="main-content">
    <ion-header class="header-v2">
      <ion-toolbar class="toolbar-v2 header">
        <div class="flex">
          <ion-title>{{ 'organization.' + surveyType + '-survey' | translate }}</ion-title>
          <app-select-language
            class="language-button"
            (languageSelected)="setLanguage($event)"
            [selectedLanguage]="language"
          ></app-select-language>
        </div>
      </ion-toolbar>
    </ion-header>

    <div class="survey-container contentcontainer codecontainer">
      <div id="surveyElement"></div>
    </div>
  </div>
</ion-content>
