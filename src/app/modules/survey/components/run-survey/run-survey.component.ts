import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Nav<PERSON>ontroller, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Model, StylesManager, SurveyModel, SurveyNG } from 'survey-angular';
import { AdminService, StoreSurveyAnswerRequest } from '../../../../services/admin/admin.service';
import { SurveyTokenService, SurveyType } from '../../../../services/jwt/survey-token.service';
import { Languages } from '../../../../utils/language-helper';

@Component({
  selector: 'app-run-survey',
  templateUrl: './run-survey.component.html',
  styleUrls: ['./run-survey.component.scss'],
})
export class RunSurveyComponent implements OnInit {
  @Input() public surveyJson: string;
  @Input() public language: Languages;
  @Input() public token: string;
  @Input() public surveyType: SurveyType;
  @Output() onFinish?: EventEmitter<Languages> = new EventEmitter<Languages>();
  private surveyModel: SurveyModel;

  // NOTE: Variable definitions: https://github.com/surveyjs/survey-library/blob/master/src/stylesmanager.ts#L632
  private static setupColorTheme(): void {
    const defaultThemeColors = StylesManager.ThemeColors.modern;

    defaultThemeColors['$main-color'] = '#160325';
    defaultThemeColors['$main-hover-color'] = '#E7E5E9';
    defaultThemeColors['$text-color'] = '#4A4A4A';
    defaultThemeColors['$header-color'] = '#7FF07F';

    defaultThemeColors['$error-color'] = '#F76C6C';

    defaultThemeColors['$header-background-color'] = '#E7E5E9';
    defaultThemeColors['$body-container-background-color'] = '#F8F8F8';

    defaultThemeColors['$answer-background-color'] = 'rgba(00, 00, 00, 0)';
    defaultThemeColors['$error-background-color'] = 'rgba(00, 00, 00, 0)';

    StylesManager.applyTheme('modern');
  }

  constructor(
    private adminService: AdminService,
    private toastController: ToastController,
    private translate: TranslateService,
    private navController: NavController,
    private surveyTokenService: SurveyTokenService,
  ) {}

  ngOnInit(): void {
    RunSurveyComponent.setupColorTheme();
    this.surveyModel = new Model(this.surveyJson);
    this.surveyModel.focusFirstQuestionAutomatic = false;
    this.surveyModel.locale = this.language;

    this.surveyModel.onComplete.add((result, options) => {
      const request: StoreSurveyAnswerRequest = {
        surveyToken: this.token,
        surveyData: this.generateAnswerJson(result.data, result.locale),
        surveyLanguage: result.locale,
      };
      this.adminService.saveSurveyAnswer(request).subscribe(async (resp) => {
        if (resp.success) {
          this.onFinish.emit();
        } else {
          this.navController.navigateRoot(`/surveys/error`);
          this.showToast(this.translate.instant('surverys.result-upload.error-message'), 'danger');
        }
      });
    });

    SurveyNG.render('surveyElement', { model: this.surveyModel });
  }

  setLanguage(language: Languages): void {
    this.translate.use(language);
    this.surveyModel.locale = language;
    this.surveyModel.render();
  }

  private generateAnswerJson(surveyResultJson: string, surveyLocale: string): string {
    const data = {
      meta: {
        type: this.surveyTokenService.getSurveyTypeFromToken(this.token),
        version: null,
        lastKnownSurveyLocale: surveyLocale,
      },
      result: {},
    };

    Object.keys(surveyResultJson).forEach((key) => {
      data.result[key] = {
        answer: surveyResultJson[key],
        locale: null,
      };
    });

    return JSON.stringify(data);
  }

  private async showToast(message: string, color: string): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color,
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }
}
