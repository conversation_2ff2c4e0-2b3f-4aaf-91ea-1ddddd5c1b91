import { Injectable } from '@angular/core';
import { SurveyType } from '../../../services/jwt/survey-token.service';

import organizationSurvey from 'src/assets/surveys/organization-survey.json';
import administrationSurvey from 'src/assets/surveys/administration-survey.json';
import staffSurvey from 'src/assets/surveys/staff-survey.json';
import parentalSurvey from 'src/assets/surveys/parental-survey.json';

@Injectable({
  providedIn: 'root',
})
export class SurveyTemplateRepository {
  getSurveyDefinitionForType(type: SurveyType): any | undefined {
    switch (type) {
      case SurveyType.ORGANIZATION_SURVEY:
        return organizationSurvey;
      case SurveyType.ADMINISTRATION_SURVEY:
        return administrationSurvey;
      case SurveyType.PARENTAL_SURVEY:
        return parentalSurvey;
      case SurveyType.STAFF_SURVEY:
        return staffSurvey;
      default:
        return undefined;
    }
  }
}
