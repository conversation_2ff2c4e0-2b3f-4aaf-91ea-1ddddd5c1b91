<ion-content *ngIf="!isLoading && hasError">
  <app-not-found
    [message]="translate.instant('childrenEvaluation.report.notFound')"
  ></app-not-found>
</ion-content>

<ion-content *ngIf="!isLoading && !hasError">
  <ion-header no-border>
    <ion-toolbar>
      <div class="print-button">
        <app-primary-button
          [buttonType]="buttonType"
          label="{{'global.button.print' | translate}}"
          (onClick)="customPrint()"
        ></app-primary-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <span #printSection>
    <div class="screen-container">
      <div class="page-break">
        <app-report-front-page
          subTitleKey="reports.location.sub_title"
          [organizationName]="organizationAnLocationName"
          [surveyResults]="reportData.surveysAndResults"
          [childEvaluations]="reportData.childEvaluations !== null ? [reportData.childEvaluations] : undefined"
          [evaluation]="reportData.evaluation !== null ? [reportData.evaluation] : undefined"
          [surveysMinDate]="reportData.surveysMinDate"
          [surveysMaxDate]="reportData.surveysMaxDate"
        >
        </app-report-front-page>
      </div>

      <div class="page-break">
        <app-tb001></app-tb001>
        <app-tb002></app-tb002>
        <app-tb003></app-tb003>
      </div>

      <div class="page-break">
        <app-tb004></app-tb004>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.ORGANIZATION_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
      >
        <div
          *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY) || hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY) || hasSurveyResult(SurveyType.STAFF_SURVEY)"
        >
          <app-tb005
            *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY) || hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY) || hasSurveyResult(SurveyType.STAFF_SURVEY)"
          ></app-tb005>
          <app-tb006
            *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY) || hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY) || hasSurveyResult(SurveyType.STAFF_SURVEY)"
          ></app-tb006>
          <app-tb007 *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"></app-tb007>
        </div>

        <app-abb001
          *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"
          [t_004_1_answer]="reportData.surveysAndResults.organizationSurveyResults[0].t_004_1"
          [t_004_2_answer]="reportData.surveysAndResults.organizationSurveyResults[0].t_004_2"
          [t_004_3_answer]="reportData.surveysAndResults.organizationSurveyResults[0].t_004_3"
          [t_004_4_answer]="reportData.surveysAndResults.organizationSurveyResults[0].t_004_4"
          [freeFormAnswer]="reportData.surveysAndResults.organizationSurveyResults[0].t_001_0"
        ></app-abb001>

        <app-tb008 *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"></app-tb008>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.ORGANIZATION_SURVEY, SurveyType.STAFF_SURVEY])"
      >
        <app-abb002
          *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"
          [t_005_answer_key]="reportData.surveysAndResults.organizationSurveyResults[0].t_005_0"
          [t_006_answer_key]="reportData.surveysAndResults.organizationSurveyResults[0].t_006_0"
          [t_007_answer_key]="reportData.surveysAndResults.organizationSurveyResults[0].t_007_0"
        ></app-abb002>

        <app-tb009 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb009>

        <app-abb003
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-abb003>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.PARENTAL_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
      >
        <app-tb010
          *ngIf="hasOneSurveyResult([SurveyType.PARENTAL_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
        ></app-tb010>
        <app-tb011-location
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [data]="reportData.surveysAndResults.administrationSurveyResults"
        ></app-tb011-location>

        <app-abb004
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-abb004>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-abb005
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-abb005>
        <app-abb006
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-abb006>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.PARENTAL_SURVEY, SurveyType.STAFF_SURVEY])"
      >
        <app-tb012 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb012>
        <app-abb007
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-abb007>

        <app-tb013 *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"></app-tb013>
        <app-abb008></app-abb008>
        <app-abb009
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="reportData.surveysAndResults.parentalSurveyResults"
        ></app-abb009>
        <app-tb014
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="reportData.surveysAndResults.parentalSurveyResults"
        ></app-tb014>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-tb015 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb015>
        <app-abb010
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-abb010>
      </div>

      <div class="page-break">
        <app-tb016-one *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb016-one>
        <app-tb016-two *ngIf="!hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb016-two>

        <app-abb011
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-abb011>

        <app-tb017 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb017>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
      >
        <app-tb018
          *ngIf="hasOneSurveyResult([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
        ></app-tb018>

        <app-abb012
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="reportData.surveysAndResults.parentalSurveyResults"
        ></app-abb012>

        <app-tb019
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="reportData.surveysAndResults.parentalSurveyResults"
        ></app-tb019>
        <app-tb020
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY) && hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"
        ></app-tb020>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)">
        <app-abb013-location
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
          [administrationSurveyResults]="reportData.surveysAndResults.administrationSurveyResults"
          [organizationSurveyResults]="reportData.surveysAndResults.organizationSurveyResults"
        ></app-abb013-location>

        <app-tb021
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
        ></app-tb021>
        <app-tb022 *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"></app-tb022>

        <app-abb014-location
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [administrationSurveyResults]="reportData.surveysAndResults.administrationSurveyResults"
        ></app-abb014-location>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)">
        <div
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY])"
        >
          <app-tb023></app-tb023>
          <app-tb024></app-tb024>
          <app-tb025></app-tb025>
        </div>
        <app-abb015-table-location
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [administrationData]="reportData.surveysAndResults.administrationSurveyResults[0]"
        >
        </app-abb015-table-location>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)">
        <app-abb015-bar-chart-location
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [parentalData]="reportData.surveysAndResults.parentalSurveyResults"
        >
        </app-abb015-bar-chart-location>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.STAFF_SURVEY, SurveyType.ADMINISTRATION_SURVEY])"
      >
        <app-tb026 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb026>
        <app-tb027-location
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [data]="reportData.surveysAndResults.administrationSurveyResults"
        ></app-tb027-location>
        <app-tb029-location
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [data]="reportData.surveysAndResults.administrationSurveyResults"
        ></app-tb029-location>
      </div>

      <!-- Attachments -->
      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.STAFF_SURVEY, SurveyType.PARENTAL_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
      >
        <h1>{{"reports.survey.attachments.title" | translate}}</h1>
        <app-attachment001
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [data]="reportData.surveysAndResults.staffSurveyResults"
        ></app-attachment001>

        <app-attachment002
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="reportData.surveysAndResults.parentalSurveyResults"
        ></app-attachment002>

        <app-attachment003
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="reportData.surveysAndResults.parentalSurveyResults"
        ></app-attachment003>

        <app-attachment004
          *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"
          [data]="reportData.surveysAndResults.organizationSurveyResults"
        ></app-attachment004>
      </div>

      <app-children-evaluation-report-component
        *ngIf="reportData.childEvaluations.aggregatedChildEvaluation != null"
        [childEvalReportData]="reportData.childEvaluations.aggregatedChildEvaluation"
        [reportType]="ReportType.LOCATION_REPORT"
      ></app-children-evaluation-report-component>

      <app-evaluation-report
        *ngIf="reportData.evaluation != null"
        [evaluation]="reportData.evaluation"
        [isAggregated]="true"
      >
      </app-evaluation-report>
    </div>
  </span>
</ion-content>
