import { ReportType } from '../../organization/organizations/models';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import {
  AdminService,
  OrganizationReportDataRequest,
  OrganizationReportDataResponse,
} from '../../../services/admin/admin.service';
import { ActivatedRoute } from '@angular/router';
import { LoadingController } from '@ionic/angular';
import { Utils } from '../../../utils/utils';
import { TranslateService } from '@ngx-translate/core';
import { PrintService, PrintStyles } from '../../../services/print.service';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import { ReportService } from '../report.service';
import { SurveyType } from '../../../services/jwt/survey-token.service';
import { UserService } from '../../../services/user/user.service';

@Component({
  selector: 'app-organization-report',
  templateUrl: './organization-report.page.html',
  styleUrls: ['./organization-report.page.scss'],
})
export class OrganizationReportPage implements OnInit {
  @ViewChild('printSection', { read: ElementRef, static: false }) printSection: ElementRef;

  public buttonType = PrimaryButtonType.GRADIENT;
  public loadingUi: HTMLIonLoadingElement = null;
  public reportData: OrganizationReportDataResponse;
  public isLoading = true;
  public hasError = false;
  public organizationName: string;
  public SurveyType = SurveyType;
  public ReportType = ReportType;

  private reportRequest: OrganizationReportDataRequest;

  constructor(
    private route: ActivatedRoute,
    private adminService: AdminService,
    private userService: UserService,
    public loadingController: LoadingController,
    public translate: TranslateService,
    private printService: PrintService,
    public reportService: ReportService,
  ) {}

  hasSurveyResult(surveyType: SurveyType): boolean {
    return this.reportService.hasResult(this.reportData.surveysAndResults, surveyType);
  }

  hasOneSurveyResult(surveys: Array<SurveyType>): boolean {
    return this.reportService.hasOneSurveyResult(surveys, this.reportData.surveysAndResults);
  }

  hasAllSurveyResults(surveys: Array<SurveyType>): boolean {
    return this.reportService.hasAllSurveyResults(surveys, this.reportData.surveysAndResults);
  }

  customPrint(): void {
    const printContent = this.printSection.nativeElement;
    this.printService.loadServerPdfGenerationStyles(
      printContent,
      PrintStyles.ORGANIZATION_AND_LOCATION_REPORT,
    );
  }

  ngOnInit(): void {
    const paramMap = this.route.snapshot.queryParamMap;
    this.reportRequest = OrganizationReportDataRequest.fromRequestParameters(paramMap);
    this.organizationName = paramMap.get('nodeName');

    const authToken = paramMap.get('authToken');
    if (authToken) {
      this.userService.authToken = authToken;
    }

    this.fetchReportData();
  }

  private async fetchReportData(): Promise<void> {
    this.isLoading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getOrganizationReportData(this.reportRequest).subscribe(async (resp) => {
      if (resp.success) {
        this.reportData = resp.data;
      }
      await this.loadingUi.dismiss();
      this.isLoading = false;
      this.hasError = !resp.success;
    });
  }
}
