<ion-grid>
  <ion-row>
    <ion-col size="12">
      <h1>{{ 'reports.evaluations.comparison.overview-diagram.title' | translate }}</h1>
      <p>{{ 'reports.evaluations.comparison.overview-diagram.explanation' | translate }}</p>
    </ion-col>
  </ion-row>

  <ion-row>
    <ion-col size="12">
      <h2>{{ getLegendTitle() }}</h2>
    </ion-col>
    <ion-col size="6">
      <div *ngFor="let traitId of boxplotLegendLeftCol" class="boxplot-legend">
        <span class="boxplot-legend-key">{{ traitId }}</span>
        <span>{{ getTraitsForBoxplotLegend(traitId) }}</span>
      </div>
    </ion-col>
    <ion-col size="6">
      <div *ngFor="let traitKey of boxplotLegendRightCol" class="boxplot-legend">
        <span class="boxplot-legend-key">{{ traitKey }}</span>
        <span>{{ getTraitsForBoxplotLegend(traitKey) }}</span>
      </div>
    </ion-col>
  </ion-row>

  <ion-row>
    <ion-col size="12">
      <div *ngFor="let config of boxplotConfig; let index = index">
        <app-boxplot-chart
          [configuration]="config"
          [title]="reportService.getIllustrationTitleTranslation(index + 1)"
          [evaluationType]="evaluationType"
        ></app-boxplot-chart>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
