import { Component, Input } from '@angular/core';
import { BoxplotChartConfiguration } from '../../../shared/components/charts/boxplot-chart/boxplot-chart.component';
import { ReportService } from '../../report.service';
import { EvaluationType } from '../../../organization/organizations/utils';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-boxplot',
  templateUrl: './boxplot.component.html',
  styleUrls: ['./boxplot.component.scss'],
})
export class BoxplotComponent {
  @Input() boxplotConfig: Array<BoxplotChartConfiguration> = [];
  @Input() boxplotLegendLeftCol: Array<string>;
  @Input() boxplotLegendRightCol: Array<string>;
  @Input() boxplotLegendItems: Map<string, string> = new Map();
  @Input() evaluationType: EvaluationType;

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  public getTraitsForBoxplotLegend(key: string): string {
    return this.boxplotLegendItems[key];
  }

  public getLegendTitle(): string {
    switch (this.evaluationType) {
      case EvaluationType.CHILD_EVALUATION:
        return this.translate.instant(
          'reports.child-evaluations.comparison.overview-diagram.traits-title',
        );
      case EvaluationType.EVALUATION:
        return this.translate.instant(
          'reports.evaluations.comparison.overview-diagram.traits-title',
        );
      default:
        return this.translate.instant('reports.comparison.overview-diagram.traits-title');
    }
  }
}
