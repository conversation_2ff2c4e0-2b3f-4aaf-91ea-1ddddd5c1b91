import { Component, Input } from '@angular/core';
import { OrganizationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment004',
  templateUrl: './attachment004.component.html',
})
export class Attachment004Component {
  @Input() data: OrganizationSurveyResult[];

  constructor() {}

  getOrganizationAnswers(): Array<string> {
    return this.data.map((it) => it.t_003_0);
  }
}
