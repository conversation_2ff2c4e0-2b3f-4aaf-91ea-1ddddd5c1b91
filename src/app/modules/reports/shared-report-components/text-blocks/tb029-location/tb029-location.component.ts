import { TranslateService } from '@ngx-translate/core';
import { Component, Input } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-tb029-location',
  templateUrl: './tb029-location.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb029LocationComponent {
  @Input() data: AdministrationSurveyResult[];

  constructor(public translate: TranslateService) {}

  getContent(): string {
    const answer = this.data[0].l_009_2.split('.')[4];
    switch (answer) {
      case '0':
        return this.translate.instant('reports.survey.TB_029.answer.disagree_completely');
      case '1':
        return this.translate.instant('reports.survey.TB_029.answer.disagree_moderately');
      case '2':
        return this.translate.instant('reports.survey.TB_029.answer.agree_moderately');
      case '3':
        return this.translate.instant('reports.survey.TB_029.answer.agree_completely');
      case '4':
        return this.translate.instant('reports.survey.TB_029.answer.no_idea');
      default:
        return '-';
    }
  }
}
