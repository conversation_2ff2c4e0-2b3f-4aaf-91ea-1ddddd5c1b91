import { TranslateService } from '@ngx-translate/core';
import { Component, Input } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-tb011-location',
  templateUrl: './tb011-location.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb011LocationComponent {
  @Input() data: AdministrationSurveyResult[];
  constructor(public translate: TranslateService) {}
}
