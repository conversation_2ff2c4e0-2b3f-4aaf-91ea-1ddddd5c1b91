import { Component, Input } from '@angular/core';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment001-comparison',
  templateUrl: './attachment001-comparison.component.html',
  styleUrls: ['./attachment001-comparison.component.scss'],
})
export class Attachment001ComparisonComponent {
  @Input() setA: StaffSurveyResult[];
  @Input() setB: StaffSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor() {}

  getAnswers(data: StaffSurveyResult[]): Array<string> {
    return data.map((it) => it.pfp_004_2);
  }
}
