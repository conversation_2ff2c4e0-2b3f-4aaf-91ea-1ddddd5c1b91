import { Component, Input } from '@angular/core';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment001',
  templateUrl: './attachment001.component.html',
})
export class Attachment001Component {
  @Input() data: StaffSurveyResult[];

  constructor() {}

  getStaffAnswers(): Array<string> {
    return this.data.map((it) => it.pfp_004_2);
  }
}
