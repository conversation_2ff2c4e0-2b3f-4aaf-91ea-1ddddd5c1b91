import { TranslateService } from '@ngx-translate/core';
import { Component, Input } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-tb019',
  templateUrl: './tb019.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb019Component {
  @Input() data: ParentalSurveyResult[];

  constructor(
    public translate: TranslateService,
    private reportService: ReportService,
  ) {}

  getContent(): string {
    const fam001 = this.data.map((it) => it.fam_001_0);
    const percentageLikeToCome = this.reportService.calcPercentage(
      fam001.filter((it) => it.includes('yes')).length,
      fam001.length,
    );

    const fam002 = this.data.map((it) => it.fam_002_0);
    const percentageFeelSafe = this.reportService.calcPercentage(
      fam002.filter((it) => it.includes('yes')).length,
      fam002.length,
    );

    return this.translate.instant('reports.survey.TB_019', {
      percentOfChildrenLikeToCome: percentageLikeToCome,
      percentOfChildrenFeelSafe: percentageFeelSafe,
    });
  }
}
