import { TranslateService } from '@ngx-translate/core';
import { Component, Input } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-tb014',
  templateUrl: './tb014.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb014Component {
  @Input() data: ParentalSurveyResult[];

  constructor(public translate: TranslateService) {}

  getContent(): string {
    const numberOfAnswers = this.data.map((it) => it.fam_005_3).length;
    return this.translate.instant('reports.survey.TB_014', {
      numberOfAnswers,
    });
  }
}
