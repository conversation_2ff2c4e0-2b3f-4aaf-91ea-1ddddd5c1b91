import { TranslateService } from '@ngx-translate/core';
import { Component } from '@angular/core';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-tb004',
  templateUrl: './tb004.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb004Component {
  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  getFooter(): string {
    const note = this.translate.instant('reports.survey.note');
    const explanation = this.translate.instant('reports.survey.TB_004.boxplot_note');
    return `${note} ${explanation}`;
  }
}
