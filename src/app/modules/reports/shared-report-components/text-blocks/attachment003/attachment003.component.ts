import { Component, Input } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment003',
  templateUrl: './attachment003.component.html',
})
export class Attachment003Component {
  @Input() data: ParentalSurveyResult[];
  constructor() {}

  getParentalAnswers(): Array<string> {
    return this.data.map((it) => it.fam_003_0);
  }
}
