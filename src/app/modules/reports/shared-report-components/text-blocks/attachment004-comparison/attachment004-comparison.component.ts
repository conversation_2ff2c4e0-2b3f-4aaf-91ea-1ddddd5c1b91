import { Component, Input } from '@angular/core';
import { OrganizationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment004-comparison',
  templateUrl: './attachment004-comparison.component.html',
  styleUrls: ['./attachment004-comparison.component.scss'],
})
export class Attachment004ComparisonComponent {
  @Input() setA: OrganizationSurveyResult[];
  @Input() setB: OrganizationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor() {}

  getAnswers(data: OrganizationSurveyResult[]): Array<string> {
    return data.map((it) => it.t_003_0);
  }
}
