import { Component, Input, OnInit } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-tb027-comparison',
  templateUrl: './tb027-comparison.component.html',
  styleUrls: ['./tb027-comparison.component.scss'],
})
export class Tb027ComparisonComponent implements OnInit {
  @Input() setA: AdministrationSurveyResult[];
  @Input() setB: AdministrationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public isAdminACooperating: boolean;
  public isAdminBCooperating: boolean;

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.init();
  }

  private init(): void {
    this.isAdminACooperating = this.setA[0].l_009_1.includes('yes');
    this.isAdminBCooperating = this.setB[0].l_009_1.includes('yes');
  }

  public getContent(): string {
    if (this.isAdminBCooperating === this.isAdminACooperating) {
      return this.getCooperationTranslation(
        this.isAdminACooperating,
        `(${this.setAPeriod}, ${this.setBPeriod})`,
      );
    } else {
      return `${this.getCooperationTranslation(this.isAdminACooperating, `(${this.setAPeriod})`)} \n${this.getCooperationTranslation(this.isAdminBCooperating, `(${this.setBPeriod})`)}`;
    }
  }

  private getCooperationTranslation(isCooperating: boolean, period: string): string {
    const translationKey = isCooperating
      ? 'reports.survey.TB_027.comparison.cooperation'
      : 'reports.survey.TB_027.comparison.no_cooperation';
    return this.translate.instant(translationKey, {
      evaluationPeriod: period,
    });
  }
}
