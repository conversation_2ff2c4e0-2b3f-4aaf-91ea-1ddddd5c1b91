import { Component, Input } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment002',
  templateUrl: './attachment002.component.html',
})
export class Attachment002Component {
  @Input() data: ParentalSurveyResult[];

  constructor() {}

  getParentalAnswers(): Array<string> {
    return this.data.map((it) => it.fam_005_3);
  }
}
