import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-tb027-location',
  templateUrl: './tb027-location.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb027LocationComponent {
  @Input() data: AdministrationSurveyResult[];

  constructor(public translate: TranslateService) {}

  getContent(): string {
    return this.data[0].l_009_1.includes('yes')
      ? this.translate.instant('reports.survey.TB_027.location.content.cooperation')
      : this.translate.instant('reports.survey.TB_027.location.content.no_cooperation');
  }
}
