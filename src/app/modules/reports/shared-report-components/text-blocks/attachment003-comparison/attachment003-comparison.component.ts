import { Component, Input } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment003-comparison',
  templateUrl: './attachment003-comparison.component.html',
  styleUrls: ['./attachment003-comparison.component.scss'],
})
export class Attachment003ComparisonComponent {
  @Input() setA: ParentalSurveyResult[];
  @Input() setB: ParentalSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor() {}

  getAnswers(data: ParentalSurveyResult[]): Array<string> {
    return data.map((it) => it.fam_003_0);
  }
}
