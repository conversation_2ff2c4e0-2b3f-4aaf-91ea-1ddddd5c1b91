import { Component, Input, OnInit } from '@angular/core';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { CHART_COLORS } from '../../../../../utils/chart-colors';
// tslint:disable-next-line:max-line-length
import { MultiBarChartWithGridConfiguration } from '../../../../shared/components/charts/multi-bar-chart-with-lines/multi-bar-chart-with-lines.component';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-tb011-organization',
  templateUrl: './tb011-organization.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb011OrganizationComponent implements OnInit {
  public barChartConfiguration: MultiBarChartWithGridConfiguration;
  public legendMultiBarChart: LegendEntry[];

  @Input() adminSurveyResults: AdministrationSurveyResult[] = [];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.ADMINISTRATION_SURVEY,
      this.adminSurveyResults.length,
    );
  }

  private initLegend(): void {
    this.legendMultiBarChart = [
      { description: 'Ja', color: CHART_COLORS.green },
      { description: 'Nein', color: CHART_COLORS.grey },
    ];
  }

  private initBarChartConfiguration(): void {
    this.barChartConfiguration = {
      color: CHART_COLORS.green,
      labelSuffix: '%',
      barConfigurations: [
        {
          label: this.translate.instant('surveys.L.007.0.0'),
          value: this.getDistribution('surveys.L.007.0.0'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.1'),
          value: this.getDistribution('surveys.L.007.0.1'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.2'),
          value: this.getDistribution('surveys.L.007.0.2'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.3'),
          value: this.getDistribution('surveys.L.007.0.3'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.4'),
          value: this.getDistribution('surveys.L.007.0.4'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.5'),
          value: this.getDistribution('surveys.L.007.0.5'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.6'),
          value: this.getDistribution('surveys.L.007.0.6'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.7'),
          value: this.getDistribution('surveys.L.007.0.7'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.8'),
          value: this.getDistribution('surveys.L.007.0.8'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.9'),
          value: this.getDistribution('surveys.L.007.0.9'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.10'),
          value: this.getDistribution('surveys.L.007.0.10'),
        },
        {
          label: this.translate.instant('surveys.L.007.0.11'),
          value: this.getDistribution('surveys.L.007.0.11'),
        },
      ],
    };
  }

  private getDistribution(key: string): number {
    let count = 0;
    this.adminSurveyResults.forEach((it) => (count += it.l_007_0.includes(key) ? 1 : 0));
    return this.reportService.calcPercentage(count, this.adminSurveyResults.length);
  }
}
