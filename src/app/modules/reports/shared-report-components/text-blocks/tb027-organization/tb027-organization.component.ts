import { TranslateService } from '@ngx-translate/core';
import { Component, Input } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-tb027-organization',
  templateUrl: './tb027-organization.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Tb027OrganizationComponent {
  @Input() data: AdministrationSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  public getContent(): string {
    const l0091 = this.data.map((it) => it.l_009_1);
    const l0091yes = this.reportService.filterAnswers(l0091, 'yes');

    const percentageCooperation = this.translate.instant(
      'reports.survey.TB_027.organization.content',
      {
        percentage: this.reportService.calcPercentage(l0091yes.length, l0091.length),
      },
    );
    const attendees = this.translate.instant('reports.survey.administration.amountAttendees', {
      amountAttendees: this.data.length,
    });
    return `${percentageCooperation} ${attendees}`;
  }
}
