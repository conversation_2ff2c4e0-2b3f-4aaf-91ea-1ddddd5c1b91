import { Component, Input } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-attachment002-comparison',
  templateUrl: './attachment002-comparison.component.html',
  styleUrls: ['./attachment002-comparison.component.scss'],
})
export class Attachment002ComparisonComponent {
  @Input() setA: ParentalSurveyResult[];
  @Input() setB: ParentalSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor() {}

  getAnswers(data: ParentalSurveyResult[]): Array<string> {
    return data.map((it) => it.fam_005_3);
  }
}
