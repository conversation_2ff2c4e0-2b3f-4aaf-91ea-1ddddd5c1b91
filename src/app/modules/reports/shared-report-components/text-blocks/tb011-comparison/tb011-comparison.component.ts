import { Component, Input } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-tb011-comparison',
  templateUrl: './tb011-comparison.component.html',
  styleUrls: ['./tb011-comparison.component.scss'],
})
export class Tb011ComparisonComponent {
  @Input() setA: AdministrationSurveyResult[];
  @Input() setB: AdministrationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor() {}
}
