import { Component, Input, OnInit } from '@angular/core';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';
import { TranslateService } from '@ngx-translate/core';
import { MultiBarChartWithGridConfiguration } from '../../../../shared/components/charts/multi-bar-chart-with-lines/multi-bar-chart-with-lines.component';
import { CHART_COLORS } from '../../../../../utils/chart-colors';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';

@Component({
  selector: 'app-abb010-comparison',
  templateUrl: './abb010-comparison.component.html',
  styleUrls: ['./abb010-comparison.component.scss'],
})
export class Abb010ComparisonComponent implements OnInit {
  @Input() setA: StaffSurveyResult[];
  @Input() setB: StaffSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public setAChartConfiguration: MultiBarChartWithGridConfiguration;
  public setBChartConfiguration: MultiBarChartWithGridConfiguration;

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.setAChartConfiguration = this.initBarChartConfiguration(this.setA, CHART_COLORS.green);
    this.setBChartConfiguration = this.initBarChartConfiguration(
      this.setB,
      CHART_COLORS.dark_green,
    );
  }

  private initBarChartConfiguration(
    data: StaffSurveyResult[],
    color: string,
  ): MultiBarChartWithGridConfiguration {
    return {
      color,
      labelSuffix: '%',
      barConfigurations: [
        {
          label: this.translate.instant('surveys.pFP.005.0.0'),
          value: this.getDistribution(data, 'surveys.pFP.005.0.0'),
        },
        {
          label: this.translate.instant('surveys.pFP.005.0.1'),
          value: this.getDistribution(data, 'surveys.pFP.005.0.1'),
        },
        {
          label: this.translate.instant('surveys.pFP.005.0.2'),
          value: this.getDistribution(data, 'surveys.pFP.005.0.2'),
        },
        {
          label: this.translate.instant('surveys.pFP.005.0.3'),
          value: this.getDistribution(data, 'surveys.pFP.005.0.3'),
        },
      ],
    };
  }

  public getFooter(): string {
    return this.reportService.getComparisonReportIllustrationFooter(
      this.setA.length,
      this.setB.length,
      this.setAPeriod,
      this.setBPeriod,
      SurveyType.STAFF_SURVEY,
    );
  }

  private getDistribution(data: StaffSurveyResult[], key: string): number {
    let count = 0;
    data.forEach((it) => (count += it.pfp_005_0.includes(key) ? 1 : 0));
    return this.reportService.calcPercentage(count, data.length);
  }

  public getLegend(): LegendEntry[] {
    return [
      { description: this.setAPeriod, color: CHART_COLORS.green },
      { description: this.setBPeriod, color: CHART_COLORS.dark_green },
    ];
  }
}
