import { Component, Input, OnInit } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb012-comparison',
  templateUrl: './abb012-comparison.component.html',
  styleUrls: ['./abb012-comparison.component.scss'],
})
export class Abb012ComparisonComponent implements OnInit {
  @Input() setA: ParentalSurveyResult[];
  @Input() setB: ParentalSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public segmentConfiguration0: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration1: SingleBarChartWithDescriptionConfiguration[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getComparisonReportIllustrationFooter(
      this.setA.length,
      this.setB.length,
      this.setAPeriod,
      this.setBPeriod,
      SurveyType.PARENTAL_SURVEY,
      this.translate.instant('reports.survey.description.familiesAgree'),
    );
  }

  private initBarChartConfiguration(): void {
    this.segmentConfiguration0 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.fam_001_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.fam_001_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration1 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.fam_002_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.fam_002_0)),
          labelSuffix: '%',
        },
      },
    ];
  }
}
