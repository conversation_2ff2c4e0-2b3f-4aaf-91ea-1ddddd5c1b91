<h3>{{ reportService.getIllustrationTitleTranslation(11) }}</h3>
<i>{{ 'reports.survey.ABB_015.location.illustration.description' | translate }}</i>

<span
  *ngFor="let data of chartConfiguration; last as isLast; let index = index"
  [class]="getBarChartWrapperClass(index)"
>
  <app-multi-bar-chart-with-description
    *ngIf="!isLast"
    [configuration]="data.chartConfiguration"
    [header]="data.header"
  >
  </app-multi-bar-chart-with-description>

  <app-multi-bar-chart-with-description
    *ngIf="isLast"
    [configuration]="data.chartConfiguration"
    [header]="data.header"
    [legendConfiguration]="legendMultiBarChart"
    [footer]="getFooter()"
  >
  </app-multi-bar-chart-with-description>
</span>
