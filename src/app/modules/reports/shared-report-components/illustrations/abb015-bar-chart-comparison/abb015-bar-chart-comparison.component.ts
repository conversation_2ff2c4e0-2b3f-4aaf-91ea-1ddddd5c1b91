import { Component, Input, OnInit } from '@angular/core';
import { ReportService } from '../../../report.service';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { CHART_COLORS } from '../../../../../utils/chart-colors';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';

@Component({
  selector: 'app-abb015-bar-chart-comparison',
  templateUrl: './abb015-bar-chart-comparison.component.html',
})
export class Abb015BarChartComparisonComponent implements OnInit {
  @Input() setAParentalSurveyResults: ParentalSurveyResult[];
  @Input() setBParentalSurveyResults: ParentalSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public chartConfiguration: Array<ChartConfiguration> = [];
  public legendMultiBarChart: LegendEntry[];

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }

  getBarChartWrapperClass(index: number): string {
    const indexBreakPoint = 6;
    return index >= indexBreakPoint && index === indexBreakPoint
      ? 'illustration-11-comparison-break'
      : '';
  }

  getFooter(): string {
    const note = this.translate.instant('reports.survey.note');
    const familiesAgreeSetA = this.translate.instant('reports.survey.family.amountAttendees', {
      amountAttendees: this.setAParentalSurveyResults.length,
    });
    const familiesAgreeSetB = this.translate.instant('reports.survey.family.amountAttendees', {
      amountAttendees: this.setBParentalSurveyResults.length,
    });

    return `${note} ${this.setAPeriod} - ${familiesAgreeSetA} ${this.setBPeriod} - ${familiesAgreeSetB}`;
  }

  private getAnswerSegment(answer: string): string {
    return answer.split('.')[4];
  }

  private initBarChartConfiguration(): void {
    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_1)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_1)),
        this.translate.instant('surveys.Fam.004.1.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_2)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_2)),
        this.translate.instant('surveys.Fam.004.2.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_3)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_3)),
        this.translate.instant('surveys.Fam.004.3.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_4)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_4)),
        this.translate.instant('surveys.Fam.004.4.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_5)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_5)),
        this.translate.instant('surveys.Fam.004.5.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_6)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_6)),
        this.translate.instant('surveys.Fam.004.6.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_7)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_7)),
        this.translate.instant('surveys.Fam.004.7.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_8)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_8)),
        this.translate.instant('surveys.Fam.004.8.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_9)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_9)),
        this.translate.instant('surveys.Fam.004.9.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_10)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_10)),
        this.translate.instant('surveys.Fam.004.10.title'),
      ),
    );

    this.chartConfiguration.push(
      this.getChartConfiguration(
        this.setAParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_11)),
        this.setBParentalSurveyResults.map((it) => this.getAnswerSegment(it.fam_004_11)),
        this.translate.instant('surveys.Fam.004.11.title'),
      ),
    );
  }

  private getChartConfiguration(
    setAData: Array<string>,
    setBData: Array<string>,
    header: string,
  ): ChartConfiguration {
    const setAAnswers = this.getOfferFrequency(setAData);
    const setBAnswers = this.getOfferFrequency(setBData);
    return {
      header,
      chartConfiguration: [
        {
          description: this.setAPeriod,
          barChartConfiguration: {
            data: [
              {
                value: this.reportService.calcPercentage(setAAnswers.moreOften, setAData.length),
                color: CHART_COLORS.dark_green,
              },
              {
                value: this.reportService.calcPercentage(setAAnswers.justEnough, setAData.length),
                color: CHART_COLORS.green,
              },
              {
                value: this.reportService.calcPercentage(setAAnswers.lessOften, setAData.length),
                color: CHART_COLORS.light_green,
              },
              {
                value: this.reportService.calcPercentage(setAAnswers.notAtAll, setAData.length),
                color: CHART_COLORS.purple,
              },
              {
                value: this.reportService.calcPercentage(
                  setAAnswers.neverBeenOffered,
                  setAData.length,
                ),
                color: CHART_COLORS.dark_purple,
              },
            ],
            labelSuffix: '%',
          },
        },
        {
          description: this.setBPeriod,
          barChartConfiguration: {
            data: [
              {
                value: this.reportService.calcPercentage(setBAnswers.moreOften, setBData.length),
                color: CHART_COLORS.dark_green,
              },
              {
                value: this.reportService.calcPercentage(setBAnswers.justEnough, setBData.length),
                color: CHART_COLORS.green,
              },
              {
                value: this.reportService.calcPercentage(setBAnswers.lessOften, setBData.length),
                color: CHART_COLORS.light_green,
              },
              {
                value: this.reportService.calcPercentage(setBAnswers.notAtAll, setBData.length),
                color: CHART_COLORS.purple,
              },
              {
                value: this.reportService.calcPercentage(
                  setBAnswers.neverBeenOffered,
                  setBData.length,
                ),
                color: CHART_COLORS.dark_purple,
              },
            ],
            labelSuffix: '%',
          },
        },
      ],
    };
  }

  private getOfferFrequency(data: Array<string>): OfferFrequency {
    return {
      moreOften: data.filter((it) => it === '0').length,
      justEnough: data.filter((it) => it === '1').length,
      lessOften: data.filter((it) => it === '2').length,
      notAtAll: data.filter((it) => it === '3').length,
      neverBeenOffered: data.filter((it) => it === '4').length,
    };
  }

  private initLegend(): void {
    this.legendMultiBarChart = [
      {
        description: this.translate.instant('reports.survey.ABB_015.more_often'),
        color: CHART_COLORS.dark_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.exactly_right'),
        color: CHART_COLORS.green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.less_often'),
        color: CHART_COLORS.light_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.no_wish'),
        color: CHART_COLORS.purple,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.not_existing'),
        color: CHART_COLORS.dark_purple,
      },
    ];
  }
}

interface OfferFrequency {
  moreOften: number;
  justEnough: number;
  lessOften: number;
  notAtAll: number;
  neverBeenOffered: number;
}

interface ChartConfiguration {
  header: string;
  chartConfiguration: Array<SingleBarChartWithDescriptionConfiguration>;
}
