import { Component, Input, OnInit } from '@angular/core';
import { ReportService } from '../../../report.service';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-abb015-table-comparison',
  templateUrl: './abb015-table-comparison.component.html',
  styleUrls: ['./abb015-table-comparison.component.scss'],
})
export class Abb015TableComparisonComponent implements OnInit {
  @Input() setAAdministrationSurveyResult: AdministrationSurveyResult[];
  @Input() setBAdministrationSurveyResult: AdministrationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public setAAdministrationAnswers: Array<string>;
  public setBAdministrationAnswers: Array<string>;

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.initAdministrationAnswers();
  }

  getAdministrationTableRowTitle(index: number): string {
    const key = `surveys.L.008.${index + 1}.title`;
    return this.translate.instant(key);
  }

  private initAdministrationAnswers(): void {
    this.setAAdministrationAnswers = this.getAdministrationAnswers(
      this.setAAdministrationSurveyResult[0],
    );
    this.setBAdministrationAnswers = this.getAdministrationAnswers(
      this.setBAdministrationSurveyResult[0],
    );
  }

  private getAdministrationAnswers(data: AdministrationSurveyResult): Array<string> {
    return [
      data.l_008_1,
      data.l_008_2,
      data.l_008_3,
      data.l_008_4,
      data.l_008_5,
      data.l_008_6,
      data.l_008_7,
      data.l_008_8,
      data.l_008_9,
      data.l_008_10,
      data.l_008_11,
    ];
  }
}
