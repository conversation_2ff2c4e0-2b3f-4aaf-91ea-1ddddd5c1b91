<h3>{{ reportService.getTableTitleTranslation(5) }}</h3>
<p>
  <i>{{ 'reports.survey.ABB_015.location.table.description' | translate }}</i>
</p>

<table>
  <tr>
    <th>{{ 'reports.survey.ABB_015.location.table.conversation_formats' | translate }}</th>
    <th class="bordered-cell table-cell-padding td-increased-width">{{ setAPeriod }}</th>
    <th class="bordered-cell table-cell-padding td-increased-width">{{ setBPeriod }}</th>
  </tr>
  <tr *ngFor="let answer of setAAdministrationAnswers; let index; let i = index">
    <td>{{ getAdministrationTableRowTitle(i) }}</td>
    <td class="bordered-cell">
      {{ answer | translate }}
    </td>
    <td class="bordered-cell">
      {{ setBAdministrationAnswers[i] | translate }}
    </td>
  </tr>
</table>
