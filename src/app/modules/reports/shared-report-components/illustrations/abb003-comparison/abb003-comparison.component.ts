import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb003-comparison',
  templateUrl: './abb003-comparison.component.html',
  styleUrls: ['./abb003-comparison.component.scss'],
})
export class Abb003ComparisonComponent implements OnInit {
  @Input() setA: StaffSurveyResult[];
  @Input() setB: StaffSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public segmentConfiguration0: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration1: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration2: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration3: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration4: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration5: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration6: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration7: SingleBarChartWithDescriptionConfiguration[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  private initBarChartConfiguration(): void {
    this.segmentConfiguration0 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_008_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_008_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration1 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_009_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_009_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration2 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_010_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_010_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration3 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_011_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_011_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration4 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_012_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_012_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration5 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_013_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_013_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration6 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_014_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_014_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration7 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_015_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_015_0)),
          labelSuffix: '%',
        },
      },
    ];
  }

  public getFooter(): string {
    return this.reportService.getComparisonReportIllustrationFooter(
      this.setA.length,
      this.setB.length,
      this.setAPeriod,
      this.setBPeriod,
      SurveyType.STAFF_SURVEY,
      this.translate.instant('reports.survey.description.staffAgreesCollaboration_comparison'),
    );
  }
}
