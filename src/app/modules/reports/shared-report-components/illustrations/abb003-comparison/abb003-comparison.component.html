<app-multi-bar-chart-with-description
  [configuration]="segmentConfiguration0"
  [title]="reportService.getIllustrationTitleTranslation(2)"
  [header]="'reports.survey.ABB_003.description_comparison' | translate"
  [description]="'reports.survey.ABB_003.1' | translate"
>
</app-multi-bar-chart-with-description>

<app-multi-bar-chart-with-description
  [configuration]="segmentConfiguration1"
  [description]="'reports.survey.ABB_003.2' | translate"
>
</app-multi-bar-chart-with-description>

<app-multi-bar-chart-with-description
  [configuration]="segmentConfiguration2"
  [description]="'reports.survey.ABB_003.3' | translate"
>
</app-multi-bar-chart-with-description>

<app-multi-bar-chart-with-description
  [configuration]="segmentConfiguration3"
  [description]="'reports.survey.ABB_003.4' | translate"
>
</app-multi-bar-chart-with-description>

<app-multi-bar-chart-with-description
  [configuration]="segmentConfiguration4"
  [description]="'reports.survey.ABB_003.5' | translate"
>
</app-multi-bar-chart-with-description>

<div class="page-break">
  <app-multi-bar-chart-with-description
    [configuration]="segmentConfiguration5"
    [description]="'reports.survey.ABB_003.6' | translate"
  >
  </app-multi-bar-chart-with-description>

  <app-multi-bar-chart-with-description
    [configuration]="segmentConfiguration6"
    [description]="'reports.survey.ABB_003.7' | translate"
  >
  </app-multi-bar-chart-with-description>

  <app-multi-bar-chart-with-description
    [configuration]="segmentConfiguration7"
    [description]="'reports.survey.ABB_003.8' | translate"
    [legendConfiguration]="reportService.getYesNoMultiBarChartLegend()"
    [footer]="getFooter()"
  >
  </app-multi-bar-chart-with-description>
</div>
