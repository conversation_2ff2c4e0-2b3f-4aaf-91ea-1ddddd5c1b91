import { ReportService } from '../../../report.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, Input } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb003',
  templateUrl: './abb003.component.html',
})
export class Abb003Component implements OnInit {
  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];

  @Input() data: StaffSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.STAFF_SURVEY,
      this.data.length,
      this.translate.instant('reports.survey.description.staffAgreesCollaboration'),
    );
  }

  private initBarChartConfiguration(): void {
    const pfp008 = this.data.map((it) => {
      return it.pfp_008_0;
    });

    const pfp009 = this.data.map((it) => {
      return it.pfp_009_0;
    });

    const pfp010 = this.data.map((it) => {
      return it.pfp_010_0;
    });

    const pfp011 = this.data.map((it) => {
      return it.pfp_011_0;
    });
    const pfp012 = this.data.map((it) => {
      return it.pfp_012_0;
    });

    const pfp013 = this.data.map((it) => {
      return it.pfp_013_0;
    });

    const pfp014 = this.data.map((it) => {
      return it.pfp_014_0;
    });

    const pfp015 = this.data.map((it) => {
      return it.pfp_015_0;
    });

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_003.1'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp008),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_003.2'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp009),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_003.3'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp010),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_003.4'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp011),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_003.5'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp012),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_003.6'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp013),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_003.7'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp014),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_003.8'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp015),
          labelSuffix: '%',
        },
      },
    ];
  }
}
