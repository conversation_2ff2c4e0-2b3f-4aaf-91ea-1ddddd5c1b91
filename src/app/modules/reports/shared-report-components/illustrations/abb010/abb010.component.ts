import { Component, Input, OnInit } from '@angular/core';
import { ReportService } from '../../../report.service';
import { TranslateService } from '@ngx-translate/core';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { CHART_COLORS } from '../../../../../utils/chart-colors';
// tslint:disable-next-line:max-line-length
import { MultiBarChartWithGridConfiguration } from '../../../../shared/components/charts/multi-bar-chart-with-lines/multi-bar-chart-with-lines.component';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb010',
  templateUrl: './abb010.component.html',
})
export class Abb010Component implements OnInit {
  @Input() data: StaffSurveyResult[];

  public barChartConfiguration: MultiBarChartWithGridConfiguration;

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(SurveyType.STAFF_SURVEY, this.data.length);
  }

  private initBarChartConfiguration(): void {
    this.barChartConfiguration = {
      color: CHART_COLORS.green,
      labelSuffix: '%',
      barConfigurations: [
        {
          label: this.translate.instant('surveys.pFP.005.0.0'),
          value: this.getDistribution('surveys.pFP.005.0.0'),
        },
        {
          label: this.translate.instant('surveys.pFP.005.0.1'),
          value: this.getDistribution('surveys.pFP.005.0.1'),
        },
        {
          label: this.translate.instant('surveys.pFP.005.0.2'),
          value: this.getDistribution('surveys.pFP.005.0.2'),
        },
        {
          label: this.translate.instant('surveys.pFP.005.0.3'),
          value: this.getDistribution('surveys.pFP.005.0.3'),
        },
      ],
    };
  }

  private getDistribution(key: string): number {
    let count = 0;
    this.data.forEach((it) => (count += it.pfp_005_0.includes(key) ? 1 : 0));
    return this.reportService.calcPercentage(count, this.data.length);
  }
}
