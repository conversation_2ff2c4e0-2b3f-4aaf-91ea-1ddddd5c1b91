import { Component, Input, OnInit } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { SingleBarChartConfiguration } from '../../../../shared/components/charts/single-bar-chart/single-bar-chart.component';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { CHART_COLORS } from '../../../../../utils/chart-colors';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb017-organization',
  templateUrl: './abb017-organization.component.html',
})
export class Abb017OrganizationComponent implements OnInit {
  @Input() data: AdministrationSurveyResult[];

  public barChartConfiguration: SingleBarChartConfiguration;
  public legendMultiBarChart: LegendEntry[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.ADMINISTRATION_SURVEY,
      this.data.length,
    );
  }

  private initBarChartConfiguration(): void {
    const l0092 = this.data.map((it) => it.l_009_2);

    const l0092disagreeCompletely = l0092.filter((it) => it.includes('surveys.L.009.2.0'));
    const l0092disagreeModerately = l0092.filter((it) => it.includes('surveys.L.009.2.1'));
    const l0092agreeModerately = l0092.filter((it) => it.includes('surveys.L.009.2.2'));
    const l0092agreeCompletelyOrDontKnow = l0092.filter(
      (it) => it.includes('surveys.L.009.2.3') || it.includes('surveys.L.009.2.4'),
    );

    this.barChartConfiguration = {
      data: [
        {
          value: this.reportService.calcPercentage(
            l0092agreeCompletelyOrDontKnow.length,
            l0092.length,
          ),
          color: CHART_COLORS.green,
        },
        {
          value: this.reportService.calcPercentage(l0092agreeModerately.length, l0092.length),
          color: CHART_COLORS.light_green,
        },
        {
          value: this.reportService.calcPercentage(l0092disagreeModerately.length, l0092.length),
          color: CHART_COLORS.purple,
        },
        {
          value: this.reportService.calcPercentage(l0092disagreeCompletely.length, l0092.length),
          color: CHART_COLORS.dark_purple,
        },
      ],
      labelSuffix: '%',
    };
  }
  private initLegend(): void {
    this.legendMultiBarChart = [
      {
        description: `${this.translate.instant('surveys.L.009.2.3')}/${this.translate.instant('surveys.L.009.2.4')}`,
        color: CHART_COLORS.green,
      },
      { description: this.translate.instant('surveys.L.009.2.2'), color: CHART_COLORS.light_green },
      { description: this.translate.instant('surveys.L.009.2.1'), color: CHART_COLORS.purple },
      { description: this.translate.instant('surveys.L.009.2.0'), color: CHART_COLORS.dark_purple },
    ];
  }
}
