import { ReportService } from '../../../report.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, Input } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb012',
  templateUrl: './abb012.component.html',
})
export class Abb012Component implements OnInit {
  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];

  @Input() data: ParentalSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.PARENTAL_SURVEY,
      this.data.length,
      this.translate.instant('reports.survey.description.familiesAgree'),
    );
  }

  private initBarChartConfiguration(): void {
    const fam0010 = this.data.map((it) => {
      return it.fam_001_0;
    });

    const fam0020 = this.data.map((it) => {
      return it.fam_002_0;
    });

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_012.1'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(fam0010),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_012.2'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(fam0020),
          labelSuffix: '%',
        },
      },
    ];
  }
}
