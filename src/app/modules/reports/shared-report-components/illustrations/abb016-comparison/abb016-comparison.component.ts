import { Component, Input } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-abb016-comparison',
  templateUrl: './abb016-comparison.component.html',
  styleUrls: ['./abb016-comparison.component.scss'],
})
export class Abb016ComparisonComponent {
  @Input() setA: AdministrationSurveyResult[];
  @Input() setB: AdministrationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor() {}
}
