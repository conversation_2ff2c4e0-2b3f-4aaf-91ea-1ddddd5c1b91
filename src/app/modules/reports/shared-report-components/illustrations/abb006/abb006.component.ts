import { ReportService } from '../../../report.service';
// tslint:disable-next-line:max-line-length
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, Input } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb006',
  templateUrl: './abb006.component.html',
})
export class Abb006Component implements OnInit {
  @Input() data: StaffSurveyResult[];

  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.STAFF_SURVEY,
      this.data.length,
      this.translate.instant('reports.survey.description.materialsAvailable'),
    );
  }

  private initBarChartConfiguration(): void {
    const pfp0031 = this.data.map((it) => {
      return it.pfp_003_1;
    });

    const pfp0032 = this.data.map((it) => {
      return it.pfp_003_2;
    });

    const pfp0033 = this.data.map((it) => {
      return it.pfp_003_3;
    });

    const pfp0034 = this.data.map((it) => {
      return it.pfp_003_4;
    });

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_006.1'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0031),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_006.2'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0032),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_006.3'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0033),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_006.4'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0034),
          labelSuffix: '%',
        },
      },
    ];
  }
}
