import { ReportService } from '../../../report.service';
// tslint:disable-next-line:max-line-length
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, Input, OnInit } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb004',
  templateUrl: './abb004.component.html',
})
export class Abb004Component implements OnInit {
  @Input() data: StaffSurveyResult[];

  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.STAFF_SURVEY,
      this.data.length,
      this.translate.instant('reports.survey.description.materialsAvailable'),
    );
  }

  private initBarChartConfiguration(): void {
    const pfp0011 = this.data.map((it) => {
      return it.pfp_001_1;
    });

    const pfp0012 = this.data.map((it) => {
      return it.pfp_001_2;
    });

    const pfp0013 = this.data.map((it) => {
      return it.pfp_001_3;
    });

    const pfp0014 = this.data.map((it) => {
      return it.pfp_001_4;
    });

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_004.1'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0011),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_004.2'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0012),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_004.3'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0013),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_004.4'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0014),
          labelSuffix: '%',
        },
      },
    ];
  }
}
