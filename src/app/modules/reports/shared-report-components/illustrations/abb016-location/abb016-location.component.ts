import { ReportService } from '../../../report.service';
// tslint:disable-next-line:max-line-length
import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';

@Component({
  selector: 'app-abb016-location',
  templateUrl: './abb016-location.component.html',
})
export class Abb016LocationComponent {
  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];
  public legendMultiBarChart: LegendEntry[];

  @Input() parentalData: ParentalSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}
}
