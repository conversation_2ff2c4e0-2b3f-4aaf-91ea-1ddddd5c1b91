import { Component, Input } from '@angular/core';
import { ReportService } from '../../../report.service';
import {
  AdministrationSurveyResult,
  OrganizationSurveyResult,
} from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-abb013-location',
  templateUrl: './abb013-location.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Abb013LocationComponent {
  @Input() organizationSurveyResults: OrganizationSurveyResult[];
  @Input() administrationSurveyResults: AdministrationSurveyResult[];

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}
  setCheckmark(answer: string): boolean {
    return answer.includes('yes');
  }

  getAdministrationHeader(): string {
    return this.translate.instant('reports.survey.ABB_013.administration_header.title', {
      numberOfAdministrations: this.administrationSurveyResults.length,
    });
  }
}
