<h3>{{ reportService.getTableTitleTranslation(3) }}</h3>
<i>{{ 'reports.survey.ABB_013.location.description' | translate }}</i>
<table class="table-3">
  <tr>
    <th class="table-width-third"></th>
    <th class="table-width-third cell-align-top">
      {{ 'reports.survey.ABB_013.organization_header.title' | translate }}
      <p class="font-normal">
        {{ 'reports.survey.ABB_013.organization_header.sub_title' | translate }}
      </p>
    </th>
    <th class="table-width-third cell-align-top">
      {{ getAdministrationHeader() }}
      <p class="font-normal">
        {{ 'reports.survey.ABB_013.administration_header.sub_title' | translate }}
      </p>
    </th>
  </tr>
  <tr>
    <td class="cell-bold">{{ 'reports.survey.ABB_013.childs_best_interests' | translate }}</td>
    <td>
      <img
        *ngIf="setCheckmark(organizationSurveyResults[0].t_002_1)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
    <td>
      <img
        *ngIf="setCheckmark(administrationSurveyResults[0].l_001_2)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td class="cell-bold">{{ 'reports.survey.ABB_013.child_rights' | translate }}</td>
    <td>
      <img
        *ngIf="setCheckmark(organizationSurveyResults[0].t_002_2)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
    <td>
      <img
        *ngIf="setCheckmark(administrationSurveyResults[0].l_001_1)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
</table>
