import { Component, Input } from '@angular/core';
import { ReportService } from '../../../report.service';
import {
  AdministrationSurveyResult,
  OrganizationSurveyResult,
} from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-abb013',
  templateUrl: './abb013.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Abb013Component {
  @Input() organizationSurveyResults: OrganizationSurveyResult[];
  @Input() administrationSurveyResults: AdministrationSurveyResult[];

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  setCheckmark(answer: string): boolean {
    return answer.includes('yes');
  }

  getAdministrationHeader(): string {
    return this.translate.instant('reports.survey.ABB_013.administration_header.title', {
      numberOfAdministrations: this.administrationSurveyResults.length,
    });
  }

  getChildInterestsApproval(): string {
    const answers = this.administrationSurveyResults.map((it) => it.l_001_1);
    const approvalCount = answers.filter((it) => it.includes('yes')).length;
    return this.getApprovalTranslation(
      this.reportService.calcPercentage(approvalCount, answers.length),
    );
  }

  getChildRightsApproval(): string {
    const answers = this.administrationSurveyResults.map((it) => it.l_001_2);
    const approvalCount = answers.filter((it) => it.includes('yes')).length;
    return this.getApprovalTranslation(
      this.reportService.calcPercentage(approvalCount, answers.length),
    );
  }

  private getApprovalTranslation(value: number): string {
    return this.translate.instant('reports.survey.approval', {
      percentage: value,
    });
  }
}
