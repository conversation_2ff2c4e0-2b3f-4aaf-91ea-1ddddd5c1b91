import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-abb001',
  templateUrl: './abb001.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Abb001Component {
  // tslint:disable-next-line:variable-name
  @Input() t_004_1_answer: string;
  // tslint:disable-next-line:variable-name
  @Input() t_004_2_answer: string;
  // tslint:disable-next-line:variable-name
  @Input() t_004_3_answer: string;
  // tslint:disable-next-line:variable-name
  @Input() t_004_4_answer: string;
  @Input() freeFormAnswer: string;

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  setCheckmark(cellId: number, answer: string): boolean {
    let answerId = answer.split('.')[4];
    // answer 2 and 3 are summarized in table cell 2
    answerId = answerId === '3' ? '2' : answerId;
    return answerId === cellId.toString();
  }

  counter(i: number): Array<number> {
    return Array(i);
  }
}
