<h3>{{ reportService.getTableTitleTranslation(1) }}</h3>
<i>{{ 'reports.survey.ABB_001.description' | translate }}</i>
<table>
  <tr>
    <th>{{ 'reports.survey.ABB_001.responsibility' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.location_administration' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.organization' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.unknown' | translate }}</th>
  </tr>
  <tr>
    <td>{{ 'reports.survey.ABB_001.job_reference' | translate }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, t_004_1_answer)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td>{{ 'reports.survey.ABB_001.job_interviews' | translate }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, t_004_2_answer)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td>{{ 'reports.survey.ABB_001.family_contracts' | translate }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, t_004_3_answer)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td>{{ 'reports.survey.ABB_001.personnel_development' | translate }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, t_004_4_answer)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td>{{ 'reports.survey.ABB_001.health_prevention' | translate }}</td>
    <td colspan="3">{{ freeFormAnswer }}</td>
  </tr>
</table>
