import { Component, Input, OnInit } from '@angular/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
// tslint:disable-next-line:max-line-length
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';
import { CHART_COLORS } from '../../../../../utils/chart-colors';

@Component({
  selector: 'app-abb009-comparison',
  templateUrl: './abb009-comparison.component.html',
  styleUrls: ['./abb009-comparison.component.scss'],
})
export class Abb009ComparisonComponent implements OnInit {
  @Input() setA: ParentalSurveyResult[];
  @Input() setB: ParentalSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public chartConfiguration0: SingleBarChartWithDescriptionConfiguration[];
  public chartConfiguration1: SingleBarChartWithDescriptionConfiguration[];
  public legendMultiBarChart: LegendEntry[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }

  public getFooter(): string {
    return this.reportService.getComparisonReportIllustrationFooter(
      this.setA.length,
      this.setB.length,
      this.setAPeriod,
      this.setBPeriod,
      SurveyType.STAFF_SURVEY,
    );
  }

  private initBarChartConfiguration(): void {
    let setAData = this.setA.map((it) => it.fam_005_1);
    let setBData = this.setB.map((it) => it.fam_005_1);
    const setAConf0AgreeCompletely = setAData.filter(
      (it) => it.includes('surveys.Fam.005.1.3') || it.includes('surveys.Fam.005.1.4'),
    ).length;
    const setAConf0Agree = setAData.filter((it) => it.includes('surveys.Fam.005.1.2')).length;
    const setAConf0Disagree = setAData.filter((it) => it.includes('surveys.Fam.005.1.1')).length;
    const setAConf0DisagreeCompletely = setAData.filter((it) =>
      it.includes('surveys.Fam.005.1.0'),
    ).length;

    const setBConf0AgreeCompletely = setBData.filter(
      (it) => it.includes('surveys.Fam.005.1.3') || it.includes('surveys.Fam.005.1.4'),
    ).length;
    const setBConf0Agree = setBData.filter((it) => it.includes('surveys.Fam.005.1.2')).length;
    const setBConf0Disagree = setBData.filter((it) => it.includes('surveys.Fam.005.1.1')).length;
    const setBConf0DisagreeCompletely = setBData.filter((it) =>
      it.includes('surveys.Fam.005.1.0'),
    ).length;

    setAData = this.setA.map((it) => it.fam_005_2);
    setBData = this.setB.map((it) => it.fam_005_2);
    const setAConf1AgreeCompletely = setAData.filter(
      (it) => it.includes('surveys.Fam.005.2.3') || it.includes('surveys.Fam.005.2.4'),
    ).length;
    const setAConf1Agree = setAData.filter((it) => it.includes('surveys.Fam.005.2.2')).length;
    const setAConf1Disagree = setAData.filter((it) => it.includes('surveys.Fam.005.2.1')).length;
    const setAConf1DisagreeCompletely = setAData.filter((it) =>
      it.includes('surveys.Fam.005.2.0'),
    ).length;

    const setBConf1AgreeCompletely = setBData.filter(
      (it) => it.includes('surveys.Fam.005.2.3') || it.includes('surveys.Fam.005.2.4'),
    ).length;
    const setBConf1Agree = setBData.filter((it) => it.includes('surveys.Fam.005.2.2')).length;
    const setBConf1Disagree = setBData.filter((it) => it.includes('surveys.Fam.005.2.1')).length;
    const setBConf1DisagreeCompletely = setBData.filter((it) =>
      it.includes('surveys.Fam.005.2.0'),
    ).length;

    this.chartConfiguration0 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(setAConf0AgreeCompletely, setAData.length),
              color: CHART_COLORS.dark_green,
            },
            {
              value: this.reportService.calcPercentage(setAConf0Agree, setAData.length),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(setAConf0Disagree, setAData.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(
                setAConf0DisagreeCompletely,
                setAData.length,
              ),
              color: CHART_COLORS.purple,
            },
          ],
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(setBConf0AgreeCompletely, setBData.length),
              color: CHART_COLORS.dark_green,
            },
            {
              value: this.reportService.calcPercentage(setBConf0Agree, setBData.length),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(setBConf0Disagree, setBData.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(
                setBConf0DisagreeCompletely,
                setBData.length,
              ),
              color: CHART_COLORS.purple,
            },
          ],
          labelSuffix: '%',
        },
      },
    ];

    this.chartConfiguration1 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(setAConf1AgreeCompletely, setAData.length),
              color: CHART_COLORS.dark_green,
            },
            {
              value: this.reportService.calcPercentage(setAConf1Agree, setAData.length),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(setAConf1Disagree, setAData.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(
                setAConf1DisagreeCompletely,
                setAData.length,
              ),
              color: CHART_COLORS.purple,
            },
          ],
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(setBConf1AgreeCompletely, setBData.length),
              color: CHART_COLORS.dark_green,
            },
            {
              value: this.reportService.calcPercentage(setBConf1Agree, setBData.length),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(setBConf1Disagree, setBData.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(
                setBConf1DisagreeCompletely,
                setBData.length,
              ),
              color: CHART_COLORS.purple,
            },
          ],
          labelSuffix: '%',
        },
      },
    ];
  }
  private initLegend(): void {
    this.legendMultiBarChart = [
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.completely_agree'),
        color: CHART_COLORS.green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.agree'),
        color: CHART_COLORS.light_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.disagree'),
        color: CHART_COLORS.purple,
      },
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.completely_disagree'),
        color: CHART_COLORS.dark_purple,
      },
    ];
  }
}
