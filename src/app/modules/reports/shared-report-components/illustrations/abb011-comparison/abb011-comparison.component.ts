import { Component, Input, OnInit } from '@angular/core';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb011-comparison',
  templateUrl: './abb011-comparison.component.html',
  styleUrls: ['./abb011-comparison.component.scss'],
})
export class Abb011ComparisonComponent implements OnInit {
  @Input() setA: StaffSurveyResult[];
  @Input() setB: StaffSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public segmentConfiguration0: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration1: SingleBarChartWithDescriptionConfiguration[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getComparisonReportIllustrationFooter(
      this.setA.length,
      this.setB.length,
      this.setAPeriod,
      this.setBPeriod,
      SurveyType.STAFF_SURVEY,
      this.translate.instant('reports.survey.description.staffAgreesCollaboration_comparison'),
    );
  }

  private initBarChartConfiguration(): void {
    this.segmentConfiguration0 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_006_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_006_0)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration1 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_007_0)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_007_0)),
          labelSuffix: '%',
        },
      },
    ];
  }
}
