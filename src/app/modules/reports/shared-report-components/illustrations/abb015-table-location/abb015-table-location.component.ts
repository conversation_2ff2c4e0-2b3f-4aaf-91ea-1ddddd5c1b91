import { Component, Input } from '@angular/core';
import { ReportService } from '../../../report.service';
import { TranslateService } from '@ngx-translate/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-abb015-table-location',
  templateUrl: './abb015-table-location.component.html',
})
export class Abb015TableLocationComponent {
  @Input() administrationData: AdministrationSurveyResult;

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}
}
