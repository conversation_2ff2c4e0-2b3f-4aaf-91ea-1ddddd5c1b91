import { ReportService } from '../../../report.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, Input } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb011',
  templateUrl: './abb011.component.html',
})
export class Abb011Component implements OnInit {
  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];

  @Input() data: StaffSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.STAFF_SURVEY,
      this.data.length,
      this.translate.instant('reports.survey.description.languageDiariesAvailable'),
    );
  }

  private initBarChartConfiguration(): void {
    const pfp0060 = this.data.map((it) => {
      return it.pfp_006_0;
    });

    const pfp0070 = this.data.map((it) => {
      return it.pfp_007_0;
    });

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_011.1'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0060),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_011.2'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0070),
          labelSuffix: '%',
        },
      },
    ];
  }
}
