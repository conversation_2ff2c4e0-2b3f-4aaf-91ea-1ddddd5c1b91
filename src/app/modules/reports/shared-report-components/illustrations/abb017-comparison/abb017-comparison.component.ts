import { Component, Input } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-abb017-comparison',
  templateUrl: './abb017-comparison.component.html',
  styleUrls: ['./abb017-comparison.component.scss'],
})
export class Abb017ComparisonComponent {
  @Input() setA: AdministrationSurveyResult[];
  @Input() setB: AdministrationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor(public reportService: ReportService) {}
}
