import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-abb016-organization',
  templateUrl: './abb016-organization.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Abb016OrganizationComponent {
  @Input() data: AdministrationSurveyResult[];

  constructor(public translate: TranslateService) {}
}
