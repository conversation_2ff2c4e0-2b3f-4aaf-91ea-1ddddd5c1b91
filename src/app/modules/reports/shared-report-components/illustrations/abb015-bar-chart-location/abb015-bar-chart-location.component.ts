import { CHART_COLORS } from 'src/app/utils/chart-colors';
import { ReportService } from '../../../report.service';
// tslint:disable-next-line:max-line-length
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { TranslateService } from '@ngx-translate/core';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
import { Component, Input, OnInit } from '@angular/core';
import { SingleBarChartConfiguration } from '../../../../shared/components/charts/single-bar-chart/single-bar-chart.component';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb015-bar-chart-location',
  templateUrl: './abb015-bar-chart-location.component.html',
})
export class Abb015BarChartLocationComponent implements OnInit {
  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];
  public legendMultiBarChart: LegendEntry[];

  @Input() parentalData: ParentalSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }
  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.PARENTAL_SURVEY,
      this.parentalData.length,
    );
  }

  private initLegend(): void {
    this.legendMultiBarChart = [
      {
        description: this.translate.instant('reports.survey.ABB_015.more_often'),
        color: CHART_COLORS.dark_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.exactly_right'),
        color: CHART_COLORS.green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.less_often'),
        color: CHART_COLORS.light_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.no_wish'),
        color: CHART_COLORS.purple,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.not_existing'),
        color: CHART_COLORS.dark_purple,
      },
    ];
  }

  private initBarChartConfiguration(): void {
    const fam0041Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_1.split('.')[4]),
    );
    const fam0042Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_2.split('.')[4]),
    );
    const fam0043Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_3.split('.')[4]),
    );
    const fam0044Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_4.split('.')[4]),
    );
    const fam0045Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_5.split('.')[4]),
    );
    const fam0046Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_6.split('.')[4]),
    );
    const fam0047Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_7.split('.')[4]),
    );
    const fam0048Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_8.split('.')[4]),
    );
    const fam0049Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_9.split('.')[4]),
    );
    const fam00410Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_10.split('.')[4]),
    );
    const fam00411Answers = this.getFilteredAnswers(
      this.parentalData.map((it) => it.fam_004_11.split('.')[4]),
    );

    this.barChartConfiguration = [
      {
        description: this.translate.instant('surveys.L.008.1.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0041Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.2.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0042Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.3.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0043Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.4.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0044Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.5.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0045Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.6.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0046Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.7.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0047Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.8.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0048Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.9.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam0049Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.10.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam00410Answers),
      },
      {
        description: this.translate.instant('surveys.L.008.11.title'),
        barChartConfiguration: this.getSingleBarChartConfig(fam00411Answers),
      },
    ];
  }

  private getFilteredAnswer(answers: Array<string>, answerKey: string): Array<string> {
    return answers.filter((it) => it.includes(answerKey));
  }

  private getFilteredAnswers(answers: Array<string>): Abb015Data {
    return {
      wish: this.reportService.calcPercentage(
        this.getFilteredAnswer(answers, '0').length,
        answers.length,
      ),
      right: this.reportService.calcPercentage(
        this.getFilteredAnswer(answers, '1').length,
        answers.length,
      ),
      wishRarely: this.reportService.calcPercentage(
        this.getFilteredAnswer(answers, '2').length,
        answers.length,
      ),
      dontWish: this.reportService.calcPercentage(
        this.getFilteredAnswer(answers, '3').length,
        answers.length,
      ),
      dontMiss: this.reportService.calcPercentage(
        this.getFilteredAnswer(answers, '4').length,
        answers.length,
      ),
    };
  }

  private getSingleBarChartConfig(data: Abb015Data): SingleBarChartConfiguration {
    return {
      data: [
        {
          value: data.wish,
          color: CHART_COLORS.dark_green,
        },
        {
          value: data.right,
          color: CHART_COLORS.green,
        },
        {
          value: data.wishRarely,
          color: CHART_COLORS.light_green,
        },
        {
          value: data.dontWish,
          color: CHART_COLORS.purple,
        },
        {
          value: data.dontMiss,
          color: CHART_COLORS.dark_purple,
        },
      ],
      labelSuffix: '%',
    };
  }
}

interface Abb015Data {
  wish: number;
  right: number;
  wishRarely: number;
  dontWish: number;
  dontMiss: number;
}
