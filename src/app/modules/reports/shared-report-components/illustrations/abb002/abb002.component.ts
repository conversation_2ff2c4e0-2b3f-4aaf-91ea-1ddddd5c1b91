import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-abb002',
  templateUrl: './abb002.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Abb002Component {
  // tslint:disable-next-line:variable-name
  @Input() t_005_answer_key: string;
  // tslint:disable-next-line:variable-name
  @Input() t_006_answer_key: string;
  // tslint:disable-next-line:variable-name
  @Input() t_007_answer_key: string;

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}
}
