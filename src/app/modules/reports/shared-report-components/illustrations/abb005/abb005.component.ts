import { ReportService } from '../../../report.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, Input } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb005',
  templateUrl: './abb005.component.html',
})
export class Abb005Component implements OnInit {
  @Input() data: StaffSurveyResult[];

  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.STAFF_SURVEY,
      this.data.length,
      this.translate.instant('reports.survey.description.materialsAvailable'),
    );
  }

  private initBarChartConfiguration(): void {
    const pfp0021 = this.data.map((it) => {
      return it.pfp_002_1;
    });

    const pfp0022 = this.data.map((it) => {
      return it.pfp_002_2;
    });

    const pfp0023 = this.data.map((it) => {
      return it.pfp_002_3;
    });

    const pfp0024 = this.data.map((it) => {
      return it.pfp_002_4;
    });

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_005.1'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0021),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_005.2'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0022),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_005.3'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0023),
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_005.4'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(pfp0024),
          labelSuffix: '%',
        },
      },
    ];
  }
}
