<h3>{{ reportService.getTableTitleTranslation(1) }}</h3>
<p>
  <i>{{ 'reports.survey.ABB_001.description_comparison_report' | translate }}</i>
</p>

<table class="table-margin-bottom">
  <tr>
    <th class="table-width-half">{{ 'reports.survey.ABB_001.job_reference' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.location_administration' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.organization' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.unknown' | translate }}</th>
  </tr>
  <tr>
    <td>{{ setAPeriod }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, setAOrganizationSurveyResults[0].t_004_1)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td>{{ setBPeriod }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, setBOrganizationSurveyResults[0].t_004_1)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
</table>

<table class="table-margin-bottom">
  <tr>
    <th class="table-width-half">{{ 'reports.survey.ABB_001.job_interviews' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.location_administration' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.organization' | translate }}</th>
    <th>{{ 'reports.survey.ABB_001.unknown' | translate }}</th>
  </tr>
  <tr>
    <td>{{ setAPeriod }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, setAOrganizationSurveyResults[0].t_004_2)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td>{{ setBPeriod }}</td>
    <td *ngFor="let cell of counter(3); let i = index">
      <img
        *ngIf="setCheckmark(i, setBOrganizationSurveyResults[0].t_004_2)"
        class="image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
</table>

<div class="page-break">
  <table class="table-margin-bottom">
    <tr>
      <th class="table-width-half">{{ 'reports.survey.ABB_001.family_contracts' | translate }}</th>
      <th>{{ 'reports.survey.ABB_001.location_administration' | translate }}</th>
      <th>{{ 'reports.survey.ABB_001.organization' | translate }}</th>
      <th>{{ 'reports.survey.ABB_001.unknown' | translate }}</th>
    </tr>
    <tr>
      <td>{{ setAPeriod }}</td>
      <td *ngFor="let cell of counter(3); let i = index">
        <img
          *ngIf="setCheckmark(i, setAOrganizationSurveyResults[0].t_004_3)"
          class="image"
          src="/assets/svg/complete_dark.svg"
          alt="logo"
        />
      </td>
    </tr>
    <tr>
      <td>{{ setBPeriod }}</td>
      <td *ngFor="let cell of counter(3); let i = index">
        <img
          *ngIf="setCheckmark(i, setBOrganizationSurveyResults[0].t_004_3)"
          class="image"
          src="/assets/svg/complete_dark.svg"
          alt="logo"
        />
      </td>
    </tr>
  </table>

  <table class="table-margin-bottom">
    <tr>
      <th class="table-width-half">
        {{ 'reports.survey.ABB_001.personnel_development' | translate }}
      </th>
      <th>{{ 'reports.survey.ABB_001.location_administration' | translate }}</th>
      <th>{{ 'reports.survey.ABB_001.organization' | translate }}</th>
      <th>{{ 'reports.survey.ABB_001.unknown' | translate }}</th>
    </tr>
    <tr>
      <td>{{ setAPeriod }}</td>
      <td *ngFor="let cell of counter(3); let i = index">
        <img
          *ngIf="setCheckmark(i, setAOrganizationSurveyResults[0].t_004_4)"
          class="image"
          src="/assets/svg/complete_dark.svg"
          alt="logo"
        />
      </td>
    </tr>
    <tr>
      <td>{{ setBPeriod }}</td>
      <td *ngFor="let cell of counter(3); let i = index">
        <img
          *ngIf="setCheckmark(i, setBOrganizationSurveyResults[0].t_004_4)"
          class="image"
          src="/assets/svg/complete_dark.svg"
          alt="logo"
        />
      </td>
    </tr>
  </table>

  <table>
    <tr>
      <th class="table-width-half">
        {{ 'reports.survey.ABB_001.health_prevention_comparison' | translate }}
      </th>
      <th>{{ 'reports.survey.ABB_001.organization' | translate }}</th>
    </tr>
    <tr>
      <td>{{ setAPeriod }}</td>
      <td>{{ setAOrganizationSurveyResults[0].t_001_0 }}</td>
    </tr>
    <tr>
      <td>{{ setBPeriod }}</td>
      <td>{{ setBOrganizationSurveyResults[0].t_001_0 }}</td>
    </tr>
  </table>
</div>
