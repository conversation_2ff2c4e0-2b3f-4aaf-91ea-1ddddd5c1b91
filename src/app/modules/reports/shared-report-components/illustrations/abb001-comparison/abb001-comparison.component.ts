import { Component, Input } from '@angular/core';
import { OrganizationSurveyResult } from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-abb001-comparison',
  templateUrl: './abb001-comparison.component.html',
  styleUrls: ['./abb001-comparison.component.scss'],
})
export class Abb001ComparisonComponent {
  @Input() setAOrganizationSurveyResults: Array<OrganizationSurveyResult>;
  @Input() setBOrganizationSurveyResults: Array<OrganizationSurveyResult>;
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor(public reportService: ReportService) {}

  setCheckmark(cellId: number, answer: string): boolean {
    let answerId = answer.split('.')[4];
    // answer 2 and 3 are summarized in table cell 2
    answerId = answerId === '3' ? '2' : answerId;
    return answerId === cellId.toString();
  }

  counter(i: number): Array<number> {
    return Array(i);
  }
}
