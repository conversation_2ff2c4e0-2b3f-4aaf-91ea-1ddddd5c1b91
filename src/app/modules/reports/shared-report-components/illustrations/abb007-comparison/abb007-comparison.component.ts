import { Component, Input, OnInit } from '@angular/core';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';
import { CHART_COLORS } from '../../../../../utils/chart-colors';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';

@Component({
  selector: 'app-abb007-comparison',
  templateUrl: './abb007-comparison.component.html',
  styleUrls: ['./abb007-comparison.component.scss'],
})
export class Abb007ComparisonComponent implements OnInit {
  @Input() setA: StaffSurveyResult[];
  @Input() setB: StaffSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public chartConfiguration: SingleBarChartWithDescriptionConfiguration[];
  public legendMultiBarChart: LegendEntry[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }

  public getFooter(): string {
    return this.reportService.getComparisonReportIllustrationFooter(
      this.setA.length,
      this.setB.length,
      this.setAPeriod,
      this.setBPeriod,
      SurveyType.STAFF_SURVEY,
    );
  }

  private initBarChartConfiguration(): void {
    const setAData = this.setA.map((it) => it.pfp_004_1);
    const setANever = setAData.filter((it) => it.includes('surveys.pFP.004.1.0'));
    const setAYearly = setAData.filter((it) => it.includes('surveys.pFP.004.1.1'));
    const setAHalfYearly = setAData.filter((it) => it.includes('surveys.pFP.004.1.2'));
    const setAMonthly = setAData.filter((it) => it.includes('surveys.pFP.004.1.3'));
    const setAWeekly = setAData.filter((it) => it.includes('surveys.pFP.004.1.4'));

    const setBData = this.setB.map((it) => it.pfp_004_1);
    const setBNever = setBData.filter((it) => it.includes('surveys.pFP.004.1.0'));
    const setBYearly = setBData.filter((it) => it.includes('surveys.pFP.004.1.1'));
    const setBHalfYearly = setBData.filter((it) => it.includes('surveys.pFP.004.1.2'));
    const setBMonthly = setBData.filter((it) => it.includes('surveys.pFP.004.1.3'));
    const setBWeekly = setBData.filter((it) => it.includes('surveys.pFP.004.1.4'));

    this.chartConfiguration = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(setANever.length, setAData.length),
              color: CHART_COLORS.dark_green,
            },
            {
              value: this.reportService.calcPercentage(setAYearly.length, setAData.length),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(setAHalfYearly.length, setAData.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(setAMonthly.length, setAData.length),
              color: CHART_COLORS.purple,
            },
            {
              value: this.reportService.calcPercentage(setAWeekly.length, setAData.length),
              color: CHART_COLORS.dark_purple,
            },
          ],
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(setBNever.length, setBData.length),
              color: CHART_COLORS.dark_green,
            },
            {
              value: this.reportService.calcPercentage(setBYearly.length, setBData.length),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(setBHalfYearly.length, setBData.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(setBMonthly.length, setBData.length),
              color: CHART_COLORS.purple,
            },
            {
              value: this.reportService.calcPercentage(setBWeekly.length, setBData.length),
              color: CHART_COLORS.dark_purple,
            },
          ],
          labelSuffix: '%',
        },
      },
    ];
  }
  private initLegend(): void {
    this.legendMultiBarChart = [
      {
        description: this.translate.instant('surveys.pFP.004.1.0'),
        color: CHART_COLORS.dark_green,
      },
      { description: this.translate.instant('surveys.pFP.004.1.1'), color: CHART_COLORS.green },
      {
        description: this.translate.instant('surveys.pFP.004.1.2'),
        color: CHART_COLORS.light_green,
      },
      { description: this.translate.instant('surveys.pFP.004.1.3'), color: CHART_COLORS.purple },
      {
        description: this.translate.instant('surveys.pFP.004.1.4'),
        color: CHART_COLORS.dark_purple,
      },
    ];
  }
}
