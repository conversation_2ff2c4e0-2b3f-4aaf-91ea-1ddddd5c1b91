import { Component, Input } from '@angular/core';
import { ReportService } from '../../../report.service';
import { OrganizationSurveyResult } from '../../../../../services/admin/admin.service';

@Component({
  selector: 'app-abb002-comparison',
  templateUrl: './abb002-comparison.component.html',
  styleUrls: ['./abb002-comparison.component.scss'],
})
export class Abb002ComparisonComponent {
  @Input() setAOrganizationSurveyResults: Array<OrganizationSurveyResult>;
  @Input() setBOrganizationSurveyResults: Array<OrganizationSurveyResult>;
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor(public reportService: ReportService) {}
}
