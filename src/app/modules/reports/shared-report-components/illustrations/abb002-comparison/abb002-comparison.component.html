<h3>{{ reportService.getTableTitleTranslation(2) }}</h3>
<p>
  <i>{{ 'reports.survey.ABB_002.description' | translate }}</i>
</p>

<table class="table-margin-bottom">
  <tr>
    <th class="table-width-half">
      {{ 'reports.survey.ABB_002.organization_to_location_administration' | translate }}
    </th>
    <th>{{ 'reports.survey.ABB_002.frequency' | translate }}</th>
  </tr>
  <tr>
    <td>{{ setAPeriod }}</td>
    <td>
      {{ setAOrganizationSurveyResults[0].t_005_0 | translate }}
    </td>
  </tr>
  <tr>
    <td>{{ setBPeriod }}</td>
    <td>
      {{ setBOrganizationSurveyResults[0].t_005_0 | translate }}
    </td>
  </tr>
</table>

<table class="table-margin-bottom">
  <tr>
    <th class="table-width-half">
      {{ 'reports.survey.ABB_002.organization_to_expert_advice' | translate }}
    </th>
    <th>{{ 'reports.survey.ABB_002.frequency' | translate }}</th>
  </tr>
  <tr>
    <td>{{ setAPeriod }}</td>
    <td>
      {{ setAOrganizationSurveyResults[0].t_006_0 | translate }}
    </td>
  </tr>
  <tr>
    <td>{{ setBPeriod }}</td>
    <td>
      {{ setBOrganizationSurveyResults[0].t_006_0 | translate }}
    </td>
  </tr>
</table>

<table>
  <tr>
    <th class="table-width-half">
      {{ 'reports.survey.ABB_002.organization_to_all_location_administrations' | translate }}
    </th>
    <th>{{ 'reports.survey.ABB_002.frequency' | translate }}</th>
  </tr>
  <tr>
    <td>{{ setAPeriod }}</td>
    <td>
      {{ setAOrganizationSurveyResults[0].t_007_0 | translate }}
    </td>
  </tr>
  <tr>
    <td>{{ setBPeriod }}</td>
    <td>
      {{ setBOrganizationSurveyResults[0].t_007_0 | translate }}
    </td>
  </tr>
</table>
