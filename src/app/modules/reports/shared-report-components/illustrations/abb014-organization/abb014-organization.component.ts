import { ReportService } from './../../../report.service';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
// tslint:disable-next-line:max-line-length
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { TranslateService } from '@ngx-translate/core';
import { Component, Input, OnInit } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb014-organization',
  templateUrl: './abb014-organization.component.html',
})
export class Abb014OrganizationComponent implements OnInit {
  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];

  @Input() data: AdministrationSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.ADMINISTRATION_SURVEY,
      this.data.length,
    );
  }

  public initBarChartConfiguration(): void {
    const l002 = this.data.map((it) => {
      return it.l_002_0;
    });

    const l003 = this.data.map((it) => {
      return it.l_003_0;
    });

    const l004 = this.data.map((it) => {
      return it.l_004_0;
    });

    const l005 = this.data.map((it) => {
      return it.l_005_0;
    });

    const l006 = this.data.map((it) => {
      return it.l_006_0;
    });

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_014.organization.1'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(l002, true),
          labelSuffix: '',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_014.organization.2'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(l003, true),
          labelSuffix: '',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_014.organization.3'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(l004, true),
          labelSuffix: '',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_014.organization.4'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(l005, true),
          labelSuffix: '',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_014.organization.5'),
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(l006, true),
          labelSuffix: '',
        },
      },
    ];
  }
}
