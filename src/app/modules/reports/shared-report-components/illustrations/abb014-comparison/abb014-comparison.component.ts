import { Component, Input, OnInit } from '@angular/core';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-abb014-comparison',
  templateUrl: './abb014-comparison.component.html',
  styleUrls: ['./abb014-comparison.component.scss'],
})
export class Abb014ComparisonComponent implements OnInit {
  @Input() setA: AdministrationSurveyResult[];
  @Input() setB: AdministrationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public setAAnswers: Array<string>;
  public setBAnswers: Array<string>;

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.setAAnswers = [
      this.setA[0].l_002_0,
      this.setA[0].l_003_0,
      this.setA[0].l_004_0,
      this.setA[0].l_005_0,
      this.setA[0].l_006_0,
    ];

    this.setBAnswers = [
      this.setB[0].l_002_0,
      this.setB[0].l_003_0,
      this.setB[0].l_004_0,
      this.setB[0].l_005_0,
      this.setB[0].l_006_0,
    ];
  }

  setCheckmark(answer: string): boolean {
    return answer.includes('yes');
  }

  getRowTitleTranslation(index: number): string {
    const key = `reports.survey.ABB_014.organization.${index + 1}`;
    return this.translate.instant(key);
  }
}
