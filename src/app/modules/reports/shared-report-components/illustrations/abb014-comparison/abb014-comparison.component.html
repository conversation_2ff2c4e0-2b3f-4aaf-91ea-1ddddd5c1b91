<h3>{{ reportService.getTableTitleTranslation(4) }}</h3>
<p>
  <i>{{ 'reports.survey.ABB_014.description' | translate }}</i>
</p>

<table>
  <tr>
    <th></th>
    <th class="bordered-cell table-cell-padding">{{ setAPeriod }}</th>
    <th class="bordered-cell table-cell-padding">{{ setBPeriod }}</th>
  </tr>
  <tr *ngFor="let answer of setAAnswers; let index; let i = index">
    <td>{{ getRowTitleTranslation(i) }}</td>
    <td class="bordered-cell">
      <img
        *ngIf="setCheckmark(answer)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
    <td class="bordered-cell">
      <img
        *ngIf="setCheckmark(setBAnswers[i])"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
  </tr>
</table>
