import { Component, Input } from '@angular/core';
import { ReportService } from '../../../report.service';
import { AdministrationSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-abb014-location',
  templateUrl: './abb014-location.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Abb014LocationComponent {
  @Input() administrationSurveyResults: AdministrationSurveyResult[];

  constructor(
    public reportService: ReportService,
    public translate: TranslateService,
  ) {}

  setCheckmark(answer: string): boolean {
    return answer.includes('yes');
  }
}
