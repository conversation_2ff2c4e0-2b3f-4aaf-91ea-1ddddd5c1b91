<h3>{{ reportService.getTableTitleTranslation(3) }}</h3>
<i>{{ 'reports.survey.ABB_013.organization.description' | translate }}</i>

<table class="table-3 table-margin-bottom">
  <tr>
    <th class="table-width-third table-cell-padding">
      {{ 'reports.survey.ABB_013.organization_header.title' | translate }}
      <p class="font-normal">
        {{ 'reports.survey.ABB_013.organization_header.sub_title' | translate }}
      </p>
    </th>
    <th class="table-width-third">
      {{ 'reports.survey.ABB_013.childs_best_interests' | translate }}
    </th>
    <th class="table-width-third">{{ 'reports.survey.ABB_013.child_rights' | translate }}</th>
  </tr>
  <tr>
    <td class="font-normal">{{ setAPeriod }}</td>
    <td>
      <img
        *ngIf="setCheckmark(setAOrganizationSurveyResults[0].t_002_1)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
    <td>
      <img
        *ngIf="setCheckmark(setAOrganizationSurveyResults[0].t_002_2)"
        src="/assets/svg/complete_dark.svg"
        class="checkbox-image"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td class="font-normal">{{ setBPeriod }}</td>
    <td>
      <img
        *ngIf="setCheckmark(setBOrganizationSurveyResults[0].t_002_1)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
    <td>
      <img
        *ngIf="setCheckmark(setBOrganizationSurveyResults[0].t_002_2)"
        src="/assets/svg/complete_dark.svg"
        class="checkbox-image"
        alt="logo"
      />
    </td>
  </tr>
</table>

<table class="table-3">
  <tr>
    <th class="table-width-third table-cell-padding">
      {{ 'reports.survey.ABB_001.location_administration' | translate }}
      <p class="font-normal">
        {{ 'reports.survey.ABB_013.administration_header.sub_title' | translate }}
      </p>
    </th>
    <th class="table-width-third">
      {{ 'reports.survey.ABB_013.childs_best_interests' | translate }}
    </th>
    <th class="table-width-third">{{ 'reports.survey.ABB_013.child_rights' | translate }}</th>
  </tr>
  <tr>
    <td class="font-normal">{{ setAPeriod }}</td>
    <td>
      <img
        *ngIf="setCheckmark(setAAdministrationSurveyResults[0].l_001_1)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
    <td>
      <img
        *ngIf="setCheckmark(setAAdministrationSurveyResults[0].l_001_2)"
        src="/assets/svg/complete_dark.svg"
        class="checkbox-image"
        alt="logo"
      />
    </td>
  </tr>
  <tr>
    <td class="font-normal">{{ setBPeriod }}</td>
    <td>
      <img
        *ngIf="setCheckmark(setBAdministrationSurveyResults[0].l_001_1)"
        class="checkbox-image"
        src="/assets/svg/complete_dark.svg"
        alt="logo"
      />
    </td>
    <td>
      <img
        *ngIf="setCheckmark(setBAdministrationSurveyResults[0].l_001_2)"
        src="/assets/svg/complete_dark.svg"
        class="checkbox-image"
        alt="logo"
      />
    </td>
  </tr>
</table>
