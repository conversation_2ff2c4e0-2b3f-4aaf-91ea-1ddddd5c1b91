import { Component, Input } from '@angular/core';
import {
  AdministrationSurveyResult,
  OrganizationSurveyResult,
} from '../../../../../services/admin/admin.service';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'app-abb013-comparison',
  templateUrl: './abb013-comparison.component.html',
  styleUrls: ['./abb013-comparison.component.scss'],
})
export class Abb013ComparisonComponent {
  @Input() setAAdministrationSurveyResults: AdministrationSurveyResult[];
  @Input() setBAdministrationSurveyResults: AdministrationSurveyResult[];
  @Input() setAOrganizationSurveyResults: OrganizationSurveyResult[];
  @Input() setBOrganizationSurveyResults: OrganizationSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  constructor(public reportService: ReportService) {}

  setCheckmark(answer: string): boolean {
    return answer.includes('yes');
  }
}
