import { Component, Input, OnInit } from '@angular/core';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { TranslateService } from '@ngx-translate/core';
import { ReportService } from '../../../report.service';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb004-comparison',
  templateUrl: './abb004-comparison.component.html',
  styleUrls: ['./abb004-comparison.component.scss'],
})
export class Abb004ComparisonComponent implements OnInit {
  @Input() setA: StaffSurveyResult[];
  @Input() setB: StaffSurveyResult[];
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public segmentConfiguration0: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration1: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration2: SingleBarChartWithDescriptionConfiguration[];
  public segmentConfiguration3: SingleBarChartWithDescriptionConfiguration[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
  }

  private initBarChartConfiguration(): void {
    this.segmentConfiguration0 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_001_1)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_001_1)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration1 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_001_2)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_001_2)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration2 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_001_3)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_001_3)),
          labelSuffix: '%',
        },
      },
    ];

    this.segmentConfiguration3 = [
      {
        description: this.setAPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setA.map((it) => it.pfp_001_4)),
          labelSuffix: '%',
        },
      },
      {
        description: this.setBPeriod,
        barChartConfiguration: {
          data: this.reportService.getYesNoSegment(this.setB.map((it) => it.pfp_001_4)),
          labelSuffix: '%',
        },
      },
    ];
  }

  public getFooter(): string {
    return this.reportService.getComparisonReportIllustrationFooter(
      this.setA.length,
      this.setB.length,
      this.setAPeriod,
      this.setBPeriod,
      SurveyType.STAFF_SURVEY,
      this.translate.instant('reports.survey.description.staffAgreesCollaboration_comparison'),
    );
  }
}
