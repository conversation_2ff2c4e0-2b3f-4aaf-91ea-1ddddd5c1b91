import { CHART_COLORS } from '../../../../../utils/chart-colors';
import { ReportService } from '../../../report.service';
import { SingleBarChartWithDescriptionConfiguration } from '../../../../shared/components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { ParentalSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, Input } from '@angular/core';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb009',
  templateUrl: './abb009.component.html',
})
export class Abb009Component implements OnInit {
  @Input() data: ParentalSurveyResult[];

  public barChartConfiguration: SingleBarChartWithDescriptionConfiguration[];
  public legendMultiBarChart: LegendEntry[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(
      SurveyType.PARENTAL_SURVEY,
      this.data.length,
      this.translate.instant('reports.survey.description.familiesAgree'),
    );
  }

  private initLegend(): void {
    const fam_005_1 = this.data.map((it) => it.fam_005_1);
    const fam_005_2 = this.data.map((it) => it.fam_005_2);

    const fam_005_1_completelyAgree = fam_005_1.filter(
      (it) => it.includes('surveys.Fam.005.1.3') || it.includes('surveys.Fam.005.1.4'),
    );
    const fam_005_1_agree = fam_005_1.filter((it) => it.includes('surveys.Fam.005.1.2'));
    const fam_005_1_disagree = fam_005_1.filter((it) => it.includes('surveys.Fam.005.1.1'));
    const fam_005_1_completelyDisagree = fam_005_1.filter((it) =>
      it.includes('surveys.Fam.005.1.0'),
    );

    const fam_005_2_completelyAgree = fam_005_2.filter(
      (it) => it.includes('surveys.Fam.005.2.3') || it.includes('surveys.Fam.005.2.4'),
    );
    const fam_005_2_agree = fam_005_2.filter((it) => it.includes('surveys.Fam.005.2.2'));
    const fam_005_2_disagree = fam_005_2.filter((it) => it.includes('surveys.Fam.005.2.1'));
    const fam_005_2_completelyDisagree = fam_005_2.filter((it) =>
      it.includes('surveys.Fam.005.2.0'),
    );

    this.barChartConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_009.1'),
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(
                fam_005_1_completelyAgree.length,
                fam_005_1.length,
              ),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(fam_005_1_agree.length, fam_005_1.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(fam_005_1_disagree.length, fam_005_1.length),
              color: CHART_COLORS.purple,
            },
            {
              value: this.reportService.calcPercentage(
                fam_005_1_completelyDisagree.length,
                fam_005_1.length,
              ),
              color: CHART_COLORS.purple,
            },
          ],
          labelSuffix: '%',
        },
      },
      {
        description: this.translate.instant('reports.survey.ABB_009.2'),
        barChartConfiguration: {
          data: [
            {
              value: this.reportService.calcPercentage(
                fam_005_2_completelyAgree.length,
                fam_005_2.length,
              ),
              color: CHART_COLORS.green,
            },
            {
              value: this.reportService.calcPercentage(fam_005_2_agree.length, fam_005_2.length),
              color: CHART_COLORS.light_green,
            },
            {
              value: this.reportService.calcPercentage(fam_005_2_disagree.length, fam_005_2.length),
              color: CHART_COLORS.purple,
            },
            {
              value: this.reportService.calcPercentage(
                fam_005_2_completelyDisagree.length,
                fam_005_2.length,
              ),
              color: CHART_COLORS.dark_purple,
            },
          ],
          labelSuffix: '%',
        },
      },
    ];
  }
  private initBarChartConfiguration(): void {
    this.legendMultiBarChart = [
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.completely_agree'),
        color: CHART_COLORS.green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.agree'),
        color: CHART_COLORS.light_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.disagree'),
        color: CHART_COLORS.purple,
      },
      {
        description: this.translate.instant('reports.survey.ABB_009.legend.completely_disagree'),
        color: CHART_COLORS.dark_purple,
      },
    ];
  }
}
