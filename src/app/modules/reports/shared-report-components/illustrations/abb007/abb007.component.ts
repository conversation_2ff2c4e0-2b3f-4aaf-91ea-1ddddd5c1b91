import { ReportService } from '../../../report.service';
import { SingleBarChartConfiguration } from '../../../../shared/components/charts/single-bar-chart/single-bar-chart.component';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import { StaffSurveyResult } from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, Input, OnInit } from '@angular/core';
import { CHART_COLORS } from 'src/app/utils/chart-colors';
import { SurveyType } from '../../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-abb007',
  templateUrl: './abb007.component.html',
})
export class Abb007Component implements OnInit {
  @Input() data: StaffSurveyResult[];

  public barChartConfiguration: SingleBarChartConfiguration;
  public legendMultiBarChart: LegendEntry[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initBarChartConfiguration();
    this.initLegend();
  }

  public getFooter(): string {
    return this.reportService.getIllustrationFooter(SurveyType.STAFF_SURVEY, this.data.length);
  }

  private initBarChartConfiguration(): void {
    const pfp004_1 = this.data.map((it) => it.pfp_004_1);

    const pfp004_1_never = pfp004_1.filter((it) => it.includes('surveys.pFP.004.1.0'));
    const pfp004_1_yearly = pfp004_1.filter((it) => it.includes('surveys.pFP.004.1.1'));
    const pfp004_1_halfYearly = pfp004_1.filter((it) => it.includes('surveys.pFP.004.1.2'));
    const pfp004_1_monthly = pfp004_1.filter((it) => it.includes('surveys.pFP.004.1.3'));
    const pfp004_1_weekly = pfp004_1.filter((it) => it.includes('surveys.pFP.004.1.4'));

    this.barChartConfiguration = {
      data: [
        {
          value: this.reportService.calcPercentage(pfp004_1_never.length, pfp004_1.length),
          color: CHART_COLORS.dark_green,
        },
        {
          value: this.reportService.calcPercentage(pfp004_1_yearly.length, pfp004_1.length),
          color: CHART_COLORS.green,
        },
        {
          value: this.reportService.calcPercentage(pfp004_1_halfYearly.length, pfp004_1.length),
          color: CHART_COLORS.light_green,
        },
        {
          value: this.reportService.calcPercentage(pfp004_1_monthly.length, pfp004_1.length),
          color: CHART_COLORS.purple,
        },
        {
          value: this.reportService.calcPercentage(pfp004_1_weekly.length, pfp004_1.length),
          color: CHART_COLORS.dark_purple,
        },
      ],
      labelSuffix: '%',
    };
  }
  private initLegend(): void {
    this.legendMultiBarChart = [
      {
        description: this.translate.instant('surveys.pFP.004.1.0'),
        color: CHART_COLORS.dark_green,
      },
      { description: this.translate.instant('surveys.pFP.004.1.1'), color: CHART_COLORS.green },
      {
        description: this.translate.instant('surveys.pFP.004.1.2'),
        color: CHART_COLORS.light_green,
      },
      { description: this.translate.instant('surveys.pFP.004.1.3'), color: CHART_COLORS.purple },
      {
        description: this.translate.instant('surveys.pFP.004.1.4'),
        color: CHART_COLORS.dark_purple,
      },
    ];
  }
}
