<div *ngFor="let chartConfig of chartConfigurations; let i = index" class="page-break">
  <h3>{{ this.reportService.getIllustrationTitleTranslation(13 + i) }}</h3>
  <i>{{ chartConfig.segmentTitleKey | translate }}</i>
  <div class="spacing-medium"></div>
  <div class="doughnut-chart-row">
    <app-donut-chart
      *ngFor="let item of chartConfig.firstRowConfigurations"
      class="doughnut-chart"
      [height]="260"
      [configuration]="item"
    ></app-donut-chart>
  </div>
  <div class="doughnut-chart-row">
    <app-donut-chart
      *ngFor="let item of chartConfig.secondRowConfigurations"
      class="doughnut-chart"
      [height]="260"
      [configuration]="item"
    ></app-donut-chart>
  </div>

  <app-chart-legend
    class="legend-container"
    [configuration]="this.legendConfiguration"
  ></app-chart-legend>
  <div class="spacing-large"></div>

  <i>{{ this.getFooter() }}</i>
</div>
