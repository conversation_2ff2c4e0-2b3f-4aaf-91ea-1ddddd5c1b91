import { CHART_COLORS } from '../../../../../utils/chart-colors';
import { ReportService } from '../../../report.service';
import { DoughnutChartConfiguration } from '../../../../shared/components/charts/donut-chart/donut-chart.component';
import { LegendEntry } from '../../../../shared/components/charts/chart-legend/chart-legend.component';
import {
  AdministrationSurveyResult,
  ParentalSurveyResult,
} from '../../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-abb015',
  templateUrl: './abb015-organization.component.html',
  styleUrls: ['../../../../../../assets/report/organization-report.pdf.scss'],
})
export class Abb015OrganizationComponent implements OnInit {
  public legendConfiguration: LegendEntry[];

  public chartConfigurations: DoughnutRowsConfiguration[] = [];

  @Input() parentalData: ParentalSurveyResult[];
  @Input() administrationData: AdministrationSurveyResult[];

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initLegend();
    this.initDoughnutConfiguration();
  }

  public getFooter(): string {
    const amountFamilies = this.translate.instant('reports.survey.family.amountAttendees', {
      amountAttendees: this.parentalData.length,
    });
    const amountStaff = this.translate.instant('reports.survey.administration.amountAttendees', {
      amountAttendees: this.administrationData.length,
    });
    return `${amountFamilies} ${amountStaff}`;
  }

  private initLegend(): void {
    this.legendConfiguration = [
      {
        description: this.translate.instant('reports.survey.ABB_015.more_often'),
        color: CHART_COLORS.dark_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.exactly_right'),
        color: CHART_COLORS.green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.less_often'),
        color: CHART_COLORS.light_green,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.no_wish'),
        color: CHART_COLORS.purple,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.not_existing'),
        color: CHART_COLORS.dark_purple,
      },
      {
        description: this.translate.instant('reports.survey.ABB_015.no_answer'),
        color: CHART_COLORS.grey,
      },
    ];
  }

  private initDoughnutConfiguration(): void {
    // there can only be 1 administration-survey-result per location
    // Map<locationId, administrationAnswer>
    const l0081 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_1)]),
    );
    const l0082 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_2)]),
    );
    const l0083 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_3)]),
    );
    const l0084 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_4)]),
    );
    const l0085 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_5)]),
    );
    const l0086 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_6)]),
    );
    const l0087 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_7)]),
    );
    const l0088 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_8)]),
    );
    const l0089 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_9)]),
    );
    const l00810 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_10)]),
    );
    const l00811 = new Map<number, string>(
      this.administrationData.map((it) => [it.locationId, this.getAnswerSegment(it.l_008_11)]),
    );

    // there can be many parental-survey-results per location
    // Map<locationId, parentalAnswer[]>
    const fam0041 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0041.get(it.locationId) === undefined ? [] : fam0041.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_1));
      fam0041.set(it.locationId, value);
    });
    const fam0042 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0042.get(it.locationId) === undefined ? [] : fam0042.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_2));
      fam0042.set(it.locationId, value);
    });
    const fam0043 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0043.get(it.locationId) === undefined ? [] : fam0043.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_3));
      fam0043.set(it.locationId, value);
    });
    const fam0044 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0044.get(it.locationId) === undefined ? [] : fam0044.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_4));
      fam0044.set(it.locationId, value);
    });
    const fam0045 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0045.get(it.locationId) === undefined ? [] : fam0045.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_5));
      fam0045.set(it.locationId, value);
    });
    const fam0046 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0046.get(it.locationId) === undefined ? [] : fam0046.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_6));
      fam0046.set(it.locationId, value);
    });
    const fam0047 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0047.get(it.locationId) === undefined ? [] : fam0047.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_7));
      fam0047.set(it.locationId, value);
    });
    const fam0048 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0048.get(it.locationId) === undefined ? [] : fam0048.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_8));
      fam0048.set(it.locationId, value);
    });
    const fam0049 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam0049.get(it.locationId) === undefined ? [] : fam0049.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_9));
      fam0049.set(it.locationId, value);
    });
    const fam00410 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam00410.get(it.locationId) === undefined ? [] : fam00410.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_10));
      fam00410.set(it.locationId, value);
    });
    const fam00411 = new Map<number, Array<string>>();
    this.parentalData.forEach((it) => {
      const value: Array<string> =
        fam00411.get(it.locationId) === undefined ? [] : fam00411.get(it.locationId);
      value.push(this.getAnswerSegment(it.fam_004_11));
      fam00411.set(it.locationId, value);
    });

    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0081, fam0041, 'surveys.Fam.004.1.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0082, fam0042, 'surveys.Fam.004.2.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0083, fam0043, 'surveys.Fam.004.3.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0084, fam0044, 'surveys.Fam.004.4.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0085, fam0045, 'surveys.Fam.004.5.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0086, fam0046, 'surveys.Fam.004.6.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0087, fam0047, 'surveys.Fam.004.7.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0088, fam0048, 'surveys.Fam.004.8.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l0089, fam0049, 'surveys.Fam.004.9.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l00810, fam00410, 'surveys.Fam.004.10.title'),
    );
    this.chartConfigurations.push(
      this.getChartSegmentConfiguration(l00811, fam00411, 'surveys.Fam.004.11.title'),
    );
  }

  private getChartSegmentConfiguration(
    administrationAnswers: Map<number, string>,
    parentalAnswers: Map<number, Array<string>>,
    segmentTitleKey: string,
  ): DoughnutRowsConfiguration {
    const configurations = [
      this.getParentalAnswersDoughnutChartConfiguration(
        parentalAnswers,
        administrationAnswers,
        '0',
      ),
      this.getParentalAnswersDoughnutChartConfiguration(
        parentalAnswers,
        administrationAnswers,
        '1',
      ),
      this.getParentalAnswersDoughnutChartConfiguration(
        parentalAnswers,
        administrationAnswers,
        '2',
      ),
      this.getParentalAnswersDoughnutChartConfiguration(
        parentalAnswers,
        administrationAnswers,
        '3',
      ),
      this.getParentalAnswersDoughnutChartConfiguration(
        parentalAnswers,
        administrationAnswers,
        '4',
      ),
      this.getParentalAnswersDoughnutChartConfiguration(
        parentalAnswers,
        administrationAnswers,
        '5',
      ),
    ];

    return {
      segmentTitleKey,
      firstRowConfigurations: configurations.slice(0, 3),
      secondRowConfigurations: configurations.slice(3, 6),
    };
  }

  private getAnswerSegment(answer: string): string {
    return answer.split('.')[4];
  }

  private getLocationIdsFromAnswer(
    administrationAnswersWithLocation: Map<number, string>,
    answer: string,
  ): Array<number> {
    const keys: Array<number> = [];
    administrationAnswersWithLocation.forEach((value, key) => {
      if (value === answer) {
        keys.push(key);
      }
    });
    return keys;
  }

  private getParentalAnswersDoughnutChartConfiguration(
    parentalAnswersWithLocations: Map<number, Array<string>>,
    administrationAnswers: Map<number, string>,
    answerKey: string,
  ): DoughnutChartConfiguration {
    const locationIds = this.getLocationIdsFromAnswer(administrationAnswers, answerKey);
    let answers: Array<string> = [];
    locationIds.forEach((it) => {
      const value: Array<string> =
        parentalAnswersWithLocations.get(it) === undefined
          ? []
          : parentalAnswersWithLocations.get(it);
      answers = answers.concat(value);
    });

    return {
      data:
        locationIds.length === 0
          ? []
          : [
              {
                color: CHART_COLORS.dark_green,
                value: this.reportService.getDistributionOfAnswer(answers, '0'),
              },
              {
                color: CHART_COLORS.green,
                value: this.reportService.getDistributionOfAnswer(answers, '1'),
              },
              {
                color: CHART_COLORS.light_green,
                value: this.reportService.getDistributionOfAnswer(answers, '2'),
              },
              {
                color: CHART_COLORS.purple,
                value: this.reportService.getDistributionOfAnswer(answers, '3'),
              },
              {
                color: CHART_COLORS.grey,
                value: this.reportService.getDistributionOfAnswer(answers, '4'),
              },
            ],
      labelSuffix: '%',
      description: {
        duration: this.translate.instant(`surveys.L.008.1.${answerKey}`),
        locations: locationIds.length,
        answers: answers.length,
      },
    };
  }
}

interface DoughnutRowsConfiguration {
  segmentTitleKey: string;
  firstRowConfigurations: DoughnutChartConfiguration[];
  secondRowConfigurations: DoughnutChartConfiguration[];
}
