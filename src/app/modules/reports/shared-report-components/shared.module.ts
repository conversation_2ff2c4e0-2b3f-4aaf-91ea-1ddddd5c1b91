import { Abb015TableComparisonComponent } from './illustrations/abb015-table-comparison/abb015-table-comparison.component';
import { Abb015BarChartComparisonComponent } from './illustrations/abb015-bar-chart-comparison/abb015-bar-chart-comparison.component';
import { Abb015BarChartLocationComponent } from './illustrations/abb015-bar-chart-location/abb015-bar-chart-location.component';
import { Abb015TableLocationComponent } from './illustrations/abb015-table-location/abb015-table-location.component';
import { Tb016TwoComponent } from './text-blocks/tb016-two/tb016-two.component';
import { Tb016OneComponent } from './text-blocks/tb016-one/tb016-one.component';
import { Abb016LocationComponent } from './illustrations/abb016-location/abb016-location.component';
import { Abb014LocationComponent } from './illustrations/abb014-location/abb014-location.component';
import { Abb013LocationComponent } from './illustrations/abb013-location/abb013-location.component';
import { Abb015OrganizationComponent } from './illustrations/abb015-organization/abb015-organization.component';
import { Abb012Component } from './illustrations/abb012/abb012.component';
import { Abb011Component } from './illustrations/abb011/abb011.component';
import { Abb009Component } from './illustrations/abb009/abb009.component';
import { Abb007Component } from './illustrations/abb007/abb007.component';
import { Abb006Component } from './illustrations/abb006/abb006.component';
import { Abb005Component } from './illustrations/abb005/abb005.component';
import { Abb014OrganizationComponent } from './illustrations/abb014-organization/abb014-organization.component';
import { SharedModule } from '../../shared/shared.module';
import { NgModule } from '@angular/core';
import { GraziasMissingTranslationHandler } from '../../../utils/missingTranslationHandler';
import { TranslateModule } from '@ngx-translate/core';
import { Tb001Component } from './text-blocks/tb001/tb001.component';
import { Tb002Component } from './text-blocks/tb002/tb002.component';
import { Tb003Component } from './text-blocks/tb003/tb003.component';
import { Tb004Component } from './text-blocks/tb004/tb004.component';
import { Tb005Component } from './text-blocks/tb005/tb005.component';
import { Tb006Component } from './text-blocks/tb006/tb006.component';
import { Tb007Component } from './text-blocks/tb007/tb007.component';
import { Tb008Component } from './text-blocks/tb008/tb008.component';
import { Tb009Component } from './text-blocks/tb009/tb009.component';
import { Tb010Component } from './text-blocks/tb010/tb010.component';
import { Tb011LocationComponent } from './text-blocks/tb011-location/tb011-location.component';
import { Tb012Component } from './text-blocks/tb012/tb012.component';
import { Tb013Component } from './text-blocks/tb013/tb013.component';
import { Tb014Component } from './text-blocks/tb014/tb014.component';
import { Tb015Component } from './text-blocks/tb015/tb015.component';
import { Tb017Component } from './text-blocks/tb017/tb017.component';
import { Tb018Component } from './text-blocks/tb018/tb018.component';
import { Tb019Component } from './text-blocks/tb019/tb019.component';
import { Tb020Component } from './text-blocks/tb020/tb020.component';
import { Tb021Component } from './text-blocks/tb021/tb021.component';
import { Tb022Component } from './text-blocks/tb022/tb022.component';
import { Tb023Component } from './text-blocks/tb023/tb023.component';
import { Tb024Component } from './text-blocks/tb024/tb024.component';
import { Tb025Component } from './text-blocks/tb025/tb025.component';
import { Tb026Component } from './text-blocks/tb026/tb026.component';
import { Tb027OrganizationComponent } from './text-blocks/tb027-organization/tb027-organization.component';
import { Tb028OrganizationComponent } from './text-blocks/tb028-organization/tb028-organization.component';
import { Tb029LocationComponent } from './text-blocks/tb029-location/tb029-location.component';
import { Abb001Component } from './illustrations/abb001/abb001.component';
import { CommonModule } from '@angular/common';
import { Abb004Component } from './illustrations/abb004/abb004.component';
import { Abb002Component } from './illustrations/abb002/abb002.component';
import { Abb003Component } from './illustrations/abb003/abb003.component';
import { Tb011OrganizationComponent } from './text-blocks/tb011-organization/tb011-organization.component';
import { Abb010Component } from './illustrations/abb010/abb010.component';
import { Abb013Component } from './illustrations/abb013/abb013.component';
import { Abb016OrganizationComponent } from './illustrations/abb016-organization/abb016-organization.component';
import { Abb017OrganizationComponent } from './illustrations/abb017-organization/abb017-organization.component';
import { Attachment001Component } from './text-blocks/attachment001/attachment001.component';
import { Attachment002Component } from './text-blocks/attachment002/attachment002.component';
import { Attachment003Component } from './text-blocks/attachment003/attachment003.component';
import { Attachment004Component } from './text-blocks/attachment004/attachment004.component';
import { Tb028LocationComponent } from './text-blocks/tb028-location/tb028-location.component';
import { Tb027LocationComponent } from './text-blocks/tb027-location/tb027-location.component';
import { Abb001ComparisonComponent } from './illustrations/abb001-comparison/abb001-comparison.component';
import { Abb002ComparisonComponent } from './illustrations/abb002-comparison/abb002-comparison.component';
import { Abb003ComparisonComponent } from './illustrations/abb003-comparison/abb003-comparison.component';
import { Tb011ComparisonComponent } from './text-blocks/tb011-comparison/tb011-comparison.component';
import { Abb004ComparisonComponent } from './illustrations/abb004-comparison/abb004-comparison.component';
import { Abb005ComparisonComponent } from './illustrations/abb005-comparison/abb005-comparison.component';
import { Abb006ComparisonComponent } from './illustrations/abb006-comparison/abb006-comparison.component';
import { Abb007ComparisonComponent } from './illustrations/abb007-comparison/abb007-comparison.component';
import { Abb009ComparisonComponent } from './illustrations/abb009-comparison/abb009-comparison.component';
import { Abb010ComparisonComponent } from './illustrations/abb010-comparison/abb010-comparison.component';
import { Abb011ComparisonComponent } from './illustrations/abb011-comparison/abb011-comparison.component';
import { Abb012ComparisonComponent } from './illustrations/abb012-comparison/abb012-comparison.component';
import { Abb013ComparisonComponent } from './illustrations/abb013-comparison/abb013-comparison.component';
import { Abb014ComparisonComponent } from './illustrations/abb014-comparison/abb014-comparison.component';
import { Abb016ComparisonComponent } from './illustrations/abb016-comparison/abb016-comparison.component';
import { Tb027ComparisonComponent } from './text-blocks/tb027-comparison/tb027-comparison.component';
import { Abb017ComparisonComponent } from './illustrations/abb017-comparison/abb017-comparison.component';
import { Attachment001ComparisonComponent } from './text-blocks/attachment001-comparison/attachment001-comparison.component';
import { Attachment002ComparisonComponent } from './text-blocks/attachment002-comparison/attachment002-comparison.component';
import { Attachment003ComparisonComponent } from './text-blocks/attachment003-comparison/attachment003-comparison.component';
import { Attachment004ComparisonComponent } from './text-blocks/attachment004-comparison/attachment004-comparison.component';
import { ComparisonReportEvaluationsComponent } from './comparison-report-evaluations/comparison-report-evaluations.component';
import { BoxplotComponent } from './boxplot/boxplot.component';
import { Abb008Component } from './illustrations/abb008/abb008.component';

@NgModule({
  declarations: [
    Abb003Component,
    Abb004Component,
    Abb005Component,
    Abb006Component,
    Abb007Component,
    Abb008Component,
    Abb009Component,
    Abb011Component,
    Abb012Component,
    Abb013LocationComponent,
    Abb014OrganizationComponent,
    Abb014LocationComponent,
    Abb015OrganizationComponent,
    Abb015TableLocationComponent,
    Abb015BarChartComparisonComponent,
    Abb015TableComparisonComponent,
    Abb015BarChartLocationComponent,
    Tb001Component,
    Tb002Component,
    Tb003Component,
    Tb004Component,
    Tb005Component,
    Tb006Component,
    Tb007Component,
    Tb008Component,
    Tb009Component,
    Tb010Component,
    Tb011LocationComponent,
    Tb012Component,
    Tb013Component,
    Tb014Component,
    Tb015Component,
    Tb016OneComponent,
    Tb016TwoComponent,
    Tb017Component,
    Tb018Component,
    Tb019Component,
    Tb020Component,
    Tb021Component,
    Tb022Component,
    Tb023Component,
    Tb024Component,
    Tb025Component,
    Tb026Component,
    Tb027OrganizationComponent,
    Tb028OrganizationComponent,
    Tb029LocationComponent,
    Abb001Component,
    Abb002Component,
    Tb011OrganizationComponent,
    Abb010Component,
    Abb013Component,
    Abb016OrganizationComponent,
    Abb016LocationComponent,
    Abb017OrganizationComponent,
    Attachment001Component,
    Attachment002Component,
    Attachment003Component,
    Attachment004Component,
    Tb028LocationComponent,
    Tb027LocationComponent,
    Abb001ComparisonComponent,
    Abb002ComparisonComponent,
    Abb003ComparisonComponent,
    Tb011ComparisonComponent,
    Abb004ComparisonComponent,
    Abb005ComparisonComponent,
    Abb006ComparisonComponent,
    Abb007ComparisonComponent,
    Abb009ComparisonComponent,
    Abb010ComparisonComponent,
    Abb011ComparisonComponent,
    Abb012ComparisonComponent,
    Abb013ComparisonComponent,
    Abb014ComparisonComponent,
    Abb016ComparisonComponent,
    Tb027ComparisonComponent,
    Abb017ComparisonComponent,
    Attachment001ComparisonComponent,
    Attachment002ComparisonComponent,
    Attachment003ComparisonComponent,
    Attachment004ComparisonComponent,
    ComparisonReportEvaluationsComponent,
    BoxplotComponent,
  ],
  imports: [
    SharedModule,
    CommonModule,
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
  ],
  exports: [
    Abb003Component,
    Abb004Component,
    Abb005Component,
    Abb006Component,
    Abb007Component,
    Abb008Component,
    Abb009Component,
    Abb011Component,
    Abb012Component,
    Abb013LocationComponent,
    Abb014OrganizationComponent,
    Abb014LocationComponent,
    Abb015OrganizationComponent,
    Tb001Component,
    Tb002Component,
    Tb003Component,
    Tb004Component,
    Tb005Component,
    Tb006Component,
    Tb007Component,
    Tb008Component,
    Tb009Component,
    Tb010Component,
    Tb011LocationComponent,
    Tb012Component,
    Tb013Component,
    Tb014Component,
    Tb015Component,
    Abb015TableLocationComponent,
    Abb015BarChartLocationComponent,
    Abb015TableComparisonComponent,
    Abb015BarChartComparisonComponent,
    Tb016OneComponent,
    Tb016TwoComponent,
    Tb017Component,
    Tb018Component,
    Tb019Component,
    Tb020Component,
    Tb021Component,
    Tb022Component,
    Tb023Component,
    Tb024Component,
    Tb025Component,
    Tb026Component,
    Tb027OrganizationComponent,
    Tb028OrganizationComponent,
    Tb029LocationComponent,
    Abb001Component,
    Abb002Component,
    Tb011OrganizationComponent,
    Abb010Component,
    Abb013Component,
    Abb016OrganizationComponent,
    Abb016LocationComponent,
    Abb017OrganizationComponent,
    Attachment001Component,
    Attachment002Component,
    Attachment003Component,
    Attachment004Component,
    Tb028LocationComponent,
    Tb027LocationComponent,
    Abb001ComparisonComponent,
    Abb002ComparisonComponent,
    Abb003ComparisonComponent,
    Tb011ComparisonComponent,
    Abb004ComparisonComponent,
    Abb005ComparisonComponent,
    Abb006ComparisonComponent,
    Abb007ComparisonComponent,
    Abb009ComparisonComponent,
    Abb010ComparisonComponent,
    Abb011ComparisonComponent,
    Abb012ComparisonComponent,
    Abb013ComparisonComponent,
    Abb014ComparisonComponent,
    Abb016ComparisonComponent,
    Abb017ComparisonComponent,
    Attachment001ComparisonComponent,
    Attachment002ComparisonComponent,
    Attachment003ComparisonComponent,
    Attachment004ComparisonComponent,
    ComparisonReportEvaluationsComponent,
    BoxplotComponent,
  ],
})
export class SharedReportModule {}
