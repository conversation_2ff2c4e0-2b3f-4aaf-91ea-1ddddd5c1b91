<ion-grid class="screen-container">
  <ion-row class="page-break">
    <ion-col size="4"></ion-col>
    <ion-col size="4">
      <div>
        <app-evaluator-analysis-title
          [scale]="scale"
          [title]="evaluations[0].institutionName"
          [date]="evaluations[0].dateOfSurveyFormatted"
        ></app-evaluator-analysis-title>
      </div>
    </ion-col>
    <ion-col size="4"></ion-col>
  </ion-row>

  <app-boxplot
    [boxplotLegendLeftCol]="boxplotLegendLeftCol"
    [boxplotLegendRightCol]="boxplotLegendRightCol"
    [boxplotConfig]="boxplotConfig"
    [boxplotLegendItems]="boxplotLegendItems"
    [evaluationType]="EvaluationType.EVALUATION"
  ></app-boxplot>

  <ion-row class="page-break">
    <ion-col size="12">
      <h2>{{ 'reports.evaluations.comparison.scoring-scheme.title' | translate }}</h2>
      <h3>{{ getScoreExplanationTitle(1) }}</h3>
      <p>
        {{ 'reports.evaluations.comparison.scoring-scheme.score-one-explanation' | translate }}
      </p>
      <h3>{{ getScoreExplanationTitle(2) }}</h3>
      <p>
        {{ 'reports.evaluations.comparison.scoring-scheme.score-two-explanation' | translate }}
      </p>
      <h3>{{ getScoreExplanationTitle(3) }}</h3>
      <p>
        {{ 'reports.evaluations.comparison.scoring-scheme.score-three-explanation' | translate }}
      </p>
      <h3>{{ getScoreExplanationTitle(4) }}</h3>
      <p>
        {{ 'reports.evaluations.comparison.scoring-scheme.score-four-explanation' | translate }}
      </p>
    </ion-col>
  </ion-row>

  <ion-row class="page-break">
    <ion-col size="12">
      <div *ngFor="let trait of traits" class="page-break">
        <app-trait-analysis-educator-comparison
          [trait]="trait"
          [evaluations]="evaluations"
          [scale]="scale"
          [educator]="educators"
          [comparisonReportParams]="barChartParams[trait.id]"
        ></app-trait-analysis-educator-comparison>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
