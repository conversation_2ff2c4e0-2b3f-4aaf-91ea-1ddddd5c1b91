import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { EvaluationViewModel } from '../../../../pages/evaluator-analysis/evaluator-analysis.page';
import { ComparisonReportDataResponse } from '../../../../services/admin/admin.service';
import { Educator } from '../../../../services/evaluation/models/educator';
import { Evaluation } from '../../../../services/evaluation/models/evaluation';
import { ScaleDefinition } from '../../../../services/evaluation/models/scale-definition';
import { Trait } from '../../../../services/evaluation/models/trait';
import { ScaleService } from '../../../../services/evaluation/scale.service';
import { EvaluationType } from '../../../organization/organizations/utils';
import { BoxplotChartConfiguration } from '../../../shared/components/charts/boxplot-chart/boxplot-chart.component';
import { ReportService } from '../../report.service';

@Component({
  selector: 'app-comparison-report-evaluations',
  templateUrl: './comparison-report-evaluations.component.html',
  styleUrls: ['./comparison-report-evaluations.component.scss'],
})
export class ComparisonReportEvaluationsComponent implements OnInit {
  @Input() evaluationData: ComparisonReportDataResponse;
  @Input() setAPeriod: string;
  @Input() setBPeriod: string;

  public traits: Array<Trait> = [];
  public educators: Array<Educator> = [];
  public evaluations: Array<EvaluationViewModel> = [];
  public scale: ScaleDefinition;
  public boxplotConfig: Array<BoxplotChartConfiguration> = [];
  public boxplotLegendLeftCol: Array<string> = [];
  public boxplotLegendRightCol: Array<string> = [];
  public boxplotLegendItems: Map<string, string> = new Map();
  public barChartParams: Map<string, ComparisonReportEvaluationBarChartParams> = new Map();
  public EvaluationType = EvaluationType;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private traitColors: any;
  private illustrationTitleOffset = 0;

  constructor(
    public translate: TranslateService,
    public scaleService: ScaleService,
    public reportService: ReportService,
  ) {}

  ngOnInit(): void {
    this.initEvaluations();
    this.initTraitColors();
    this.initBoxplotConfig();
    this.initBoxplotLegend();
    this.initBarChartParams();
  }

  public getScoreExplanationTitle(score: number): string {
    return this.translate.instant('reports.evaluations.comparison.scoring-scheme.score-title', {
      score,
    });
  }

  private initBarChartParams(): void {
    this.traits.forEach((trait, index) => {
      this.barChartParams[trait.id] = {
        illustrationTitle: this.reportService.getIllustrationTitleTranslation(
          index + 1 + this.illustrationTitleOffset,
        ),
        header: '',
        footer: '',
      };
    });
  }

  private initBoxplotLegend(): void {
    const colLength = Math.ceil(this.traits.length / 2);
    this.traits.forEach((trait, index) => {
      const traitId = trait.id.replace('t', '').toUpperCase();
      index < colLength
        ? this.boxplotLegendLeftCol.push(traitId)
        : this.boxplotLegendRightCol.push(traitId);
      this.boxplotLegendItems[traitId] = this.translate.instant(trait.localizationKey());
    });
  }

  private initTraitColors(): void {
    this.traitColors = this.scaleService.getTraitColorsForScale(this.scale);
  }

  private initEvaluations(): void {
    const evaluationA = Evaluation.fromJson(this.evaluationData.dataResponseSetA.evaluation);
    const evaluationB = Evaluation.fromJson(this.evaluationData.dataResponseSetB.evaluation);
    this.evaluations.push(new EvaluationViewModel(evaluationA, this.translate, this.setAPeriod));
    this.evaluations.push(new EvaluationViewModel(evaluationB, this.translate, this.setBPeriod));

    this.scale = evaluationA.scales.values().next().value.scaleDefinition;

    evaluationA.scales
      .values()
      .next()
      .value.traits.forEach((trait) => {
        this.traits.push(trait);
      });
  }

  private initBoxplotConfig(): void {
    this.evaluations.forEach((data) => {
      this.illustrationTitleOffset += 1;
      const config = [];
      data.evaluation.scales
        .values()
        .next()
        .value.traits.forEach((trait, traitName) => {
          const scores = [];
          trait.branches.forEach((branch) => {
            branch.scores.forEach((value) => {
              if (value !== 9) {
                // a score of 9 represents a branch, that could not be evaluated for a certain educator
                scores.push(value);
              }
            });
          });
          config.push({
            values: scores,
            color: this.traitColors[trait.id],
            label: traitName.replace('t', '').toUpperCase(),
          });
        });

      this.boxplotConfig.push({
        data: config,
        scaleY: {
          min: 1,
          max: 4,
        },
      });
    });
  }
}

export interface ComparisonReportEvaluationBarChartParams {
  illustrationTitle: string;
  header: string;
  footer: string;
}
