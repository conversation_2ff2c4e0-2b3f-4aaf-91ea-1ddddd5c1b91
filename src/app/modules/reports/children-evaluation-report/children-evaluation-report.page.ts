import { ReportType } from '../../organization/organizations/models';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ChildrenEvaluationApiService } from './children-evaluation-api.service';
import { UserService } from '../../../services/user/user.service';
import { LoadingController, NavController } from '@ionic/angular';
import { ChildrenEvaluationReportDo } from './models/children-evaluation-report';
import { PrintService, PrintStyles } from '../../../services/print.service';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import { TranslateService } from '@ngx-translate/core';
import { Utils } from '../../../utils/utils';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-children-evaluation-report-page',
  templateUrl: './children-evaluation-report.page.html',
  styleUrls: ['./children-evaluation-report.page.scss'],
})
export class ChildrenEvaluationReportPage implements OnInit {
  @ViewChild('printSection', { read: ElementRef, static: false }) printSection: ElementRef;
  public childEvalReportData: ChildrenEvaluationReportDo | null = null;
  public isLoading = true;
  public buttonType = PrimaryButtonType.GRADIENT;
  public hasError = false;
  public loadingUi: HTMLIonLoadingElement = null;
  public ReportType = ReportType;

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private childEvalApiService: ChildrenEvaluationApiService,
    private userService: UserService,
    private navController: NavController,
    private printService: PrintService,
    public translate: TranslateService,
    public loadingController: LoadingController,
  ) {}

  ngOnInit(): void {
    this.activateSessionAndFetchData();
  }

  activateSessionAndFetchData(): void {
    this.route.params.subscribe(async (params) => {
      if (params.token !== undefined) {
        this.storeAuthToken(params.token);
        this.translate.use(params.lang).subscribe(() => {
          this.http.get(`./assets/i18n/${params.lang}.json`).subscribe((translationFile) => {
            this.translate.setTranslation(params.lang, translationFile);
            this.navController.navigateRoot(
              `reports/children-evaluation/${params.evaluationRemoteId}`,
            );
          });
        });
      } else {
        this.getReportData(params.evaluationRemoteId);
      }
    });
  }

  storeAuthToken(token: string): void {
    this.userService.storeAuthToken(token);
  }

  async getReportData(childEvaluationRemoteId): Promise<void> {
    await this.presentLoadingUi();
    this.childEvalApiService
      .getChildEvaluationReportData(childEvaluationRemoteId)
      .subscribe(async (result) => {
        if (result.success) {
          this.childEvalReportData = result.data.result;
        } else {
          this.hasError = true;
        }
        this.dismissLoadingUi();
      });
  }

  customPrint(): void {
    const printContent = this.printSection.nativeElement;
    this.printService.openBrowserPrintDialog(printContent, PrintStyles.CHILD_EVALUATION_REPORT);
  }

  private async presentLoadingUi(): Promise<void> {
    this.isLoading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
  }

  private async dismissLoadingUi(): Promise<void> {
    await this.loadingUi.dismiss();
    this.isLoading = false;
  }
}
