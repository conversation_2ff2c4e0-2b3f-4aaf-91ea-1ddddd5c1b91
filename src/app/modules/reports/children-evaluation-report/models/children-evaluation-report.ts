export class ChildrenEvaluationReportResponse {
  result: ChildrenEvaluationReportDo;
}

export class ChildrenEvaluationReportDo {
  localId: string;
  customerServiceId: string;
  dateOfSurvey: string;
  organizationName: string;
  locationName: string;
  groupName: string;
  evaluatingEducatorFirstName: string;
  evaluatingEducatorLastName: string;
  evaluatingEducatorMail: string;
  language: string;
  numberOfEvaluatedChildren: number;
  calculatedScores: ChildrenEvaluationCalculatedScores;
  freeFormAnswers: ChildrenEvaluationFreeFormAnswers[];
}

export class ChildrenSingleChoiceAnswerMean {
  id: string;
  meanScore: number;
}

export class ChildrenEvaluationCalculatedScores {
  singleChoiceAnswersMean: ChildrenSingleChoiceAnswerMean[];
  singleChoiceAnswersAccumulated: ChildrenSingleChoiceAnswerAccumulated[];
}

export class ChildrenSingleChoiceAnswerAccumulated {
  id: string;
  codeBookKey: string;
  accumulatedScore: ChildrenSingleChoiceAnswerDistribution;
}

export class ChildrenSingleChoiceAnswerDistribution {
  disagreeCompletely: number;
  disagreeToModerateDegree: number;
  agreeToModerateDegree: number;
  agreeCompletely: number;
  noAnswersGiven: number;
}

export class ChildrenEvaluationFreeFormAnswers {
  questionId: string;
  codeBookKey: string;
  answers: string[];
}
