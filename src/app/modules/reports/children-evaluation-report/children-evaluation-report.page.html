<ion-content *ngIf="!isLoading && !hasError">
  <div class="print-button">
    <app-primary-button
      [buttonType]="buttonType"
      label="{{'global.button.print' | translate}}"
      (onClick)="customPrint()"
    ></app-primary-button>
  </div>
  <app-children-evaluation-report-component
    #printSection
    [childEvalReportData]="childEvalReportData"
    [reportType]="ReportType.CHILDREN_EVALUATION_REPORT"
  ></app-children-evaluation-report-component>
</ion-content>

<ion-content *ngIf="!isLoading && hasError">
  <app-not-found
    [message]="translate.instant('childrenEvaluation.report.notFound')"
  ></app-not-found>
</ion-content>
