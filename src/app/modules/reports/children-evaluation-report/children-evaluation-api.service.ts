import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { UserService } from '../../../services/user/user.service';
import { Observable, of } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { catchError, map } from 'rxjs/operators';
import { NonEmptyResponse } from '../../../services/evaluation/evaluation.service';
import { ChildrenEvaluationReportResponse } from './models/children-evaluation-report';

@Injectable({
  providedIn: 'root',
})
export class ChildrenEvaluationApiService {
  constructor(
    private http: HttpClient,
    private userService: UserService,
  ) {}

  getChildEvaluationReportData(
    evaluationId: string,
  ): Observable<NonEmptyResponse<ChildrenEvaluationReportResponse>> {
    return this.http
      .get<ChildrenEvaluationReportResponse>(
        `${environment.apiBaseUrl}/api/v1/child-evaluations/${evaluationId}`,
        {
          headers: new HttpHeaders().append('x-auth-token', this.userService.authToken),
        },
      )
      .pipe(
        map((response) => {
          return new NonEmptyResponse(true, '', response);
        }),
        catchError((errorResp) => {
          const errorMessage = errorResp.error.localizedMessage;
          return of(new NonEmptyResponse(false, errorMessage, undefined));
        }),
      );
  }
}
