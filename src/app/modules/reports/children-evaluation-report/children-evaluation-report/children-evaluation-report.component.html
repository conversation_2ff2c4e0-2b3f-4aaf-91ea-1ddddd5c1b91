<div class="screen-container page-break">
  <div class="logo-container">
    <img class="front-page-logo" src="/assets/img/logo_child_perspective.png" alt="logo" />
  </div>
  <div class="info-text">
    <div class="title">{{ 'childrenEvaluation.report.title' | translate }}</div>
    <div *ngIf="isChildrenEvaluationReport()" class="subtitle">
      {{ 'childrenEvaluation.report.subtitle' | translate }}
      {{ childEvalReportData.dateOfSurvey | isoDate: translate.currentLang : 'yearOnly' }}
    </div>
    <b>{{ childEvalReportData.organizationName }}</b>
    <div>{{ childEvalReportData.locationName }}</div>
    <div>{{ childEvalReportData.groupName }}</div>
    <div *ngIf="isChildrenEvaluationReport()">
      {{ childEvalReportData.dateOfSurvey | isoDate: translate.currentLang }}
      ・
      {{ childEvalReportData.numberOfEvaluatedChildren }}
      {{ 'childrenEvaluation.report.children_count' | translate }} ・
      {{ childEvalReportData.customerServiceId }}
    </div>
    <div *ngIf="!isChildrenEvaluationReport()">
      {{ childEvalReportData.numberOfEvaluatedChildren }}
      {{ 'childrenEvaluation.report.children_count' | translate }}
    </div>
  </div>

  <div class="page-break" *ngIf="reportType !== ReportType.COMPARISON_REPORT">
    <app-bubble-chart-child
      [data]="childEvalReportData.calculatedScores.singleChoiceAnswersMean"
    ></app-bubble-chart-child>
  </div>
  <div class="page-break" *ngIf="reportType === ReportType.COMPARISON_REPORT">
    <app-boxplot
      [boxplotConfig]="boxplotConfig"
      [boxplotLegendLeftCol]="boxplotLegendLeftCol"
      [boxplotLegendRightCol]="boxplotLegendRightCol"
      [boxplotLegendItems]="boxplotLegendItems"
      [evaluationType]="EvaluationType.CHILD_EVALUATION"
    ></app-boxplot>
  </div>
  <div class="page-break report-section" *ngFor="let answers of setAReportData; let i = index">
    <app-children-evaluation-bar-chart
      [answers]="answers"
      [answersToCompareWith]="getAnswersToCompareWith(i)"
      [index]="i + 1"
      [reportType]="reportType"
      [illustrationIndexOffset]="illustrationTitleOffset"
      [setAPeriod]="setAPeriod"
      [setBPeriod]="setBPeriod"
    ></app-children-evaluation-bar-chart>
    <app-children-evaluation-free-form-answers
      *ngIf="reportType !== ReportType.COMPARISON_REPORT"
      [freeFormAnswers]="getFreeFormAnswersFor(answers.id)"
    >
    </app-children-evaluation-free-form-answers>
  </div>
</div>
