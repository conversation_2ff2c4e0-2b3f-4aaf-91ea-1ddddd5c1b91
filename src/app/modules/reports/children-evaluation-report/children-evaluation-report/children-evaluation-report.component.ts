import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ReportType } from '../../../organization/organizations/models';
import { EvaluationType } from '../../../organization/organizations/utils';
import { BoxplotChartConfiguration } from '../../../shared/components/charts/boxplot-chart/boxplot-chart.component';
import { ChildEvaluationReportUtil } from '../../../shared/components/child-evaluation-report/child-evaluation-report-util';
import { ReportService } from '../../report.service';
import {
  ChildrenEvaluationFreeFormAnswers,
  ChildrenEvaluationReportDo,
  ChildrenSingleChoiceAnswerAccumulated,
} from '../models/children-evaluation-report';

@Component({
  selector: 'app-children-evaluation-report-component',
  templateUrl: './children-evaluation-report.component.html',
  styleUrls: [
    './children-evaluation-report.component.scss',
    '../children-evaluation-report.page.scss',
  ],
})
export class ChildrenEvaluationReportComponent implements OnInit {
  @Input() childEvalReportData: ChildrenEvaluationReportDo | null = null;
  @Input() childEvalReportDataToCompareWith: ChildrenEvaluationReportDo | null = null;
  @Input() reportType: ReportType;
  @Input() setAPeriod: string | null;
  @Input() setBPeriod: string | null;

  public ReportType = ReportType;
  public setAReportData: Array<ChildrenSingleChoiceAnswerAccumulated>;
  public setBReportData: Array<ChildrenSingleChoiceAnswerAccumulated>;
  public boxplotConfig: Array<BoxplotChartConfiguration> = [];
  public EvaluationType = EvaluationType;
  public boxplotLegendLeftCol: Array<string> = [];
  public boxplotLegendRightCol: Array<string> = [];
  public boxplotLegendItems: Map<string, string> = new Map();
  public illustrationTitleOffset = 0;

  constructor(
    public translate: TranslateService,
    public reportService: ReportService,
    private childEvalReportUtil: ChildEvaluationReportUtil,
  ) {}

  ngOnInit(): void {
    this.initSingleChoiceAnswersSorted();
    this.initBoxplotConfig();
    this.initBoxplotLegend();
  }

  getFreeFormAnswersFor(id: string): ChildrenEvaluationFreeFormAnswers {
    return this.childEvalReportData.freeFormAnswers.filter(
      (answers) => answers.questionId === id,
    )[0];
  }

  initSingleChoiceAnswersSorted(): void {
    const comparator = (a, b) => {
      return a.id.toString().localeCompare(b.id.toString(), 'en', { numeric: true });
    };
    this.setAReportData =
      this.childEvalReportData.calculatedScores.singleChoiceAnswersAccumulated.sort(comparator);
    if (this.childEvalReportDataToCompareWith !== null) {
      this.setBReportData =
        this.childEvalReportDataToCompareWith.calculatedScores.singleChoiceAnswersAccumulated.sort(
          comparator,
        );
    }
  }

  isChildrenEvaluationReport(): boolean {
    return this.reportType === ReportType.CHILDREN_EVALUATION_REPORT;
  }

  getAnswersToCompareWith(i: number): ChildrenSingleChoiceAnswerAccumulated | null {
    return this.setBReportData !== undefined ? this.setBReportData[i] : null;
  }

  private initBoxplotLegend(): void {
    const colLength = Math.ceil(this.setAReportData.length / 2);
    this.setAReportData.forEach((answerObj, index) => {
      const answerId = answerObj.id.replace('q', '');
      index < colLength
        ? this.boxplotLegendLeftCol.push(answerId)
        : this.boxplotLegendRightCol.push(answerId);
      this.boxplotLegendItems[answerId] = this.childEvalReportUtil.getTranslationFromQuestionId(
        answerObj.id,
      );
    });
  }

  private initBoxplotConfig(): void {
    if (this.reportType === ReportType.COMPARISON_REPORT) {
      const childEvaluations = [this.setAReportData, this.setBReportData];

      childEvaluations.forEach((childEvaluation) => {
        this.illustrationTitleOffset += 1;
        const config = [];
        childEvaluation.forEach((data, index) => {
          const scores = [];
          for (let i = 0; i < data.accumulatedScore.disagreeCompletely; i++) {
            scores.push(1);
          }
          for (let i = 0; i < data.accumulatedScore.disagreeToModerateDegree; i++) {
            scores.push(2);
          }
          for (let i = 0; i < data.accumulatedScore.agreeToModerateDegree; i++) {
            scores.push(3);
          }
          for (let i = 0; i < data.accumulatedScore.agreeCompletely; i++) {
            scores.push(4);
          }
          config.push({
            values: scores,
            color: this.childEvalReportUtil.getQuestionColorCode(data.id),
            label: index + 1,
          });
        });
        this.boxplotConfig.push({
          data: config,
          scaleY: {
            min: 1,
            max: 4,
          },
        });
      });
    }
  }
}
