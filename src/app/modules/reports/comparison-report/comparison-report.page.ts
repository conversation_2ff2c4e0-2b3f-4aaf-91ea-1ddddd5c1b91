import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  AdminService,
  AggregatedChildEvaluations,
  ComparisonReportDataRequest,
  ComparisonReportDataResponse,
  OrganizationReportDataResponse,
  SurveysAndResults,
} from '../../../services/admin/admin.service';
import { LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Utils } from '../../../utils/utils';
import { UserService } from '../../../services/user/user.service';
import { PrimaryButtonType } from '../../shared/components/primary-button/primary-button.component';
import { PrintService, PrintStyles } from '../../../services/print.service';
import { SurveyType } from '../../../services/jwt/survey-token.service';
import { ReportService } from '../report.service';
import { ReportType } from '../../organization/organizations/models';
import { Evaluation } from '../../../services/evaluation/models/evaluation';

@Component({
  selector: 'app-comparison-report',
  templateUrl: './comparison-report.page.html',
  styleUrls: ['./comparison-report.page.scss'],
})
export class ComparisonReportPage implements OnInit {
  @ViewChild('printSection', { read: ElementRef, static: false }) printSection: ElementRef;

  public buttonType = PrimaryButtonType.GRADIENT;
  public loadingUi: HTMLIonLoadingElement = null;
  public isLoading = true;
  public hasError = false;
  public reportData: ComparisonReportDataResponse;
  public organizationAndLocationName: string;
  public combinedSets: OrganizationReportDataResponse = new OrganizationReportDataResponse();
  public SurveyType = SurveyType;
  public ReportType = ReportType;
  public setAPeriod: string;
  public setBPeriod: string;

  private reportRequest: ComparisonReportDataRequest;

  constructor(
    private route: ActivatedRoute,
    private adminService: AdminService,
    private userService: UserService,
    public loadingController: LoadingController,
    private printService: PrintService,
    public translate: TranslateService,
    public reportService: ReportService,
  ) {}

  customPrint(): void {
    const printContent = this.printSection.nativeElement;
    this.printService.loadServerPdfGenerationStyles(printContent, PrintStyles.COMPARISON_REPORT);
  }

  ngOnInit(): void {
    const paramMap = this.route.snapshot.queryParamMap;
    this.reportRequest = ComparisonReportDataRequest.fromRequestParameters(paramMap);
    this.organizationAndLocationName = paramMap.get('nodeName');

    const authToken = paramMap.get('authToken');
    if (authToken) {
      this.userService.authToken = authToken;
    }

    this.fetchReportData();
  }

  hasSurveyResult(surveyType: SurveyType): boolean {
    return this.reportService.hasResult(this.combinedSets.surveysAndResults, surveyType);
  }

  hasOneSurveyResult(surveys: Array<SurveyType>): boolean {
    return this.reportService.hasOneSurveyResult(surveys, this.combinedSets.surveysAndResults);
  }

  hasAllSurveyResults(surveys: Array<SurveyType>): boolean {
    return this.reportService.hasAllSurveyResults(surveys, this.combinedSets.surveysAndResults);
  }

  hasAttachments(): boolean {
    return (
      this.hasFirstAttachment() ||
      this.hasSecondAttachment() ||
      this.hasThirdAttachment() ||
      this.hasFourthAttachment()
    );
  }

  hasFirstAttachment(): boolean {
    return !this.hasSurveyResult(SurveyType.STAFF_SURVEY)
      ? false
      : this.combinedSets.surveysAndResults.staffSurveyResults.map((it) => it.pfp_004_2).length > 0;
  }

  hasSecondAttachment(): boolean {
    return !this.hasSurveyResult(SurveyType.PARENTAL_SURVEY)
      ? false
      : this.combinedSets.surveysAndResults.parentalSurveyResults.map((it) => it.fam_005_3).length >
          0;
  }

  hasThirdAttachment(): boolean {
    return !this.hasSurveyResult(SurveyType.PARENTAL_SURVEY)
      ? false
      : this.combinedSets.surveysAndResults.parentalSurveyResults.map((it) => it.fam_003_0).length >
          0;
  }

  hasFourthAttachment(): boolean {
    return !this.hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)
      ? false
      : this.combinedSets.surveysAndResults.organizationSurveyResults.map((it) => it.t_003_0)
          .length > 0;
  }

  hasEvaluation(): boolean {
    return (
      this.reportData.dataResponseSetA.evaluation !== null &&
      this.reportData.dataResponseSetB.evaluation !== null
    );
  }

  hasChildEvaluation(): boolean {
    return (
      this.reportData.dataResponseSetA.childEvaluations.aggregatedChildEvaluation !== null &&
      this.reportData.dataResponseSetB.childEvaluations.aggregatedChildEvaluation !== null
    );
  }

  private async fetchReportData(): Promise<void> {
    this.isLoading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getComparisonReportData(this.reportRequest).subscribe(async (resp) => {
      if (resp.success) {
        this.initData(resp.data);
      }
      await this.loadingUi.dismiss();
      this.isLoading = false;
      this.hasError = !resp.success;
    });
  }

  private initData(data: ComparisonReportDataResponse): void {
    this.reportData = data;
    this.initSurveys(data);
  }

  private initSurveys(data: ComparisonReportDataResponse): void {
    const setA = data.dataResponseSetA;
    const setB = data.dataResponseSetB;

    this.combinedSets.surveysAndResults = new SurveysAndResults();
    this.combinedSets.surveysAndResults.surveys = setA.surveysAndResults.surveys.concat(
      setB.surveysAndResults.surveys,
    );
    this.combinedSets.surveysAndResults.parentalSurveyResults =
      setA.surveysAndResults.parentalSurveyResults.concat(
        setB.surveysAndResults.parentalSurveyResults,
      );
    this.combinedSets.surveysAndResults.organizationSurveyResults =
      setA.surveysAndResults.organizationSurveyResults.concat(
        setB.surveysAndResults.organizationSurveyResults,
      );
    this.combinedSets.surveysAndResults.staffSurveyResults =
      setA.surveysAndResults.staffSurveyResults.concat(setB.surveysAndResults.staffSurveyResults);
    this.combinedSets.surveysAndResults.administrationSurveyResults =
      setA.surveysAndResults.administrationSurveyResults.concat(
        setB.surveysAndResults.administrationSurveyResults,
      );
    this.combinedSets.surveysMinDate = data.surveysMinDate;
    this.combinedSets.surveysMaxDate = data.surveysMaxDate;

    this.setAPeriod = this.reportService.getSurveyPeriodTranslation(
      setA.surveysMinDate,
      setA.surveysMaxDate,
    );
    this.setBPeriod = this.reportService.getSurveyPeriodTranslation(
      setB.surveysMinDate,
      setB.surveysMaxDate,
    );
  }

  public getEvaluations(): Array<Evaluation> | undefined {
    const setAEval = this.reportData.dataResponseSetA.evaluation;
    const setBEval = this.reportData.dataResponseSetB.evaluation;
    return setAEval !== null && setBEval !== null ? [setAEval, setBEval] : undefined;
  }

  public getChildEvaluations(): Array<AggregatedChildEvaluations> | undefined {
    const setAChildEval = this.reportData.dataResponseSetA.childEvaluations;
    const setBChildEval = this.reportData.dataResponseSetB.childEvaluations;
    return setAChildEval !== null && setBChildEval !== null
      ? [setAChildEval, setBChildEval]
      : undefined;
  }
}
