<ion-content *ngIf="!isLoading && hasError">
  <app-not-found
    [message]="translate.instant('childrenEvaluation.report.notFound')"
  ></app-not-found>
</ion-content>

<ion-content *ngIf="!isLoading && !hasError">
  <ion-header no-border>
    <ion-toolbar>
      <div class="print-button">
        <app-primary-button
          [buttonType]="buttonType"
          label="{{'global.button.print' | translate}}"
          (onClick)="customPrint()"
        ></app-primary-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <span #printSection>
    <div class="screen-container">
      <div class="page-break">
        <app-report-front-page
          subTitleKey="reports.comparison.sub_title"
          [organizationName]="organizationAndLocationName"
          [surveyResults]="combinedSets.surveysAndResults"
          [childEvaluations]="getChildEvaluations()"
          [evaluation]="getEvaluations()"
          [surveysMinDate]="combinedSets.surveysMinDate"
          [surveysMaxDate]="combinedSets.surveysMaxDate"
        ></app-report-front-page>
      </div>

      <div class="page-break">
        <app-tb001></app-tb001>
        <app-tb002></app-tb002>
        <app-tb003></app-tb003>
      </div>

      <div class="page-break">
        <app-tb004></app-tb004>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.ORGANIZATION_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
      >
        <app-tb005
          *ngIf="hasOneSurveyResult([SurveyType.ORGANIZATION_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
        ></app-tb005>
        <app-tb006
          *ngIf="hasOneSurveyResult([SurveyType.ORGANIZATION_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
        ></app-tb006>
        <app-tb007 *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"></app-tb007>

        <app-abb001-comparison
          *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"
          [setAOrganizationSurveyResults]="reportData.dataResponseSetA.surveysAndResults.organizationSurveyResults"
          [setBOrganizationSurveyResults]="reportData.dataResponseSetB.surveysAndResults.organizationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb001-comparison>

        <app-tb008 *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"></app-tb008>

        <app-abb002-comparison
          *ngIf="hasSurveyResult(SurveyType.ORGANIZATION_SURVEY)"
          [setAOrganizationSurveyResults]="reportData.dataResponseSetA.surveysAndResults.organizationSurveyResults"
          [setBOrganizationSurveyResults]="reportData.dataResponseSetB.surveysAndResults.organizationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        >
        </app-abb002-comparison>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-tb009 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb009>

        <app-abb003-comparison
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb003-comparison>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.PARENTAL_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
      >
        <app-tb010
          *ngIf="hasOneSurveyResult([SurveyType.PARENTAL_SURVEY, SurveyType.ADMINISTRATION_SURVEY, SurveyType.STAFF_SURVEY])"
        ></app-tb010>
        <app-tb011-comparison
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.administrationSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.administrationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-tb011-comparison>
        <p *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)">
          {{"reports.survey.TB_011.imageExplanation" | translate}}
        </p>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-abb004-comparison
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        >
        </app-abb004-comparison>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-abb005-comparison
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        >
        </app-abb005-comparison>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-abb006-comparison
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb006-comparison>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-tb012 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb012>
        <app-abb007-comparison
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb007-comparison>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)">
        <app-tb013 *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"></app-tb013>
        <app-abb008></app-abb008>
        <app-abb009-comparison
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.parentalSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.parentalSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb009-comparison>
        <app-tb014
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="combinedSets.surveysAndResults.parentalSurveyResults"
        ></app-tb014>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)">
        <app-tb015 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb015>
        <app-abb010-comparison
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb010-comparison>
      </div>

      <div class="page-break">
        <app-tb016-one *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb016-one>
        <app-tb016-two *ngIf="!hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb016-two>
        <app-abb011-comparison
          *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb011-comparison>
        <app-tb017 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb017>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
      >
        <app-tb018
          *ngIf="hasOneSurveyResult([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
        ></app-tb018>
        <app-abb012-comparison
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.parentalSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.parentalSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb012-comparison>

        <app-tb019
          *ngIf="hasSurveyResult(SurveyType.PARENTAL_SURVEY)"
          [data]="combinedSets.surveysAndResults.parentalSurveyResults"
        ></app-tb019>
        <app-tb020
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
        ></app-tb020>
      </div>

      <div class="page-break" *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)">
        <app-abb013-comparison
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.ORGANIZATION_SURVEY])"
          [setAAdministrationSurveyResults]="reportData.dataResponseSetA.surveysAndResults.administrationSurveyResults"
          [setBAdministrationSurveyResults]="reportData.dataResponseSetB.surveysAndResults.administrationSurveyResults"
          [setAOrganizationSurveyResults]="reportData.dataResponseSetA.surveysAndResults.organizationSurveyResults"
          [setBOrganizationSurveyResults]="reportData.dataResponseSetB.surveysAndResults.organizationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb013-comparison>
        <app-tb022 *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"></app-tb022>
        <app-abb014-comparison
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.administrationSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.administrationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb014-comparison>
      </div>

      <div
        class="page-break"
        *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY])"
      >
        <div
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY])"
        >
          <app-tb023></app-tb023>
          <app-tb024></app-tb024>
          <app-tb025></app-tb025>
        </div>
        <app-abb015-table-comparison
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY])"
          [setAAdministrationSurveyResult]="reportData.dataResponseSetA.surveysAndResults.administrationSurveyResults"
          [setBAdministrationSurveyResult]="reportData.dataResponseSetB.surveysAndResults.administrationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        >
        </app-abb015-table-comparison>
      </div>

      <div
        class="page-break"
        *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY])"
      >
        <app-abb015-bar-chart-comparison
          *ngIf="hasAllSurveyResults([SurveyType.ADMINISTRATION_SURVEY, SurveyType.PARENTAL_SURVEY])"
          [setAParentalSurveyResults]="reportData.dataResponseSetA.surveysAndResults.parentalSurveyResults"
          [setBParentalSurveyResults]="reportData.dataResponseSetB.surveysAndResults.parentalSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        >
        </app-abb015-bar-chart-comparison>
      </div>

      <div
        class="page-break"
        *ngIf="hasOneSurveyResult([SurveyType.STAFF_SURVEY, SurveyType.ADMINISTRATION_SURVEY])"
      >
        <app-tb026 *ngIf="hasSurveyResult(SurveyType.STAFF_SURVEY)"></app-tb026>
        <app-abb016-comparison
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.administrationSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.administrationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb016-comparison>
        <app-abb017-comparison
          *ngIf="hasSurveyResult(SurveyType.ADMINISTRATION_SURVEY)"
          [setA]="reportData.dataResponseSetA.surveysAndResults.administrationSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.administrationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-abb017-comparison>
      </div>

      <div class="page-break" *ngIf="hasAttachments()">
        <h1>{{"reports.survey.attachments.title" | translate}}</h1>

        <app-attachment001-comparison
          *ngIf="hasFirstAttachment()"
          [setA]="reportData.dataResponseSetA.surveysAndResults.staffSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.staffSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-attachment001-comparison>

        <app-attachment002-comparison
          *ngIf="hasSecondAttachment()"
          [setA]="reportData.dataResponseSetA.surveysAndResults.parentalSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.parentalSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-attachment002-comparison>

        <app-attachment003-comparison
          *ngIf="hasThirdAttachment()"
          [setA]="reportData.dataResponseSetA.surveysAndResults.parentalSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.parentalSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-attachment003-comparison>

        <app-attachment004-comparison
          *ngIf="hasFourthAttachment()"
          [setA]="reportData.dataResponseSetA.surveysAndResults.organizationSurveyResults"
          [setB]="reportData.dataResponseSetB.surveysAndResults.organizationSurveyResults"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-attachment004-comparison>
      </div>

      <app-comparison-report-evaluations
        *ngIf="hasEvaluation()"
        [evaluationData]="reportData"
        [setAPeriod]="setAPeriod"
        [setBPeriod]="setBPeriod"
      ></app-comparison-report-evaluations>

      <div class="page-break" *ngIf="hasChildEvaluation()">
        <app-children-evaluation-report-component
          [childEvalReportData]="reportData.dataResponseSetA.childEvaluations.aggregatedChildEvaluation"
          [childEvalReportDataToCompareWith]="reportData.dataResponseSetB.childEvaluations.aggregatedChildEvaluation"
          [reportType]="ReportType.COMPARISON_REPORT"
          [setAPeriod]="setAPeriod"
          [setBPeriod]="setBPeriod"
        ></app-children-evaluation-report-component>
      </div>
    </div>
  </span>
</ion-content>
