import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { EvaluatorAnalysisPageModule } from '../../pages/evaluator-analysis/evaluator-analysis.module';
import { ChildrenEvaluationBubbleChartComponent } from '../shared/components/child-evaluation-report/children-evaluation-bubble-chart/children-evaluation-bubble-chart.component';
import { SharedModule } from '../shared/shared.module';
import { ChildrenEvaluationReportPage } from './children-evaluation-report/children-evaluation-report.page';
import { ChildrenEvaluationReportComponent } from './children-evaluation-report/children-evaluation-report/children-evaluation-report.component';
import { ComparisonReportPage } from './comparison-report/comparison-report.page';
import { LocationReportPage } from './location-report/location-report.page';
import { OrganizationReportPage } from './organization-report/organization-report.page';
import { ReportRoutingModule } from './report-routing.module';
import { SharedReportModule } from './shared-report-components/shared.module';

@NgModule({
  declarations: [
    ChildrenEvaluationReportPage,
    OrganizationReportPage,
    LocationReportPage,
    ComparisonReportPage,
    ChildrenEvaluationBubbleChartComponent,
    ChildrenEvaluationReportComponent,
  ],
  imports: [
    SharedReportModule,
    CommonModule,
    SharedModule,
    ReportRoutingModule,
    EvaluatorAnalysisPageModule,
  ],
  providers: [],
})
export class ReportModule {}
