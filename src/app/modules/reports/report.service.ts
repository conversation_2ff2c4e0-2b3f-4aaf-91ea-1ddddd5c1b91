import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SurveyType } from '../../services/jwt/survey-token.service';
import { SurveysAndResults } from '../../services/admin/admin.service';
import { LegendEntry } from '../shared/components/charts/chart-legend/chart-legend.component';
import { CHART_COLORS } from '../../utils/chart-colors';
import { SegmentConfiguration } from '../shared/components/charts/report-chart-models';
import moment from 'moment';

@Injectable({
  providedIn: 'root',
})
export class ReportService {
  constructor(private translate: TranslateService) {}

  public getYesNoMultiBarChartLegend(): LegendEntry[] {
    return [
      { description: this.translate.instant('surveys.answers.x.x.yes'), color: CHART_COLORS.green },
      { description: this.translate.instant('surveys.answers.x.x.no'), color: CHART_COLORS.purple },
    ];
  }

  public getIllustrationTitleTranslation(illustrationId: number): string {
    return this.translate.instant('reports.survey.illustration', {
      illustrationNumber: illustrationId,
    });
  }

  public getTableTitleTranslation(tableId: number): string {
    return this.translate.instant('reports.survey.table', {
      tableNumber: tableId,
    });
  }

  public getSurveyPeriodTranslation(start: Date, end: Date): string {
    const format = (date: Date) => moment(date).format('MMMM YYYY');
    return this.translate.instant('reports.survey.time_period', {
      startDate: format(start),
      endDate: format(end),
    });
  }

  public getIllustrationFooter(
    surveyType: SurveyType,
    attendees: number,
    description?: string,
  ): string {
    const amountAttendeesKey = this.getAmoundAttendeesTranslationKey(surveyType);
    const note = this.translate.instant('reports.survey.note');
    const amountAttendees = this.translate.instant(amountAttendeesKey, {
      amountAttendees: attendees,
    });
    description = description !== undefined ? description : '';
    return `${note} ${amountAttendees} ${description}`;
  }

  public getComparisonReportIllustrationFooter(
    amountAttendeesSetA: number,
    amountAttendeesSetB: number,
    setAPeriod: string,
    setBPeriod: string,
    surveyType: SurveyType,
    description?: string,
  ): string {
    const amountAttendeesKey = this.getAmoundAttendeesTranslationKey(surveyType);
    const note = this.translate.instant('reports.survey.note');
    const setAAmountAttendees = this.translate.instant(amountAttendeesKey, {
      amountAttendees: amountAttendeesSetA,
    });
    const setBAmountAttendees = this.translate.instant(amountAttendeesKey, {
      amountAttendees: amountAttendeesSetB,
    });
    description = description !== undefined ? description : '';

    return `${note} ${setAPeriod} - ${setAAmountAttendees} ${setBPeriod} - ${setBAmountAttendees} ${description}`;
  }

  private getAmoundAttendeesTranslationKey(surveyType: SurveyType): string {
    switch (surveyType) {
      case SurveyType.STAFF_SURVEY:
        return 'reports.survey.staff.amountAttendees';
      case SurveyType.ADMINISTRATION_SURVEY:
        return 'reports.survey.administration.amountAttendees';
      case SurveyType.PARENTAL_SURVEY:
        return 'reports.survey.family.amountAttendees';
      default:
        return '';
    }
  }

  public calcPercentage(part: number, whole: number): number {
    return Math.floor((part / whole) * 100);
  }

  public getDistributionOfAnswer(
    answers: string[],
    answer: string,
    isAbsolute: boolean = false,
  ): number {
    const numberOfAnswers = answers.filter((it) => it.includes(answer)).length;
    return answers.length === 0
      ? 0
      : isAbsolute
        ? numberOfAnswers
        : this.calcPercentage(numberOfAnswers, answers.length);
  }

  public getYesNoSegment(answers: string[], isAbsolute: boolean = false): SegmentConfiguration[] {
    return [
      {
        value: this.getDistributionOfAnswer(answers, 'yes', isAbsolute),
        color: CHART_COLORS.green,
      },
      {
        value: this.getDistributionOfAnswer(answers, 'no', isAbsolute),
        color: CHART_COLORS.purple,
      },
    ];
  }

  public filterAnswers(data: string[], filter: string): string[] {
    return data.filter((it) => it.includes(filter));
  }

  public hasResult(data: SurveysAndResults, type: SurveyType): boolean {
    switch (type) {
      case SurveyType.ORGANIZATION_SURVEY:
        return data.organizationSurveyResults.length >= 1;
      case SurveyType.ADMINISTRATION_SURVEY:
        return data.administrationSurveyResults.length >= 1;
      case SurveyType.PARENTAL_SURVEY:
        return data.parentalSurveyResults.length >= 1;
      case SurveyType.STAFF_SURVEY:
        return data.staffSurveyResults.length >= 1;
      default:
        return false;
    }
  }

  public hasOneSurveyResult(surveys: Array<SurveyType>, data: SurveysAndResults): boolean {
    return surveys.filter((it) => this.hasResult(data, it)).length > 0;
  }

  public hasAllSurveyResults(surveys: Array<SurveyType>, data: SurveysAndResults): boolean {
    return surveys.filter((it) => this.hasResult(data, it)).length === surveys.length;
  }
}
