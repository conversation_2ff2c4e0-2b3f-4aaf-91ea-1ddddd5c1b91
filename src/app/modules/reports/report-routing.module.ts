import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { ChildrenEvaluationReportPage } from './children-evaluation-report/children-evaluation-report.page';
import { OrganizationReportPage } from './organization-report/organization-report.page';
import { LocationReportPage } from './location-report/location-report.page';
import { ComparisonReportPage } from './comparison-report/comparison-report.page';

const routes: Routes = [
  {
    path: 'children-evaluation/:evaluationRemoteId/:token/:lang',
    component: ChildrenEvaluationReportPage,
  },
  {
    path: 'children-evaluation/:evaluationRemoteId',
    component: ChildrenEvaluationReportPage,
  },
  {
    path: 'organization',
    component: OrganizationReportPage,
  },
  {
    path: 'location',
    component: LocationReportPage,
  },
  {
    path: 'comparison',
    component: ComparisonReportPage,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  declarations: [],
  exports: [RouterModule],
})
export class ReportRoutingModule {}
