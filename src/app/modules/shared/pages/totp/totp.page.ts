import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { TotpState, UserService } from '../../../../services/user/user.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { take } from 'rxjs';
import { AdminRoutes } from '../../../../app-routes';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-totp',
  templateUrl: './totp.page.html',
  styleUrls: ['./totp.page.scss'],
})
export class TotpPage {
  totpForm!: FormGroup;

  userLoginIdentifier = '';
  password = '';
  totpState: TotpState;
  totpSetupQrCodeUrl = '';
  totpSecret = '';

  isLoading = false;
  errorMessage = '';

  showSecretAsText = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private userService: UserService,
    private translate: TranslateService,
  ) {
    const state = this.router.getCurrentNavigation()?.extras.state;
    if (state) {
      this.userLoginIdentifier = state['userLoginIdentifier'];
      this.password = state['password'];
      this.totpState = state['totpState'];
      this.totpSetupQrCodeUrl = state['totpSetupQrCodeUrl'];
      this.totpSecret = state['totpSecret'];
    }

    this.totpForm = this.formBuilder.group({
      totpCode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
    });
  }

  submitForm() {
    this.isLoading = true;
    this.errorMessage = '';

    this.userService
      .totpLogin(this.userLoginIdentifier, this.password, this.totpForm.value.totpCode)
      .pipe(take(1))
      .subscribe((response) => {
        if (response.success) {
          this.router.navigate([AdminRoutes.dashboard]);
        } else {
          if (response.httpStatus == 401) {
            this.errorMessage = this.translate.instant('totp.error.invalidCode');
          } else {
            this.errorMessage = this.translate.instant('global.error.generic');
          }
        }

        this.isLoading = false;
      });
  }

  navigateImprint(): void {
    this.router.navigate(['/imprint']);
  }

  swapQrCodeWithSecretAsText() {
    this.showSecretAsText = !this.showSecretAsText;
  }

  get isTotpSetup() {
    return this.totpState == TotpState.SETUP_REQUIRED && this.totpSetupQrCodeUrl && this.totpSecret;
  }

  get swapQrCodeWithTextLinkTranslation() {
    return this.translate.instant(this.showSecretAsText ? 'totp.setup.showQrCode' : 'totp.setup.cantScan');
  }
}
