@import './../../../../../theme/variables_v2';

ion-grid,
ion-col {
  padding: 0;
  height: 100%;
}

.scene {
  position: relative;
  height: 100%;
  width: 100%;
  z-index: 1;

  background-size: cover !important;

  &.login-img {
    background: url('../../../../../assets/img/tablet_login.webp') top;
  }
}

.background-color {
  background-color: var(--ion-color-primary-V2);
}

.img-footer {
  position: absolute;
  z-index: 2;
  bottom: 50px;
  left: 50px;
  color: white;

  a {
    color: white;
    cursor: pointer;
    font-weight: bold;
    text-decoration: underline;
  }
}

.scroll-content {
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
  justify-content: space-around;

  .content-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 15%;
    padding: 32px 0;

    .login-header-image-container {
      width: 100px;
      height: auto;
      margin-bottom: 40px;
    }

    h3, span {
      color: white;
    }

    h3 {
      margin-bottom: 16px;
    }

    .separator-line {
      border: none;
      height: 2px;
      background-color: white;
      margin: 32px 0;
      width: 100%;
    }

    ol {
      margin: 0;
      padding: 0;

      list-style: none;
      counter-reset: item;

      li {
        position: relative;
        padding-left: 1.5rem;
        color: white;

        &::before {
          counter-increment: item;
          content: counter(item) ".";
          position: absolute;
          left: 0;
          color: white;
        }
      }
    }

    .qr-code-container {
      display: flex;
      flex-direction: column;
      align-items: center;

      margin: 32px 0;

      qrcode {
        margin-bottom: 1rem;
      }

      a {
        color: white;
        cursor: pointer;
        font-weight: bold;
        text-decoration: underline;
        text-align: center;
      }

      p {
        color: white;
        font-weight: bold;

        word-break: break-all;
      }
    }

    .input-container {
      width: 100%;

      .input-field {
        margin-bottom: 16px;
      }

      .error {
        color: var(--ion-color-danger-V2);
        font-weight: bold;
      }
    }
  }
}
