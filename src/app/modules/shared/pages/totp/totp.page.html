<ion-content [fullscreen]="true" class="design-v2" [forceOverscroll]="false">
    <ion-grid class="h-100">
        <ion-row class="h-100">
            <ion-col size="7" class="login-img-container">
                <div class="scene login-img"></div>
                <div class="img-footer">
                    <div>{{ 'login.more' | translate }}</div>
                    <a (click)="navigateImprint()">{{ 'login.imprint' | translate }}</a>
                </div>
            </ion-col>
            <ion-col size="5" class="background-color disable-scrollbar scroll-content">
                <div class="content-container">
                    <div class="login-header-image-container">
                        <ion-img src="assets/img/logo_white.png" alt="GrazIAS"></ion-img>
                    </div>
                    @if (isTotpSetup) {
                        <span>{{ 'totp.setup.description' | translate }}</span>

                        <hr class="separator-line"/>

                        <ol>
                          <li>
                            <span>{{ 'totp.setup.stepOne' | translate }}</span>
                            <div class="qr-code-container">
                              @if (showSecretAsText) {
                                <span>{{ 'totp.setup.manualSecret' | translate }}</span>
                                <p>{{ totpSecret }}</p>
                              } @else {
                                <qrcode [qrdata]="totpSetupQrCodeUrl">
                                </qrcode>
                              }
                              <a (click)="swapQrCodeWithSecretAsText()">
                                {{ swapQrCodeWithTextLinkTranslation }}
                              </a>
                            </div>
                          </li>
                          <li>
                            <span>{{ 'totp.setup.stepTwo' | translate }}</span>
                          </li>
                        </ol>
                    } @else {
                        <h3>{{ 'totp.verification.title' | translate }}</h3>
                        <span>{{ 'totp.verification.description' | translate }}</span>
                    }

                    <hr class="separator-line"/>

                    <form
                            [formGroup]="totpForm"
                            (ngSubmit)="submitForm()"
                            class="input-container">
                        <div class="input-field">
                            <app-input-field
                                    formControlName="totpCode"
                                    type="number"
                                    placeholder="000000">
                            </app-input-field>
                        </div>
                        @if (errorMessage) {
                          <p class="error"> {{ errorMessage }} </p>
                        }
                        <div class="submit-button">
                            <app-primary-button
                                    class="button"
                                    type="submit"
                                    [isDisabled]="isLoading || !totpForm.valid"
                                    [isLoading]="isLoading"
                                    [label]="'totp.button.submit' | translate">
                            </app-primary-button>
                        </div>
                    </form>
                </div>
            </ion-col>
        </ion-row>
    </ion-grid>
</ion-content>
