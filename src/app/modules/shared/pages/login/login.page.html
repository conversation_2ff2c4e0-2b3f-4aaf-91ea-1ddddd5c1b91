<ion-content [fullscreen]="true" class="design-v2" [forceOverscroll]="false">
  <ion-grid class="h-100">
    <ion-row class="h-100">
      <ion-col size="7" class="login-img-container">
        <div class="scene login-img"></div>
        <div class="img-footer">
          <div>{{'login.more' | translate}}</div>
          <a (click)="navigateImprint()">{{'login.imprint' | translate}}</a>
        </div>
      </ion-col>
      <ion-col size="5" class="background-color disable-scrollbar scroll-content">
        <div class="content-container">
          <div class="login-header-image-container">
            <ion-img src="assets/img/logo_white.png" alt="GrazIAS"></ion-img>
          </div>
          <form
            [formGroup]="loginForm"
            (ngSubmit)="submitForm()"
            (keyup.enter)="submitForm()"
            class="input-container"
          >
            <div class="email-field">
              <app-input-field
                formControlName="email"
                type="email"
                (onFocus)="onFocus(loginFocusNode.EMAIL)"
                (onBlur)="onBlur(loginFocusNode.EMAIL)"
                [invalid]="emailErrorMessage !== ''"
                [placeholder]="'login.placeholder.email' | translate"
              >
              </app-input-field>
              <ng-container *ngIf="emailErrorMessage">
                <div class="validation-box">
                  <ion-text> {{emailErrorMessage | translate}} </ion-text>
                </div>
              </ng-container>
            </div>
            <div class="password-field">
              <app-input-field
                formControlName="password"
                type="password"
                (onFocus)="onFocus(loginFocusNode.PASSWORD)"
                (onBlur)="onBlur(loginFocusNode.PASSWORD)"
                [invalid]="passwordErrorMessage !== ''"
                [placeholder]="'login.placeholder.password' | translate"
              >
              </app-input-field>
              <ng-container *ngIf="passwordErrorMessage">
                <div class="validation-box">
                  <ion-text> {{passwordErrorMessage | translate}} </ion-text>
                </div>
              </ng-container>
            </div>
            <div class="validation-box" *ngIf="isError">
              <ion-text> {{ errorMessage }} </ion-text>
            </div>
            <div class="login-button">
              <app-primary-button
                type="submit"
                expand="block"
                [isDisabled]="isLoading"
                [isLoading]="isLoading"
                [label]="'login.button.submit' | translate"
              >
              </app-primary-button>
            </div>
          </form>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
