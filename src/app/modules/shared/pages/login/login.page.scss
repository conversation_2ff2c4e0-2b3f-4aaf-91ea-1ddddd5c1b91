@import './../../../../../theme/variables_v2';

ion-grid,
ion-col {
  padding: 0;
  height: 100%;
}

.scene {
  position: relative;
  height: 100%;
  width: 100%;
  z-index: 1;

  background-size: cover !important;

  &.login-img {
    background: url('../../../../../assets/img/tablet_login.webp') top;
  }

  &.register-img {
    background: url('../../../../../assets/img/tablet_register.webp') top;
  }
}

.background-color {
  background-color: var(--ion-color-primary-V2);
}

.content-container {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: auto 15%;

  .login-header-image-container {
    width: 100px;
    height: auto;
    margin-bottom: 40px;
  }

  .input-container {
    width: 100%;

    .email-field {
      margin-bottom: 16px;
    }

    .password-field {
      margin-bottom: 16px;
    }

    .validation-box {
      margin-top: 8px;
      margin-bottom: 16px;

      ::ng-deep ion-text {
        font-size: 14px;
        font-weight: bold;
        color: $color-danger;
        line-height: 22px;
      }
    }
  }
}

.login-button {
  margin-top: 32px;
}

.login-img-container {
  position: relative;
}

.img-footer {
  position: absolute;
  z-index: 2;
  bottom: 50px;
  left: 50px;
  color: white;

  a {
    color: white;
    cursor: pointer;
    font-weight: bold;
    text-decoration: underline;
  }
}

.scroll-content {
  overflow: scroll;
  height: 100vh;
}
