import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { TotpState, UserService } from 'src/app/services/user/user.service';
import { LoginUtils } from './loginUtils';
import { NavController } from '@ionic/angular';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage {
  public version: string;
  public isError = false;
  public isLoading = false;
  public errorMessage: string;
  public focus: LoginFocusNode[] = [];
  loginFocusNode = LoginFocusNode;

  private hasTriedToLogin: boolean;

  constructor(
    private translate: TranslateService,
    private formBuilder: UntypedFormBuilder,
    private userService: UserService,
    private navController: NavController,
  ) {}

  get passwordErrorMessage(): string {
    const passwordControl = this.loginForm.get('password');
    return LoginUtils.validatePassword(!this.hasTriedToLogin, passwordControl);
  }

  get emailErrorMessage(): string {
    const emailControl = this.loginForm.get('email');
    return LoginUtils.validateMail(!this.hasTriedToLogin, emailControl);
  }

  public loginForm = this.formBuilder.group({
    email: ['', [Validators.required]],
    password: ['', [Validators.required, Validators.minLength(8), Validators.maxLength(100)]],
  });

  navigateImprint(): void {
    this.navController.navigateForward(['/imprint']);
  }

  async submitForm(): Promise<void> {
    this.isError = false;
    this.errorMessage = '';
    this.hasTriedToLogin = true;

    if (this.loginForm.valid) {
      this.isLoading = true;
      this.userService
        .requestTotpLogin(this.loginForm.value.email, this.loginForm.value.password)
        .subscribe(async (result) => {
          this.isError = !result.success;

          if (!result.success) {
            if (result.httpStatus == 401) {
              this.errorMessage = this.translate.instant('mobileAppV2.generic.error.login');
            } else {
              this.errorMessage = this.translate.instant('global.error.generic');
            }
            this.isLoading = false;
            return;
          }

          this.navController.navigateRoot(['/totp'], {
            state: {
              userLoginIdentifier: this.loginForm.value.email,
              password: this.loginForm.value.password,
              totpState: result.data.totpState,
              totpSetupQrCodeUrl: result.data.totpSetupQrCodeUrl,
              totpSecret: result.data.totpSecret,
            },
          });
          this.isLoading = false;
        });
    }
  }

  onFocus(focusNode: LoginFocusNode): void {
    if (!this.focus.includes(focusNode)) {
      this.focus.push(focusNode);
    }
  }

  onBlur(focusNode: LoginFocusNode): void {
    const index = this.focus.indexOf(focusNode, 0);
    if (index > -1) {
      this.focus.splice(index, 1);
    }
  }
}

enum LoginFocusNode {
  EMAIL,
  PASSWORD,
}
