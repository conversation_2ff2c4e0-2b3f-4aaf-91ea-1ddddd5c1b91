import { AbstractControl } from '@angular/forms';

export class LoginUtils {
  static validatePassword(
    isInitialFormFocus: boolean,
    passwordControl: AbstractControl,
    confirmPasswordControl?: AbstractControl,
  ): string {
    if (isInitialFormFocus) {
      return '';
    }

    if (passwordControl.hasError('required')) {
      return 'global.error.required';
    } else if (passwordControl.hasError('minlength')) {
      return 'global.error.password_min_length';
    } else if (passwordControl.hasError('maxlength')) {
      return 'global.error.password_max_length';
    } else if (
      confirmPasswordControl != null &&
      passwordControl.value !== confirmPasswordControl.value
    ) {
      return 'register.error.passwords_do_not_match';
    } else {
      return '';
    }
  }

  static validateMail(isInitialFormFocus: boolean, emailControl: AbstractControl): string {
    if (isInitialFormFocus) {
      return '';
    }

    if (emailControl.hasError('required')) {
      return 'global.error.required';
    } else if (emailControl.hasError('email')) {
      return 'global.error.email';
    } else {
      return '';
    }
  }
}
