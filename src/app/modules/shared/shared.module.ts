import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { IsoDatePipe } from 'src/app/pipes/iso-date.pipe';
import { GraziasMissingTranslationHandler } from '../../utils/missingTranslationHandler';
import { AddUserPopUpComponent } from './components/add-user-pop-up/add-user-pop-up.component';
import { BarChartComponent } from './components/charts/bar-chart/bar-chart.component';
import { BoxplotChartComponent } from './components/charts/boxplot-chart/boxplot-chart.component';
import { BubbleChartComponent } from './components/charts/bubble-chart/bubble-chart.component';
import { ChartLegendComponent } from './components/charts/chart-legend/chart-legend.component';
import { DoughnutChartComponent } from './components/charts/donut-chart/donut-chart.component';
import { MultiBarChartWithDescriptionComponent } from './components/charts/multi-bar-chart-with-description/multi-bar-chart-with-description.component';
import { MultiBarChartWithLinesComponent } from './components/charts/multi-bar-chart-with-lines/multi-bar-chart-with-lines.component';
import { SingleBarChartWithDescriptionComponent } from './components/charts/single-bar-chart-with-description/single-bar-chart-with-description.component';
import { SingleBarChartComponent } from './components/charts/single-bar-chart/single-bar-chart.component';
import { ChildrenEvaluationBarChartExplanationComponent } from './components/child-evaluation-report/children-evaluation-bar-chart-explanation/children-evaluation-bar-chart-explanation.component';
import { ChildrenEvaluationBarChartComponent } from './components/child-evaluation-report/children-evaluation-bar-chart/children-evaluation-bar-chart.component';
import { ChildrenEvaluationFreeFormAnswersComponent } from './components/child-evaluation-report/children-evaluation-free-form-answers/children-evaluation-free-form-answers.component';
import { DatepickerWithHeadingComponent } from './components/datepicker-with-heading/datepicker-with-heading.component';
import { DropdownWithHeadingComponent } from './components/dropdown-with-heading/dropdown-with-heading.component';
import { DropdownComponent } from './components/dropdown/dropdown.component';
import { EmptyViewComponent } from './components/empty-view/empty-view.component';
import { EvaluationDetailInfoBoxComponent } from './components/evaluation-detail-info-box/evaluation-detail-info-box.component';
import { EvaluatorAnalysisTitleComponent } from './components/evaluator-analysis-title/evaluator-analysis-title.component';
import { IconButtonComponent } from './components/icon-button/icon-button.component';
import { InactiveCopyableInputFieldComponent } from './components/inactive-copyable-input-field/inactive-copyable-input-field.component';
import { InputFieldComponent } from './components/input-field/input-field.component';
import { InputWithHeadingAndSuggestionsComponent } from './components/input-with-heading-and-suggestions/input-with-heading-and-suggestions.component';
import { InputWithHeadingComponent } from './components/input-with-heading/input-with-heading.component';
import { NavigationButtonComponent } from './components/navigation-button/navigation-button.component';
import { NotFoundComponent } from './components/not-found/not-found.component';
import { PrimaryButtonComponent } from './components/primary-button/primary-button.component';
import { QuestionnaireTokenBoxComponent } from './components/questionnaire-token-box/questionnaire-token-box.component';
import { ReportFrontPageComponent } from './components/report-front-page/report-front-page.component';
import { ReportLanguagePopUpComponent } from './components/report-language-pop-up/report-language-pop-up.component';
import { SideNavBarComponent } from './components/side-nav-bar/side-nav-bar.component';
import { SubFilterListTileComponent } from './components/sub-filter-list-tile/sub-filter-list-tile.component';
import { SurveyDetailInfoBoxComponent } from './components/survey-detail-info-box/survey-detail-info-box.component';
import { SvgIconComponent } from './components/svg-icon/svg-icon.component';
import { TabBarComponent } from './components/tab-bar/tab-bar.component';
import { TaggingComponent } from './components/tagging/tagging.component';
import { TextareaWithHeadingComponent } from './components/textarea-with-heading/textarea-with-heading.component';
import { TraitAnalysisEducatorComparisonComponent } from './components/trait-analysis-educator-comparison/trait-analysis-educator-comparison.component';
import { TraitAnalysisTeamComponent } from './components/trait-analysis-team/trait-analysis-team.component';
import { TraitAnalysisComponent } from './components/trait-analysis/trait-analysis.component';
import { UpsertFormInfoBoxComponent } from './components/upsert-form-info-box/upsert-form-info-box.component';
import { LoginPage } from './pages/login/login.page';
import { QRCodeModule } from 'angularx-qrcode';
import { TotpPage } from './pages/totp/totp.page';

@NgModule({
  declarations: [
    PrimaryButtonComponent,
    InputFieldComponent,
    IconButtonComponent,
    LoginPage,
    TotpPage,
    InputWithHeadingComponent,
    DropdownWithHeadingComponent,
    UpsertFormInfoBoxComponent,
    TextareaWithHeadingComponent,
    NavigationButtonComponent,
    SvgIconComponent,
    SideNavBarComponent,
    DatepickerWithHeadingComponent,
    QuestionnaireTokenBoxComponent,
    SurveyDetailInfoBoxComponent,
    UpsertFormInfoBoxComponent,
    EmptyViewComponent,
    NotFoundComponent,
    InactiveCopyableInputFieldComponent,
    ChildrenEvaluationBarChartComponent,
    ChildrenEvaluationBarChartExplanationComponent,
    ChildrenEvaluationFreeFormAnswersComponent,
    TaggingComponent,
    InputWithHeadingAndSuggestionsComponent,
    EvaluationDetailInfoBoxComponent,
    SingleBarChartComponent,
    SingleBarChartWithDescriptionComponent,
    MultiBarChartWithDescriptionComponent,
    BoxplotChartComponent,
    ChartLegendComponent,
    DoughnutChartComponent,
    ReportFrontPageComponent,
    MultiBarChartWithLinesComponent,
    SubFilterListTileComponent,
    AddUserPopUpComponent,
    ReportLanguagePopUpComponent,
    DropdownComponent,
    BarChartComponent,
    TraitAnalysisTeamComponent,
    EvaluatorAnalysisTitleComponent,
    TraitAnalysisComponent,
    BubbleChartComponent,
    TraitAnalysisEducatorComparisonComponent,
    TabBarComponent,
  ],
  imports: [
    CommonModule,
    IonicModule,
    ReactiveFormsModule,
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
    MatSidenavModule,
    RouterModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    IsoDatePipe,
    QRCodeModule,
  ],
  exports: [
    PrimaryButtonComponent,
    InputFieldComponent,
    LoginPage,
    TotpPage,
    IconButtonComponent,
    IonicModule,
    TranslateModule,
    ReactiveFormsModule,
    InputWithHeadingComponent,
    DropdownWithHeadingComponent,
    UpsertFormInfoBoxComponent,
    TextareaWithHeadingComponent,
    NavigationButtonComponent,
    SideNavBarComponent,
    EmptyViewComponent,
    QuestionnaireTokenBoxComponent,
    DatepickerWithHeadingComponent,
    SurveyDetailInfoBoxComponent,
    UpsertFormInfoBoxComponent,
    EmptyViewComponent,
    NotFoundComponent,
    InactiveCopyableInputFieldComponent,
    ChildrenEvaluationBarChartComponent,
    ChildrenEvaluationFreeFormAnswersComponent,
    TaggingComponent,
    InputWithHeadingAndSuggestionsComponent,
    EvaluationDetailInfoBoxComponent,
    SingleBarChartComponent,
    SingleBarChartWithDescriptionComponent,
    MultiBarChartWithDescriptionComponent,
    BoxplotChartComponent,
    ChartLegendComponent,
    DoughnutChartComponent,
    ReportFrontPageComponent,
    MultiBarChartWithLinesComponent,
    SubFilterListTileComponent,
    AddUserPopUpComponent,
    DropdownComponent,
    BarChartComponent,
    TraitAnalysisTeamComponent,
    EvaluatorAnalysisTitleComponent,
    TraitAnalysisComponent,
    BubbleChartComponent,
    TraitAnalysisEducatorComparisonComponent,
    TabBarComponent,
    IsoDatePipe,
  ],
})
export class SharedModule {}
