<div class="input-container">
  <ion-card-title>
    <b>{{ 'spssFilter.add.user' | translate }}</b>
  </ion-card-title>

  <div class="paragraph" [innerHTML]="'spssFilter.add.user_description' | translate"></div>

  <app-input-with-heading
    formControlName="trackingId"
    type="text"
    [label]="'mobileAppV2.coreData.educatorForm.accountLabel' | translate"
    [setFocus]="true"
    [inputValue]="userForm.controls.trackingId.value"
    prefix="#ID-"
    (handleChange)="onChange($event)"
    [errorMessage]="errorMessage"
  >
  </app-input-with-heading>

  <ion-row class="flex-container">
    <app-primary-button
      expand="block"
      (onClick)="closePopup()"
      [isDisabled]="false"
      [isLoading]="false"
      [border]="true"
      [label]="'global.button.cancel' | translate"
    >
    </app-primary-button>
    <app-primary-button
      type="submit"
      expand="block"
      (onClick)="fetchUser()"
      [isDisabled]="false"
      [isLoading]="false"
      [label]="'mobileAppV2.generic.add' | translate"
      [buttonType]="buttonType.GRADIENT"
    >
    </app-primary-button>
  </ion-row>
</div>
