import { Component, Input } from '@angular/core';
import { LoadingController, ModalController } from '@ionic/angular';
import { PrimaryButtonType } from '../primary-button/primary-button.component';
import { Utils } from '../../../../utils/utils';
import { AdminService } from '../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { UntypedFormBuilder } from '@angular/forms';
import { SpssFilterDetailPage } from '../../../../pages/admin/spss-filter-detail/spss-filter-detail.page';

@Component({
  selector: 'app-add-user-pop-up-component',
  templateUrl: './add-user-pop-up.component.html',
  styleUrls: ['./add-user-pop-up.component.scss'],
})
export class AddUserPopUpComponent {
  constructor(
    private modalController: ModalController,
    private adminService: AdminService,
    public translate: TranslateService,
    public loadingController: LoadingController,
    private formBuilder: UntypedFormBuilder,
  ) {}
  @Input() parent: SpssFilterDetailPage;

  public loadingUi: HTMLIonLoadingElement = null;
  public loading = false;
  public errorMessage: string;
  public buttonType = PrimaryButtonType;

  public userForm = this.formBuilder.group({
    trackingId: [''],
  });

  protected readonly PrimaryButtonType = PrimaryButtonType;

  public onChange(value: string) {
    this.userForm.controls.trackingId.setValue(value);
  }

  async fetchUser(): Promise<void> {
    const trackingId = this.userForm.controls.trackingId.value;
    this.errorMessage = undefined;
    this.loading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getUserDataFromTrackingId(trackingId).subscribe((response) => {
      if (response.success) {
        this.parent.selectedUsers.find((user) => user.trackingId == response.data.trackingId) !==
        undefined
          ? (this.errorMessage = this.translate.instant('spssFilter.user.already_inserted'))
          : this.parent.selectedUsers.push(response.data);
      } else {
        this.errorMessage = this.translate.instant('spssFilter.user.not_found');
      }
      !this.errorMessage && this.closePopup();
      this.loading = false;
    });
    await this.loadingUi.dismiss();
  }

  closePopup() {
    this.modalController.dismiss();
  }
}
