<ion-label class="label-container">{{ label.toUpperCase() }}</ion-label>
<div class="input-container" [ngClass]="{ 'invalid': isError() }">
  <ion-select
    mode="md"
    (ionChange)="writeValue($event.detail.value)"
    [okText]="'global.alert.confirm.ok' | translate"
    [cancelText]="'global.alert.confirm.cancel' | translate"
    [interfaceOptions]="interfaceOptions"
    [value]="inputValue"
  >
    <ion-select-option *ngFor="let value of values" [value]="value">
      {{ value.toUpperCase() }}
    </ion-select-option>
  </ion-select>
</div>
<ng-container *ngIf="isError()">
  <div class="validation-box">
    <ion-text>
      {{ errorMessage | translate }}
    </ion-text>
  </div>
</ng-container>
