@import './../../../../../theme/variables_v2';

.input-container {
  border: 1px solid $color-light-gray;
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 16px;
  margin-top: 6px;

  ::ng-deep {
    ion-select {
      height: 56px;
      color: $color-primary-v2;
      border: none;
      margin: 0;
      background-color: transparent;
      padding-bottom: 10px;
    }
  }

  &.invalid {
    border-color: $color-danger;
  }
}

::ng-deep {
  .alert-radio-icon.sc-ion-alert-md {
    border-color: var(--ion-color-primary-V2);
  }

  [aria-checked='true'].sc-ion-alert-md .alert-radio-icon.sc-ion-alert-md {
    border-color: var(--ion-color-primary-V2);
  }

  .alert-radio-inner.sc-ion-alert-md {
    background-color: var(--ion-color-primary-V2);
  }

  .alert-button.sc-ion-alert-md {
    color: var(--ion-color-primary-V2);
    font-family: 'Open Sans', serif;
  }

  .alert-radio-label.sc-ion-alert-md {
    font-family: 'Open Sans', serif;
  }

  .alert-title.sc-ion-alert-md {
    font-family: 'Open Sans', serif;
  }

  .alert-wrapper.sc-ion-alert-md {
    border-radius: 12px;
  }
}

.label-container {
  color: var(--ion-color-dark-grey-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
}

.validation-box {
  padding-top: 6px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
  color: $color-danger;
}
