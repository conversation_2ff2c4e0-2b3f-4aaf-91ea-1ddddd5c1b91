import { Component, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-dropdown-with-heading',
  templateUrl: './dropdown-with-heading.component.html',
  styleUrls: ['./dropdown-with-heading.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: DropdownWithHeadingComponent,
      multi: true,
    },
  ],
})
export class DropdownWithHeadingComponent implements ControlValueAccessor {
  @Input() type: string;
  @Input() label = '';
  @Input() errorMessage?: string;
  @Input() values: string[];
  @Input() interfaceOptions: any;
  @Input() inputValue: string;

  private onChange = (value: string) => {};

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {}

  writeValue(value: string): void {
    this.onChange(value);
  }

  isError(): boolean {
    return this.errorMessage != null;
  }
}
