<div class="tab-bar">
  <div
    *ngFor="let tabName of tabNames; let i = index"
    class="tab"
    [class.active]="i === selectedTabIndex"
    [class.completed]="showContentStates && contentStates[i]"
    (click)="selectTab(i)"
  >
    {{ tabName | translate }}
    <app-svg-icon
      class="checkmark"
      *ngIf="showContentStates && contentStates[i]"
      width="12px"
      height="12px"
      icon="assets/svg/check_small.svg"
      [iconColor]="i === selectedTabIndex ? '#AAB3F4' : 'black'"
    ></app-svg-icon>
  </div>
</div>
