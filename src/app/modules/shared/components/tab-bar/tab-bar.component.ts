import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-tab-bar',
  templateUrl: './tab-bar.component.html',
  styleUrls: ['./tab-bar.component.scss'],
})
export class TabBarComponent {
  @Input() tabNames: string[] = [];
  @Input() contentStates: boolean[] = [];
  @Input() showContentStates = true;
  @Output() tabSelected = new EventEmitter<number>();
  selectedTabIndex = 0;

  selectTab(index: number): void {
    this.tabSelected.emit(index);
    this.selectedTabIndex = index;
  }
}
