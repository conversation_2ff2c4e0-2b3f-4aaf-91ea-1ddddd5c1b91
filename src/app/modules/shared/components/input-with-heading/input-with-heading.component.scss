@import './../../../../../theme/variables_v2';

.input-container {
  border: 1px solid $color-light-gray;
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 16px;
  margin-top: 6px;

  ::ng-deep {
    ion-input {
      height: 56px;
      color: $color-primary-v2;
      border: none;
      margin: 0;
      background-color: transparent;

      .input-highlight.sc-ion-input-md {
        display: none;
      }

      > input {
        --placeholder-color: var(--ion-color-placeholder-V2);
        --placeholder-opacity: 1;
        margin-bottom: 0;
      }
    }
  }

  &.invalid {
    border-color: $color-danger;
  }
}

.label-container {
  color: var(--ion-color-dark-grey-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
}

.validation-box {
  padding-top: 6px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
  color: $color-danger;
}

.flex-parent {
  display: flex;
}

.inline-prefix {
  flex: 1;
  display: flex;
  align-items: center;
  color: $color-hint-text-grey;
}

.inline-content {
  flex: 18;
}
