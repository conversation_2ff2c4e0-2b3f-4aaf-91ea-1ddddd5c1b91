import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-input-with-heading',
  templateUrl: './input-with-heading.component.html',
  styleUrls: ['./input-with-heading.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: InputWithHeadingComponent,
      multi: true,
    },
  ],
})
export class InputWithHeadingComponent implements ControlValueAccessor, OnInit {
  @ViewChild('textInput', { read: ElementRef, static: true }) textInput: ElementRef;

  @Input() type: string;
  @Input() placeholder: string;
  @Input() label?: string;
  @Input() errorMessage?: string;
  @Input() inputValue: string;
  @Input() disabled = false;
  @Input() setFocus = false;
  @Input() prefix?: string;
  @Input() maxLength?: number;
  @Output() handleChange?: EventEmitter<string> = new EventEmitter<string>();

  ngOnInit(): void {
    if (!this.disabled && this.inputValue === '' && this.setFocus) {
      setTimeout(() => {
        this.textInput.nativeElement.setFocus(true);
      }, 300);
    }
  }

  constructor(private renderer: Renderer2) {}

  private onChange = (value: string) => {
    this.handleChange.emit(value);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
  registerOnTouched(fn: any): void {}

  writeValue(value: string): void {
    this.onChange(value);
  }

  setDisabledState?(isDisabled: boolean): void {
    this.renderer.setProperty(this.textInput.nativeElement, 'disabled', isDisabled);
  }

  isError(): boolean {
    return this.errorMessage != null;
  }
}
