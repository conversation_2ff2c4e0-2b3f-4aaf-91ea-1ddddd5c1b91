<ion-label *ngIf="label" class="label-container">{{ label.toUpperCase() }}</ion-label>
<div class="input-container" [ngClass]="{ 'invalid': isError() }">
  <span class="flex-parent">
    <span *ngIf="prefix" class="inline-prefix">{{ prefix }}</span>
    <ion-input
      class="inline-content"
      #textInput
      [type]="type"
      [maxlength]="maxLength"
      [placeholder]="placeholder"
      [clearOnEdit]="false"
      [value]="inputValue"
      [disabled]="disabled"
      (ionChange)="writeValue($event.detail.value)"
    >
    </ion-input>
  </span>
</div>
<ng-container *ngIf="isError()">
  <div class="validation-box">
    <ion-text>
      {{ errorMessage | translate }}
    </ion-text>
  </div>
</ng-container>
