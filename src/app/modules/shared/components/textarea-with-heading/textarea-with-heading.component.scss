@import './../../../../../theme/variables_v2';

.input-container {
  border: 1px solid $color-light-gray;
  border-radius: 8px;
  margin-top: 6px;

  ::ng-deep {
    ion-textarea {
      border: none;
      margin: 0;
      color: $color-primary-v2;
      padding: 0;

      .textarea-highlight.sc-ion-textarea-md {
        display: none;
      }
    }

    > textarea {
      --placeholder-color: var(--ion-color-placeholder-V2);
      --placeholder-opacity: 1;
      margin-bottom: 0;
    }

    .native-textarea {
      padding: 16px !important;
      --placeholder-color: var(--ion-color-placeholder-V2);
    }
  }

  &.invalid {
    border-color: $color-danger;
  }
}

.label-container {
  color: var(--ion-color-dark-grey-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
}

.validation-box {
  padding-top: 6px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
  color: $color-danger;
}
