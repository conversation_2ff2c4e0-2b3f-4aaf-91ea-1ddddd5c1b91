<ion-label class="label-container">{{ label.toUpperCase() }}</ion-label>
<div class="input-container" [ngClass]="{ 'invalid': isError() }">
  <ion-textarea
    #textarea
    [placeholder]="placeholder"
    [clearOnEdit]="false"
    [value]="inputValue"
    (ionChange)="writeValue($event.detail.value)"
    [disabled]="disabled"
    [rows]="rows"
  >
  </ion-textarea>
</div>
<ng-container *ngIf="isError()">
  <div class="validation-box">
    <ion-text>
      {{ errorMessage | translate }}
    </ion-text>
  </div>
</ng-container>
