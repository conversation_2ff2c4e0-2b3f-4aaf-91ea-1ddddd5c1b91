import { Component, ElementRef, Input, OnInit, Renderer2, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-textarea-with-heading',
  templateUrl: './textarea-with-heading.component.html',
  styleUrls: ['./textarea-with-heading.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TextareaWithHeadingComponent,
      multi: true,
    },
  ],
})
export class TextareaWithHeadingComponent implements ControlValueAccessor, OnInit {
  @ViewChild('textarea', { read: ElementRef, static: true }) textInput: ElementRef;

  @Input() placeholder: string;
  @Input() label = '';
  @Input() errorMessage?: string;
  @Input() inputValue: string;
  @Input() disabled = false;
  @Input() setFocus = false;
  @Input() rows: number;

  ngOnInit(): void {
    if (!this.disabled && this.inputValue === '' && this.setFocus) {
      setTimeout(() => {
        this.textInput.nativeElement.setFocus(true);
      }, 300);
    }
  }

  constructor(private renderer: Renderer2) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private onChange = (value: string) => {};

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
  registerOnTouched(fn: any): void {}

  writeValue(value: string): void {
    this.onChange(value);
  }

  setDisabledState?(isDisabled: boolean): void {
    this.renderer.setProperty(this.textInput.nativeElement, 'disabled', isDisabled);
  }

  isError(): boolean {
    return this.errorMessage != null;
  }
}
