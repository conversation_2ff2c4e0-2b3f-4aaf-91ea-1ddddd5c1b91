<ion-grid *ngIf="!isLoading">
  <ion-row>
    <ion-col class="ion-no-padding">
      <div class="info-section">
        <div class="horizontal-spacer"></div>
        <ion-grid>
          <ion-row>
            <ion-col size="8">
              <ion-text class="section-title">
                {{ 'organization.title' | translate }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text class="section-title">
                {{ 'surveyDetail.sectionTitle.id' | translate }}
              </ion-text>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col size="8">
              <ion-text class="bold">
                {{ details.organizationName }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text>
                {{ details.customerServiceId }}
              </ion-text>
            </ion-col>
          </ion-row>
          <div class="horizontal-spacer"></div>
          <ion-row>
            <ion-col size="8">
              <ion-text class="section-title">
                {{ 'organization.location.title' | translate }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text class="section-title">
                {{ 'organization.group.title' | translate }}
              </ion-text>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col size="8">
              <ion-text>
                {{ details.locationName }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text>
                {{ details.groupName }}
              </ion-text>
            </ion-col>
          </ion-row>
          <div class="horizontal-spacer"></div>
          <ion-row>
            <ion-col>
              <ion-text class="section-title">
                {{ 'organization.evaluation-detail.evaluator' | translate }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text class="section-title">
                {{ getNumberOfParticipantsTitle() }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text class="section-title">
                {{ 'organization.evaluation-detail.updated_at' | translate }}
              </ion-text>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-text>
                {{ details.evaluator }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text>
                {{ details.numberOfParticipants }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text>
                {{ details.uploadedAt | isoDate: translate.currentLang }}
              </ion-text>
            </ion-col>
          </ion-row>
        </ion-grid>
        <div class="horizontal-spacer"></div>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
