import { Component, Input, OnInit } from '@angular/core';
import { LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import {
  AdminService,
  OrganizationEvaluationDetail,
} from '../../../../services/admin/admin.service';
import { Utils } from '../../../../utils/utils';
import { EvaluationType } from '../../../organization/organizations/utils';

@Component({
  selector: 'app-evaluation-detail-info-box',
  templateUrl: './evaluation-detail-info-box.component.html',
  styleUrls: [
    '../upsert-form-info-box/upsert-form-info-box.component.scss',
    './evaluation-detail-info-box.component.scss',
  ],
})
export class EvaluationDetailInfoBoxComponent implements OnInit {
  @Input() evaluationId: string;
  @Input() evaluationType: EvaluationType;

  public details: OrganizationEvaluationDetail;
  public isLoading = true;
  private loadingUi: HTMLIonLoadingElement = null;

  constructor(
    public translate: TranslateService,
    private adminService: AdminService,
    private toastController: ToastController,
    private navController: NavController,
    private loadingController: LoadingController,
  ) {}

  ngOnInit(): void {
    this.fetchEvaluationDetail();
  }

  private async fetchEvaluationDetail(): Promise<void> {
    await this.presentLoadingUi();
    this.adminService
      .getOrganizationEvaluationDetail(this.evaluationId, this.evaluationType)
      .subscribe(async (result) => {
        if (result.success) {
          this.details = result.data;
        } else {
          await this.showErrorMessage(true);
        }
        await this.dismissLoadingUi();
      });
  }

  getNumberOfParticipantsTitle(): string {
    switch (this.evaluationType) {
      case EvaluationType.CHILD_EVALUATION:
        return this.translate.instant('organization.evaluation-detail.number-of-children');
      case EvaluationType.EVALUATION:
        return this.translate.instant('organization.evaluation-detail.number-of-educators');
    }
  }

  async showErrorMessage(shouldReturn: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('global.error.generic'),
      color: 'danger',
      position: 'bottom',
      duration: 10000,
      cssClass: 'global-toast',
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }

  navigateBack(): void {
    this.navController.navigateRoot('admin/organizations');
  }

  private async presentLoadingUi(): Promise<void> {
    this.isLoading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
  }

  private async dismissLoadingUi(): Promise<void> {
    this.isLoading = false;
    await this.loadingUi.dismiss();
  }
}
