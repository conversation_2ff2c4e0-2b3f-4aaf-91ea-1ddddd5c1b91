import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SurveyState } from '../../../../services/admin/admin.service';
import { SurveyType } from '../../../../services/jwt/survey-token.service';

@Component({
  selector: 'app-survey-detail-info-box',
  templateUrl: './survey-detail-info-box.component.html',
  styleUrls: [
    '../upsert-form-info-box/upsert-form-info-box.component.scss',
    './survey-detail-info-box.component.scss',
  ],
})
export class SurveyDetailInfoBoxComponent {
  // todo: rename
  @Input() surveyAccessId: string;
  @Input() organization: string;
  @Input() location: string;
  @Input() group: string;
  @Input() createdAt: string;
  @Input() tokenRegeneratedAt: string;
  @Input() availableAnswers: string;
  @Input() surveyState: SurveyState;
  @Input() surveyType: SurveyType;

  constructor(public translate: TranslateService) {}

  isLocation(): boolean {
    return this.surveyType !== SurveyType.ORGANIZATION_SURVEY;
  }

  isGroup(): boolean {
    return this.surveyType === SurveyType.STAFF_SURVEY;
  }

  isLocationOrGroup(): boolean {
    return this.isLocation() || this.isGroup();
  }
}
