<ion-grid>
  <ion-row>
    <ion-col class="ion-no-padding">
      <div class="info-section">
        <div class="horizontal-spacer"></div>
        <ion-grid>
          <ion-row>
            <ion-col size="8">
              <ion-text class="section-title">
                {{ 'organization.title' | translate }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text class="section-title">
                {{ 'surveyDetail.sectionTitle.id' | translate }}
              </ion-text>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col size="8">
              <ion-text class="bold">
                {{ organization }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text>
                {{ surveyAccessId }}
              </ion-text>
            </ion-col>
          </ion-row>
          <div class="horizontal-spacer"></div>
          <ion-row *ngIf="isLocationOrGroup()">
            <ion-col size="8">
              <ion-text class="section-title" *ngIf="isLocation()">
                {{ 'organization.location.title' | translate }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text class="section-title" *ngIf="isGroup()">
                {{ 'organization.group.title' | translate }}
              </ion-text>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="isLocationOrGroup()">
            <ion-col size="8">
              <ion-text *ngIf="isLocation()">
                {{ location }}
              </ion-text>
            </ion-col>
            <ion-col size="4">
              <ion-text *ngIf="isGroup()">
                {{ group }}
              </ion-text>
            </ion-col>
          </ion-row>
          <div class="horizontal-spacer" *ngIf="isLocationOrGroup()"></div>
          <ion-row>
            <ion-col>
              <ion-text class="section-title">
                {{ 'surveyDetail.sectionTitle.created' | translate }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text class="section-title">
                {{ 'surveyDetail.sectionTitle.renewed' | translate }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text class="section-title">
                {{ 'surveyDetail.sectionTitle.numberOfAnswers' | translate }}
              </ion-text>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-text>
                {{ createdAt | isoDate: translate.currentLang }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text>
                {{ tokenRegeneratedAt | isoDate: translate.currentLang }}
              </ion-text>
            </ion-col>
            <ion-col>
              <ion-text>
                {{ availableAnswers }}
              </ion-text>
            </ion-col>
          </ion-row>
        </ion-grid>
        <div class="horizontal-spacer"></div>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
