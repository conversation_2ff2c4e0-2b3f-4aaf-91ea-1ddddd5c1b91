<ion-label class="label-container">{{ 'surveyToken.link.label' | translate }}</ion-label>
<ion-grid>
  <ion-row>
    <div class="input-container">
      <ion-grid>
        <ion-row class="ion-activatable ripple-parent" (click)="copyValueToClipboard()">
          <ion-input [readonly]="true" [value]="getLink()"> </ion-input>
          <ion-img class="copy-icon" src="assets/svg/copy.svg"></ion-img>
          <ion-ripple-effect></ion-ripple-effect>
        </ion-row>
      </ion-grid>
    </div>
    <app-icon-button
      class="icon-button"
      (click)="refresh()"
      [isLoading]="isSurveyTokenLoading"
      [iconPath]="'assets/svg/renew.svg'"
    >
    </app-icon-button>
  </ion-row>
</ion-grid>
