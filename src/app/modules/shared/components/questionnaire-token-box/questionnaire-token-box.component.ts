import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SurveyTokenService } from '../../../../services/jwt/survey-token.service';
import { Utils } from '../../../../utils/utils';
import { ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-questionnaire-token-box',
  templateUrl: './questionnaire-token-box.component.html',
  styleUrls: ['./questionnaire-token-box.component.scss'],
})
export class QuestionnaireTokenBoxComponent {
  @Input() token: string;
  @Input() isSurveyTokenLoading: boolean;
  @Output() onRefresh: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    public surveyTokenService: SurveyTokenService,
    public toastController: ToastController,
    public translate: TranslateService,
  ) {}

  getLink(): string {
    return this.surveyTokenService.getLinkToQuestionnaire(this.token);
  }

  refresh(): void {
    this.onRefresh.emit();
  }

  copyValueToClipboard(): void {
    Utils.copyToClipboard(this.getLink());
    this.showSuccessMessage();
  }

  async showSuccessMessage(): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('surveyToken.copied'),
      color: 'success',
      position: 'bottom',
      duration: 5000,
      cssClass: 'global-toast',
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }
}
