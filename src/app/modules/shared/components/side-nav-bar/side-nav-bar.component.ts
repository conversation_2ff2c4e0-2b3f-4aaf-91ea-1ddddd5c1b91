import { Component, ViewChild } from '@angular/core';
import { Mat<PERSON>idenav } from '@angular/material/sidenav';
import { UserService } from '../../../../services/user/user.service';
import { NavController } from '@ionic/angular';

@Component({
  selector: 'app-side-nav-bar',
  templateUrl: './side-nav-bar.component.html',
  styleUrls: ['./side-nav-bar.component.scss'],
})
export class SideNavBarComponent {
  @ViewChild(MatSidenav, { static: false }) public sidenav: MatSidenav;

  constructor(
    private userService: UserService,
    private navController: NavController,
  ) {}

  logout(): void {
    this.userService.logout().subscribe((resp) => {
      this.navigateLogin();
    });
  }

  navigateLogin(): void {
    this.navController.navigateRoot('/login');
  }
}
