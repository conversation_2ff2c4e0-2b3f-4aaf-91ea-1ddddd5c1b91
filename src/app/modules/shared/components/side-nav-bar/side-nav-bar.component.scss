@import 'src/theme/_variables_v2.scss';

.logo-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 36px;

  ::ng-deep ion-img {
    height: 50px;
    margin-right: 8px;
    width: 157px;
  }
}

.sidebar {
  background: $color-primary-v2;
  padding: 50px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 0;
  height: 100%;
}

mat-sidenav-container {
  height: 100%;
}

mat-sidenav {
  width: 296px;
}

.logout-button {
  display: flex;
  color: white;
  align-items: center;
  padding-bottom: 8px;
  padding-top: 8px;
  padding-left: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  cursor: pointer;

  .icon {
    padding-right: 16px;
    padding-top: 4px;
  }
}

.mat-drawer-content::-webkit-scrollbar {
  display: none;
}
