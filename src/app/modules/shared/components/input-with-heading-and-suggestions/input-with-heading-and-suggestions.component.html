<ion-label *ngIf="label !== ''" class="label">{{ label.toUpperCase() }}</ion-label>
<div class="input-container">
  <img *ngIf="iconPath !== null" src="{{ iconPath }}" class="prefix-icon" alt="" />
  <input
    autocomplete="off"
    id="inputElement"
    list="tags"
    [type]="type"
    [placeholder]="placeholder"
    (keyup)="onChange()"
    (change)="onEnter()"
  />
  <datalist id="tags" class="dropdown">
    <option *ngFor="let option of suggestions" [value]="option"></option>
  </datalist>
</div>
