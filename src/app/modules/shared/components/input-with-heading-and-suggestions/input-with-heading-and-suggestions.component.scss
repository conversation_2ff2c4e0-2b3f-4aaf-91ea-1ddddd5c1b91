@import './../../../../../theme/variables_v2';

.input-container {
  display: flex;
  border: 1px solid $color-light-gray;
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 16px;
  margin-top: 6px;

  ::ng-deep {
    input {
      height: 56px;
      color: $color-primary-v2;
      width: 100%;
      border: none;
      margin: 0;
      background-color: transparent;

      .input-highlight.sc-ion-input-md {
        display: none;
      }

      > input {
        margin-bottom: 0;
      }
    }

    input::placeholder {
      color: $color-hint-text-grey;
    }

    input::-webkit-input-placeholder {
      /* Edge */
      color: $color-hint-text-grey;
    }

    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: $color-hint-text-grey;
    }

    input:-moz-placeholder {
      color: $color-hint-text-grey;
      opacity: 1;
    }

    input::-moz-placeholder {
      color: $color-hint-text-grey;
      opacity: 1;
    }

    input:focus {
      outline: none;
    }
  }
}

.label {
  color: var(--ion-color-dark-grey-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
}

.prefix-icon {
  padding-right: 8px;
}

.dropdown {
  width: 100%;
  background-color: white;
  color: $color-primary-v2;
}
