import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-input-with-heading-and-suggestions',
  templateUrl: './input-with-heading-and-suggestions.component.html',
  styleUrls: ['./input-with-heading-and-suggestions.component.scss'],
})
export class InputWithHeadingAndSuggestionsComponent {
  @Input() type: string;
  @Input() placeholder = '';
  @Input() label = '';
  @Input() suggestions: string[] = [];
  @Input() iconPath: string = null;

  @Output() inputChanged = new EventEmitter<string>();
  @Output() submit = new EventEmitter<string>();

  public value: string;

  onChange(): void {
    const inputElement = document.getElementById('inputElement') as HTMLInputElement;
    this.inputChanged.emit(inputElement.value);
  }

  onEnter(): void {
    const inputElement = document.getElementById('inputElement') as HTMLInputElement;
    this.submit.emit(inputElement.value);
    inputElement.value = '';
  }
}
