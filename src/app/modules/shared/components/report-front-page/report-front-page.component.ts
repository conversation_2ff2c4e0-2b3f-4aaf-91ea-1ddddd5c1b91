import { Component, Input, OnInit } from '@angular/core';
import {
  AggregatedChildEvaluations,
  SurveysAndResults,
} from '../../../../services/admin/admin.service';
import { TranslateService } from '@ngx-translate/core';
import { SurveyType } from '../../../../services/jwt/survey-token.service';
import { Evaluation } from '../../../../services/evaluation/models/evaluation';
import moment from 'moment';

@Component({
  selector: 'app-report-front-page',
  templateUrl: './report-front-page.component.html',
  styleUrls: ['./report-front-page.component.scss'],
})
export class ReportFrontPageComponent implements OnInit {
  @Input() subTitleKey: string;
  @Input() organizationName: string;
  @Input() surveysMinDate: Date;
  @Input() surveysMaxDate: Date;
  @Input() surveyResults: SurveysAndResults;
  @Input() childEvaluations?: Array<AggregatedChildEvaluations> | undefined;
  @Input() evaluation?: Array<Evaluation> | undefined;

  public listEntries: ListEntry[] = [];
  private separator = '  ・  ';

  constructor(public translate: TranslateService) {}

  ngOnInit(): void {
    this.initSurveyListEntry(
      SurveyType.ORGANIZATION_SURVEY,
      'reports.front_page.organization_survey',
    );
    this.initSurveyListEntry(
      SurveyType.ADMINISTRATION_SURVEY,
      'reports.front_page.administration_survey',
    );
    this.initSurveyListEntry(SurveyType.STAFF_SURVEY, 'reports.front_page.staff_survey');
    this.initSurveyListEntry(SurveyType.PARENTAL_SURVEY, 'reports.front_page.parental_survey');
    this.initEvaluationListEntry();
    this.initChildEvaluationListEntry();
  }

  getEvaluationDuration(): string {
    const format = (date: Date) => moment(date).format('MMMM YYYY');
    return this.translate.instant('reports.front_page.evaluation_duration', {
      surveysMinDate: format(this.surveysMinDate),
      surveysMaxDate: format(this.surveysMaxDate),
    });
  }

  initSurveyListEntry(surveyType: SurveyType, surveyTypeTranslationKey: string): void {
    let numberOfSurveyAnswers = 0;
    let displayString: string;
    switch (surveyType) {
      case SurveyType.ADMINISTRATION_SURVEY: {
        numberOfSurveyAnswers = this.surveyResults.administrationSurveyResults.length;
        break;
      }
      case SurveyType.ORGANIZATION_SURVEY: {
        numberOfSurveyAnswers = this.surveyResults.organizationSurveyResults.length;
        break;
      }
      case SurveyType.PARENTAL_SURVEY: {
        numberOfSurveyAnswers = this.surveyResults.parentalSurveyResults.length;
        break;
      }
      case SurveyType.STAFF_SURVEY: {
        numberOfSurveyAnswers = this.surveyResults.staffSurveyResults.length;
        break;
      }
    }

    const surveysOfType = this.surveyResults.surveys.filter(
      (it) => SurveyType[it.type] === surveyType,
    );
    const surveyTypeTranslation = this.translate.instant(surveyTypeTranslationKey);

    const nrOfSurveysTranslation = this.translate.instant('reports.front_page.number_of_surveys', {
      numberOfSurveys: surveysOfType.length,
    });

    const nrOfAnswersTranslation = this.translate.instant('reports.front_page.number_of_answers', {
      numberOfAnswers: numberOfSurveyAnswers,
    });

    if (numberOfSurveyAnswers === 0 || surveyType === SurveyType.ORGANIZATION_SURVEY) {
      displayString = surveyTypeTranslation;
    } else {
      displayString =
        surveyTypeTranslation +
        this.separator +
        nrOfSurveysTranslation +
        this.separator +
        nrOfAnswersTranslation;
    }

    this.listEntries.push({ hasData: numberOfSurveyAnswers > 0, data: displayString });
  }

  initEvaluationListEntry(): void {
    this.listEntries.push({
      hasData: this.evaluation !== undefined,
      data: this.translate.instant('reports.front_page.evaluation'),
    });
  }

  initChildEvaluationListEntry(): void {
    const nrOfEvaluations =
      this.childEvaluations === undefined
        ? 0
        : this.childEvaluations.reduce((sum, current) => sum + current.numberOfChildEvaluations, 0);

    const nrOfChildren =
      this.childEvaluations === undefined
        ? 0
        : this.childEvaluations.reduce(
            (sum, current) =>
              sum +
              (current.aggregatedChildEvaluation !== null
                ? current.aggregatedChildEvaluation.numberOfEvaluatedChildren
                : 0),
            0,
          );
    const nrOfChildEvalTranslation = this.translate.instant(
      'reports.front_page.number_of_child_evaluations',
      {
        nrOfChildEvaluations: nrOfEvaluations,
      },
    );
    const nrOfChildrenTranslation = this.translate.instant(
      'reports.front_page.number_of_children',
      {
        nrOfChildren,
      },
    );

    const displayString =
      nrOfEvaluations > 0
        ? this.translate.instant('reports.front_page.child_evaluation') +
          this.separator +
          nrOfChildEvalTranslation +
          this.separator +
          nrOfChildrenTranslation
        : this.translate.instant('reports.front_page.child_evaluation');

    this.listEntries.push({ hasData: nrOfEvaluations > 0, data: displayString });
  }
}

interface ListEntry {
  hasData: boolean;
  data: string;
}
