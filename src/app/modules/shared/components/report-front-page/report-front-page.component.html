<div>
  <div class="title">{{ 'reports.organization.title' | translate }}</div>
  <div class="sub-title">{{ subTitleKey | translate }}</div>
  <div class="heading-underline"></div>
</div>

<div class="front-page-evaluation-information-container">
  <div class="organization-name">{{ organizationName }}</div>
  <div class="evaluation-duration">{{ getEvaluationDuration() }}</div>
</div>

<div
  *ngFor="let item of listEntries"
  style="display: flex; flex-direction: row; align-items: center"
>
  <img
    class="front-page-list-entry-icon"
    [src]="item.hasData ? '/assets/svg/complete_dark.svg' : '/assets/svg/ic_cancel.svg'"
    alt="complete"
  />
  <div class="front-page-list-entry">{{ item.data }}</div>
</div>
