<div class="input-container design-v2">
  <ion-card-title>
    <b>{{ 'organization.new-organization-report-dialog.title' | translate }}</b>
  </ion-card-title>

  <div
    class="paragraph"
    [innerHTML]="'organization.new-organization-report-dialog.text' | translate"
  ></div>

  <div class="list-scroll-container disable-scrollbar">
    <ion-list *ngFor="let language of languages" lines="none">
      <ion-item
        class="language-item"
        [ngClass]="{ 'selected-language': language === selectedLanguage }"
        (click)="selectLanguage(language)"
      >
        <ion-text>
          {{ getLanguageTranslationKey(language) | translate }}
        </ion-text>
        <div *ngIf="language === selectedLanguage" #selectedIconDiv class="w-100 icon-container">
          <img src="assets/svg/check.svg" class="check-icon" />
        </div>
      </ion-item>
    </ion-list>
  </div>

  <ion-row class="flex-container">
    <app-primary-button
      expand="block"
      (onClick)="closePopup()"
      [isDisabled]="false"
      [isLoading]="false"
      [border]="true"
      [label]="'global.button.cancel' | translate"
    >
    </app-primary-button>
    <app-primary-button
      type="submit"
      expand="block"
      (onClick)="submitPopup(selectedLanguage)"
      [isDisabled]="false"
      [isLoading]="false"
      [label]="'global.create' | translate"
      [buttonType]="buttonType.GRADIENT"
    >
    </app-primary-button>
  </ion-row>
</div>
