@import './../../../../../theme/variables_v2';

* {
  color: var(--ion-color-primary-V2);
}

.input-container {
  border-radius: 8px;
  border: 1px solid $color-light-gray;
  padding: 28px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.flex-container {
  display: flex;
  flex-direction: row;
  flex: 1;
  justify-content: space-between;
  padding-top: 42px;
}

.flex-container > * + * {
  margin-left: 30px;
}

.paragraph {
  margin-top: 4%;
  margin-bottom: 4%;
}

.language-item {
  border: 2px solid var(--ion-color-light-grey-V2);
  border-radius: 8px;
  cursor: pointer;

  &.selected-language {
    border: 2px solid var(--ion-color-primary-V2);
  }
}

.list-scroll-container {
  overflow-y: scroll;
  height: 250px;
}

.icon-container {
  display: flex;
  align-items: center;
}

.check-icon {
  width: 16px;
  margin-left: auto;
}
