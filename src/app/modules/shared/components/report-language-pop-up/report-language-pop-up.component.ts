import { Component, ElementRef, Input, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import {
  getLanguageFromCode,
  getLanguageTranslationKey,
  Languages,
} from '../../../../utils/language-helper';
import { PrimaryButtonType } from '../primary-button/primary-button.component';

@Component({
  selector: 'app-report-language-pop-up',
  templateUrl: './report-language-pop-up.component.html',
  styleUrls: ['./report-language-pop-up.component.scss'],
})
export class ReportLanguagePopUpComponent {
  @Input() callbackFunction: (language: Languages) => void;
  @Input() languages: Languages[];
  @ViewChild('selectedIconDiv', { static: false }) selectedIconDiv: ElementRef;

  getLanguageTranslationKey = getLanguageTranslationKey;
  buttonType = PrimaryButtonType;

  selectedLanguage: Languages = getLanguageFromCode(this.translate.currentLang);

  constructor(
    private modalController: ModalController,
    public translate: TranslateService,
  ) {}

  ionViewDidEnter(): void {
    this.selectedIconDiv.nativeElement.parentNode.scrollIntoView({
      block: 'center',
      behavior: 'smooth',
    });
  }

  selectLanguage(language: Languages): void {
    this.selectedLanguage = language;
  }

  closePopup(): void {
    this.modalController.dismiss();
  }

  submitPopup(language: Languages): void {
    this.modalController.dismiss();
    this.callbackFunction(language);
  }
}
