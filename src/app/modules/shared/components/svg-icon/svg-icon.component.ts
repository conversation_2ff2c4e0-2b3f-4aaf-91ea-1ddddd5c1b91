import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-svg-icon',
  templateUrl: './svg-icon.component.html',
})
/* This Component is used to enable changing colors of an svg file.
 * This enables theming of images without the need of adding new svg files.
 * The svg must have an id, otherwise it cannot be found by the
 * 'use' element. The id must be passed at the end of the filepath,
 * like in the following example:
 * 'assets/icons/my_svg.svg#elementId
 * */
export class SvgIconComponent {
  @Input() icon: string;
  @Input() iconColor: string;
  @Input() width = '100%';
  @Input() height = '100%';
}
