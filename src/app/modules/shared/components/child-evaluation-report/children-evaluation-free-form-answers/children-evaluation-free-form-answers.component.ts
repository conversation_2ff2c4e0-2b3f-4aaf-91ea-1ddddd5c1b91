import { Component, Input } from '@angular/core';
import { ChildrenEvaluationFreeFormAnswers } from '../../../../reports/children-evaluation-report/models/children-evaluation-report';
import { ChildEvaluationReportUtil } from '../child-evaluation-report-util';

@Component({
  selector: 'app-children-evaluation-free-form-answers',
  templateUrl: './children-evaluation-free-form-answers.component.html',
  styleUrls: ['./children-evaluation-free-form-answers.component.scss'],
})
export class ChildrenEvaluationFreeFormAnswersComponent {
  @Input() freeFormAnswers: ChildrenEvaluationFreeFormAnswers;

  constructor(private childEvalReportUtil: ChildEvaluationReportUtil) {}

  public get questionTitle(): string {
    // tslint:disable-next-line:max-line-length
    return `"${this.childEvalReportUtil.getQuestionTitleFromIdAndCodeBookKey(this.freeFormAnswers.questionId, this.freeFormAnswers.codeBookKey)}"`;
  }
}
