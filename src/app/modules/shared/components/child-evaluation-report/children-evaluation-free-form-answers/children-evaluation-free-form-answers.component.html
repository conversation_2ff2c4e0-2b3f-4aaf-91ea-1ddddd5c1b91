<div>
  <h2 class="question-title">{{ questionTitle }}</h2>
  <div [class]="'underline-' + freeFormAnswers.questionId"></div>
</div>
<div *ngIf="freeFormAnswers.answers.length !== 0">
  <div *ngFor="let answer of freeFormAnswers.answers" class="free-form-answer-list-item">
    • {{ answer }}
  </div>
</div>
<div
  *ngIf="freeFormAnswers.answers.length === 0"
  class="free-form-answer-list-item no-answer-given"
>
  {{ 'childrenEvaluation.report.freeFormQuestion.noAnswerGiven' | translate }}
</div>
