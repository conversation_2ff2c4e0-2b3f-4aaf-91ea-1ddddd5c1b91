<div>
  <h1>{{ index }} {{ topic }}</h1>
  <div>{{ introduction }}</div>
</div>
<div>
  <h2 class="question-title">{{ questionTitle }}</h2>
  <div [class]="'underline-' + answers.id"></div>
</div>
<div *ngIf="illustrationTitle">
  <h3>{{ illustrationTitle }}</h3>
</div>
<div [hidden]="isChartHidden" #chartContainer class="child-eval-bar-chart-container">
  <canvas #barCanvas></canvas>
</div>
<div #imgWrap class="child-eval-bar-chart-image-container"></div>

<div *ngIf="legendConfiguration">
  <div class="spacing-large"></div>
  <app-chart-legend [configuration]="legendConfiguration"></app-chart-legend>
  <div class="spacing-large"></div>
</div>

<app-children-evaluation-bar-chart-explanation
  [childEvaluationQuestionId]="answers.id"
  [agreeCompletelyShare]="getDistributionFor(childEvalAnswerOption.AGREE_COMPLETELY)"
  [agreeToModerateDegreeShare]="getDistributionFor(childEvalAnswerOption.AGREE_TO_MODERATE_DEGREE)"
  [disagreeToModerateDegreeShare]="
    getDistributionFor(childEvalAnswerOption.DISAGREE_TO_MODERATE_DEGREE)
  "
  [disagreeCompletelyShare]="getDistributionFor(childEvalAnswerOption.DISAGREE_COMPLETELY)"
></app-children-evaluation-bar-chart-explanation>
