import { ReportType } from '../../../../organization/organizations/models';
import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Chart } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { ChildEvaluationV1ReportColor } from '../child-evaluation-report-color-scheme';
import {
  ChildrenSingleChoiceAnswerAccumulated,
  ChildrenSingleChoiceAnswerDistribution,
} from '../../../../reports/children-evaluation-report/models/children-evaluation-report';
import { ChildEvaluationReportUtil } from '../child-evaluation-report-util';
import { TranslateService } from '@ngx-translate/core';
import { LegendEntry } from '../../charts/chart-legend/chart-legend.component';
import { DataSetModel, DataSetsModel } from '../../charts/report-chart-models';

@Component({
  selector: 'app-children-evaluation-bar-chart',
  templateUrl: './children-evaluation-bar-chart.component.html',
  styleUrls: ['./children-evaluation-bar-chart.component.scss'],
})
export class ChildrenEvaluationBarChartComponent implements OnInit, AfterViewInit {
  @Input() answers: ChildrenSingleChoiceAnswerAccumulated;
  @Input() answersToCompareWith?: ChildrenSingleChoiceAnswerAccumulated | null;
  @Input() index: number;
  @Input() reportType: ReportType;
  @Input() illustrationIndexOffset?: number | null;
  @Input() setAPeriod?: string | undefined;
  @Input() setBPeriod?: string | undefined;

  @ViewChild('barCanvas', { static: true }) barCanvas: ElementRef;
  @ViewChild('imgWrap', { static: false }) imgWrap: ElementRef;
  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef;

  private barChart: Chart;

  private singleBarWidth: number;
  private maxBarThickness: number;
  private categoryPercentage: number;
  private barPercentage: number;
  private labelHeight: number;
  private gridColor = '#dbdbdb';
  private chartLabelFontSize: number;
  private comparisonColor = '#000000';
  public isChartHidden = false;
  public childEvalAnswerOption = ChildEvaluationAnswerOption;
  public ReportType = ReportType;
  public illustrationTitle: string = null;
  public legendConfiguration: LegendEntry[];

  constructor(
    private renderer: Renderer2,
    private childEvalReportUtil: ChildEvaluationReportUtil,
    private translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.init();
    this.initChartConfig();
    this.initLegendConfig();
  }

  private initChartConfig(): void {
    this.maxBarThickness = 45;
    this.categoryPercentage = 0.4;
    this.barPercentage = 0.8;
    this.singleBarWidth = 300;
    this.labelHeight = 0;
    this.chartLabelFontSize = this.reportType === ReportType.CHILDREN_EVALUATION_REPORT ? 18 : 13; // px
  }

  private init(): void {
    if (this.reportType === ReportType.COMPARISON_REPORT && this.illustrationIndexOffset !== null) {
      this.illustrationTitle = this.translate.instant('reports.survey.illustration', {
        illustrationNumber: this.index + this.illustrationIndexOffset,
      });
    }
  }

  private initLegendConfig(): void {
    if (this.setAPeriod && this.setBPeriod) {
      this.legendConfiguration = [
        {
          description: this.setAPeriod,
          color: this.comparisonColor,
        },
        {
          description: this.setBPeriod,
          color: this.childEvalReportUtil.getQuestionColorCode(this.answers.id),
        },
        {
          description: this.translate.instant('childrenEvaluation.report.barChartLabel.no-result'),
          color: ChildEvaluationV1ReportColor.disabled,
        },
      ];
    }
  }

  ngAfterViewInit(): void {
    this.renderChart();
  }

  renderChart(): void {
    const context = this;
    const imageClass = 'child-eval-bar-image-with-labels';
    const yAxisMax = this.getYAxisMaxValue();
    const yAxisRange = this.getYAxisRange(yAxisMax);

    // @ts-ignore
    this.barChart = new Chart(this.barCanvas.nativeElement, {
      plugins: [ChartDataLabels],
      type: 'bar',
      // @ts-ignore
      data: this.generateDataForBarChart(),
      options: {
        responsive: true,
        maintainAspectRatio: false,
        // @ts-ignore
        borderColor: this.gridColor,
        animation: {
          onComplete(): void {
            const barImage = this.toBase64Image();
            if (barImage !== 'data:,') {
              const img = context.renderer.createElement('img');
              context.renderer.addClass(img, imageClass);
              img.src = barImage;
              context.renderer.appendChild(context.imgWrap.nativeElement, img);
              context.isChartHidden = true;
            }
          },
        },
        legend: {
          display: false,
        },
        cornerRadius: 20,
        scales: {
          yAxes: [
            {
              ticks: {
                fontColor: this.gridColor,
                fontSize: this.chartLabelFontSize,
                padding: 20,
                min: 0,
                max: yAxisMax,
              },
              afterBuildTicks(): number[] {
                return yAxisRange;
              },
              gridLines: {
                drawBorder: true,
                color: this.gridColor,
                zeroLineColor: this.gridColor,
              },
            },
          ],
          xAxes: [
            {
              ticks: {
                fontColor: 'black',
                fontSize: this.chartLabelFontSize,
                padding: 20,
                maxRotation: 0,
                minRotation: 0,
                // @ts-ignore
                callback(label): string[] | string | number {
                  // Break line of word is too long
                  if (typeof label === 'string' && label.includes('\n')) {
                    return label.split('\n');
                  } else {
                    return label;
                  }
                },
              },
              gridLines: {
                drawBorder: true,
                display: true,
                zeroLineColor: this.gridColor,
              },
            },
          ],
        },
        layout: {
          padding: {
            left: 0,
            right: 0,
            top: this.labelHeight,
            bottom: 0,
          },
        },
        plugins: {
          datalabels: {
            align: 'top',
            color: 'black',
            rotation: -90,
            anchor: 'start',
            backgroundColor: null,
            borderColor: null,
            borderRadius: 4,
            borderWidth: 1,
            clamp: true,
            font: {
              size: 20,
              weight: 400,
            },
            // tslint:disable-next-line:no-shadowed-variable
            offset(context): number {
              return context.chart.chartArea.bottom - context.chart.chartArea.top + 15;
            },
            // tslint:disable-next-line:no-shadowed-variable
            formatter(value, context): number {
              // @ts-ignore
              return context.chart.data.datasets[context.datasetIndex].labels[context.dataIndex];
            },
          },
        },
      },
    });
  }

  public get questionTitle(): string {
    return `"${this.childEvalReportUtil.getQuestionTitleFromIdAndCodeBookKey(this.answers.id, this.answers.codeBookKey)}"`;
  }

  public get topic(): string {
    return this.translate.instant(`childrenEvaluation.report.${this.answers.id}.topic`);
  }

  public get introduction(): string {
    return this.translate.instant(`childrenEvaluation.report.${this.answers.id}.introduction`);
  }

  generateDataForBarChart(): DataSetsModel {
    const labelData = this.generateChartLabels();
    const data = this.getDataSetModel();

    return {
      labels: labelData,
      datasets: data,
    };
  }

  generateChartLabels(): string[] {
    return [
      this.translate.instant('childrenEvaluation.report.barChartLabel.disagreeCompletely'),
      this.translate.instant('childrenEvaluation.report.barChartLabel.disagreeToModerateDegree'),
      this.translate.instant('childrenEvaluation.report.barChartLabel.agreeToModerateDegree'),
      this.translate.instant('childrenEvaluation.report.barChartLabel.agreeCompletely'),
      this.translate.instant('childrenEvaluation.report.barChartLabel.noAnswerGiven'),
    ];
  }

  getDataSetModel(): DataSetModel[] {
    const isComparison = this.reportType === ReportType.COMPARISON_REPORT;
    const dataSet = [];
    dataSet.push(this.generateDataSet(this.answers.accumulatedScore, isComparison, isComparison));
    if (isComparison) {
      dataSet.push(this.generateDataSet(this.answersToCompareWith.accumulatedScore, false, true));
    }
    return dataSet;
  }

  private generateDataSet(
    answer: ChildrenSingleChoiceAnswerDistribution,
    isComparisonBar: boolean,
    hasBorder: boolean,
  ): DataSetModel {
    const borderColor = hasBorder ? this.comparisonColor : '';
    const borderWidth = hasBorder ? 2 : 0;
    return {
      barThickness: 'flex',
      maxBarThickness: this.maxBarThickness,
      barPercentage: this.barPercentage,
      categoryPercentage: this.categoryPercentage,
      data: this.getAccumulatedAnswers(answer),
      labels: ['', '', '', '', ''],
      backgroundColor: [
        isComparisonBar ? this.comparisonColor : this.getBarColor(answer.disagreeCompletely),
        isComparisonBar ? this.comparisonColor : this.getBarColor(answer.disagreeToModerateDegree),
        isComparisonBar ? this.comparisonColor : this.getBarColor(answer.agreeToModerateDegree),
        isComparisonBar ? this.comparisonColor : this.getBarColor(answer.agreeCompletely),
        isComparisonBar ? this.comparisonColor : this.getBarColor(answer.noAnswersGiven, true),
      ],
      borderColor,
      borderWidth,
      borderSkipped: false,
    };
  }

  private getAccumulatedAnswers(answer: ChildrenSingleChoiceAnswerDistribution): number[] {
    return [
      this.getBarCharScoreRepresentation(answer.disagreeCompletely),
      this.getBarCharScoreRepresentation(answer.disagreeToModerateDegree),
      this.getBarCharScoreRepresentation(answer.agreeToModerateDegree),
      this.getBarCharScoreRepresentation(answer.agreeCompletely),
      this.getBarCharScoreRepresentation(answer.noAnswersGiven),
    ];
  }

  private getBarCharScoreRepresentation(value: number): number {
    return value === 0 ? 0.1 : value;
  }

  private getYAxisMaxValue(): number {
    let answers = this.getAccumulatedAnswers(this.answers.accumulatedScore);
    if (this.answersToCompareWith) {
      answers = answers.concat(
        this.getAccumulatedAnswers(this.answersToCompareWith.accumulatedScore),
      );
    }
    const barMaxValue = Math.max(...answers);

    if (barMaxValue >= 9 && barMaxValue % 5 !== 0) {
      return barMaxValue + (5 - (barMaxValue % 5));
    }
    return barMaxValue;
  }

  private getYAxisRange(maxValue: number): number[] {
    if (maxValue < 10) {
      return [...Array(maxValue + 1).keys()];
    } else {
      return [...Array(maxValue / 5 + 1).keys()].map((x) => x * 5);
    }
  }

  private getBarColor(barValue: number, useDisabledColor: boolean = false): string {
    return barValue === 0 || useDisabledColor
      ? ChildEvaluationV1ReportColor.disabled
      : this.childEvalReportUtil.getQuestionColorCode(this.answers.id);
  }

  getDistributionFor(answer: ChildEvaluationAnswerOption): string {
    const sum =
      this.answers.accumulatedScore.disagreeCompletely +
      this.answers.accumulatedScore.disagreeToModerateDegree +
      this.answers.accumulatedScore.agreeToModerateDegree +
      this.answers.accumulatedScore.agreeCompletely +
      this.answers.accumulatedScore.noAnswersGiven;

    switch (answer) {
      case ChildEvaluationAnswerOption.AGREE_COMPLETELY:
        return this.calcShareOfAnswer(sum, this.answers.accumulatedScore.agreeCompletely);
      case ChildEvaluationAnswerOption.AGREE_TO_MODERATE_DEGREE:
        return this.calcShareOfAnswer(sum, this.answers.accumulatedScore.agreeToModerateDegree);
      case ChildEvaluationAnswerOption.DISAGREE_TO_MODERATE_DEGREE:
        return this.calcShareOfAnswer(sum, this.answers.accumulatedScore.disagreeToModerateDegree);
      case ChildEvaluationAnswerOption.DISAGREE_COMPLETELY:
        return this.calcShareOfAnswer(sum, this.answers.accumulatedScore.disagreeCompletely);
    }
  }

  private calcShareOfAnswer(sum: number, share: number): string {
    const percent = (100 / sum) * share;
    return (Math.round(percent * 10) / 10).toString();
  }
}

export enum ChildEvaluationAnswerOption {
  NO_ANSWER_GIVEN = '0',
  AGREE_COMPLETELY = '1',
  AGREE_TO_MODERATE_DEGREE = '2',
  DISAGREE_TO_MODERATE_DEGREE = '3',
  DISAGREE_COMPLETELY = '4',
}
