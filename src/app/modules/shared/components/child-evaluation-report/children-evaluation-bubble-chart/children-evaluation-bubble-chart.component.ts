import {
  AfterViewChecked,
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  ViewChild,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { D3PackedBubbleChartUtils } from '../../../../../utils/d3-packed-bubble-chart.utils';
import { ChildrenSingleChoiceAnswerMean } from '../../../../reports/children-evaluation-report/models/children-evaluation-report';
import { ChildEvaluationReportUtil } from '../child-evaluation-report-util';

@Component({
  selector: 'app-bubble-chart-child',
  templateUrl: './children-evaluation-bubble-chart.component.html',
  styleUrls: ['./children-evaluation-bubble-chart.component.scss'],
})
export class ChildrenEvaluationBubbleChartComponent implements AfterViewChecked, AfterViewInit {
  @Input() data: ChildrenSingleChoiceAnswerMean[];

  constructor(
    private translate: TranslateService,
    private childEvaluationReportUtil: ChildEvaluationReportUtil,
  ) {}

  @ViewChild('bubbleChart', { static: false }) bubbleChart: ElementRef;

  ngAfterViewInit(): void {
    const questions = this.data;

    const bubbles = [];
    Array.from(questions).forEach((question) => {
      const score = question.meanScore;

      bubbles.push({
        name: this.translate.instant(
          this.childEvaluationReportUtil.getShortTranslationFromQuestionId(question.id),
        ),
        count: score <= 0 ? 1 : score,
        color: this.getColorForQuestion(question.id),
      });
    });

    const dataset = {
      children: [
        {
          name: 'Questions',
          children: bubbles,
        },
      ],
    };

    D3PackedBubbleChartUtils.renderChart(dataset, this.bubbleChart.nativeElement, 700, 700, 120);
  }

  ngAfterViewChecked(): void {
    D3PackedBubbleChartUtils.resizeSvg();
  }

  getColorForQuestion(questionId: string): string {
    return this.childEvaluationReportUtil.getQuestionColorCode(questionId);
  }
}
