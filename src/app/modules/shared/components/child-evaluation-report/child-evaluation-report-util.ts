import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChildEvaluationV1ReportColor } from './child-evaluation-report-color-scheme';

@Injectable({
  providedIn: 'root',
})
export class ChildEvaluationReportUtil {
  constructor(private translate: TranslateService) {}

  CHILD_EVALUATION_TRANSLATION_KEY_PREFIX = 'children_eval_v1.';
  CHILD_EVALUATION_TRANSLATION_KEY_POSTFIX = '.title';
  CHILD_EVALUATION_TRANSLATION_KEY_POSTFIX_SHORT = '.title_short';

  getTranslationFromQuestionId(questionId: string): string {
    return this.translate.instant(
      this.CHILD_EVALUATION_TRANSLATION_KEY_PREFIX +
        questionId +
        this.CHILD_EVALUATION_TRANSLATION_KEY_POSTFIX,
    );
  }

  getShortTranslationFromQuestionId(questionId: string): string {
    return this.translate.instant(
      this.CHILD_EVALUATION_TRANSLATION_KEY_PREFIX +
        questionId +
        this.CHILD_EVALUATION_TRANSLATION_KEY_POSTFIX_SHORT,
    );
  }

  public getQuestionTitleFromIdAndCodeBookKey(questionId: string, codeBookKey: string): string {
    return this.translate.instant(`children_eval_v1.${questionId}.question.${codeBookKey}`);
  }

  getQuestionColorCode(questionId: string): ChildEvaluationV1ReportColor {
    switch (questionId) {
      case 'q1':
        return ChildEvaluationV1ReportColor.q1;
      case 'q2':
        return ChildEvaluationV1ReportColor.q2;
      case 'q3':
        return ChildEvaluationV1ReportColor.q3;
      case 'q4':
        return ChildEvaluationV1ReportColor.q4;
      case 'q5':
        return ChildEvaluationV1ReportColor.q5;
      case 'q6':
        return ChildEvaluationV1ReportColor.q6;
      case 'q7':
        return ChildEvaluationV1ReportColor.q7;
      case 'q8':
        return ChildEvaluationV1ReportColor.q8;
      case 'q9':
        return ChildEvaluationV1ReportColor.q9;
      case 'q10':
        return ChildEvaluationV1ReportColor.q10;
      default:
        return ChildEvaluationV1ReportColor.q1;
    }
  }
}
