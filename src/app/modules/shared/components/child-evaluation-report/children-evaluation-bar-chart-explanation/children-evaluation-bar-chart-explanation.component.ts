import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-children-evaluation-bar-chart-explanation',
  templateUrl: './children-evaluation-bar-chart-explanation.component.html',
  styleUrls: ['./children-evaluation-bar-chart-explanation.component.scss'],
})
export class ChildrenEvaluationBarChartExplanationComponent {
  @Input() childEvaluationQuestionId: string;
  @Input() agreeCompletelyShare: string;
  @Input() agreeToModerateDegreeShare: string;
  @Input() disagreeCompletelyShare: string;
  @Input() disagreeToModerateDegreeShare: string;

  constructor(public translate: TranslateService) {}

  public get agreeCompletelyExplanation(): () => string {
    return this.translate.instant(
      `childrenEvaluation.report.answerExplanation.${this.childEvaluationQuestionId}.agreeCompletely`,
    );
  }

  public get agreeToModerateDegreeExplanation(): () => string {
    return this.translate.instant(
      `childrenEvaluation.report.answerExplanation.${this.childEvaluationQuestionId}.agreeToModerateDegree`,
    );
  }

  public get disagreeToModerateDegreeExplanation(): () => string {
    return this.translate.instant(
      `childrenEvaluation.report.answerExplanation.${this.childEvaluationQuestionId}.disagreeToModerateDegree`,
    );
  }

  public get disagreeCompletelyExplanation(): () => string {
    return this.translate.instant(
      `childrenEvaluation.report.answerExplanation.${this.childEvaluationQuestionId}.disagreeCompletely`,
    );
  }
}
