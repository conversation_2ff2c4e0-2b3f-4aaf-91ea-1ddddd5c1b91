@import './../../../../../theme/variables_v2';

.input-container {
  position: relative;
  height: 58px;
  margin-top: 6px;

  ::ng-deep {
    ion-select {
      height: 56px;
      color: $color-primary-v2;
      border: none;
      margin: 0;
      background-color: transparent;
      padding-bottom: 10px;
    }
  }
}

.label-container {
  color: var(--ion-color-dark-grey-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
}

.validation-box {
  padding-top: 6px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
  color: $color-danger;
}

.custom-dropdown {
  width: 100%;
  position: absolute;
  z-index: 200;
  border: 1px solid $color-light-gray;
  border-radius: 8px;
  background-color: white;
  transition: max-height 0.3s ease-in-out;
  max-height: 56px;
  overflow-y: hidden;
  cursor: default;

  &.valid {
    padding-top: 10px;
    margin-top: 8px;
    padding-left: 16px;
  }

  &.invalid {
    border-color: $color-danger;
  }

  &.open {
    z-index: 201;
    max-height: 336px;
    transition: max-height 0.3s ease-in-out;
    box-shadow: 2.5px 2.5px 4.5px 2.5px rgba(0, 0, 0, 0.32);
  }
}

.dropdown-toggle {
  display: flex;
  justify-content: space-between;
  padding: 14px;
  border-bottom: 1px solid $color-light-gray;

  &.empty {
    color: $color-dark-gray;
  }

  &.closed {
    border-bottom: none;
  }
}

.dropdown-menu {
  overflow-y: scroll;
  transition: max-height 0.3s ease-in-out;
  max-height: 0;

  &.open {
    max-height: 280px;
    overflow-y: scroll;
  }
}

.dropdown-toggle-item {
  &.open {
    rotate: 180deg;
    transition: rotate 0.3s ease-in-out;
  }

  &.closed {
    rotate: 0deg;
    transition: rotate 0.3s ease-in-out;
  }
}

.dropdown-item {
  border-top: 1px solid $color-light-gray;
  padding: 14px;

  &.first {
    border-top: none;
  }
}
