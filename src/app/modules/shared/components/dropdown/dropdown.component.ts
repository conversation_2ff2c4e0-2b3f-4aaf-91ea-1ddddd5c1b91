import { Component, HostListener, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NgClass } from '@angular/common';
import type { Option } from './Option';

@Component({
  selector: 'app-dropdown',
  templateUrl: './dropdown.component.html',
  styleUrls: ['./dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: DropdownComponent,
      multi: true,
    },
  ],
})
export class DropdownComponent<T> implements ControlValueAccessor {
  @Input() type: string;
  @Input() label = '';
  @Input() errorMessage?: string;
  @Input() options: Option<T>[];
  @Input() interfaceOptions: any;
  @Input() inputValue: string;
  @Input() placeholder = 'Select an option';
  @Input() displaySelected = true;

  selectedOption: string | null = null;
  showDropdown = false;
  empty = true;
  clickedInside = false;

  protected readonly NgClass = NgClass;

  private onChange = (value: T) => {};

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {}

  writeValue(value: T): void {
    this.onChange(value);
  }

  isError(): boolean {
    return this.errorMessage != null;
  }

  toggleDropdown(): void {
    this.clickedInside = true;
    this.showDropdown = !this.showDropdown;
  }

  getOptionLabels(): string[] {
    return this.options.map((option) => option.label);
  }

  onSelectOption(label: string): void {
    const option = this.options.find((o) => o.label === label);
    this.selectedOption = option.label;
    this.showDropdown = false;
    this.empty = !(this.selectedOption && this.displaySelected);
    this.writeValue(option.value); // Notify the form control about the selected value
  }

  @HostListener('document:click')
  clickout(): void {
    if (!this.clickedInside) {
      this.showDropdown = false;
    }
    this.clickedInside = false;
  }
}
