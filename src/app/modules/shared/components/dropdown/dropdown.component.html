<label class="label-container">{{ label.toUpperCase() }}</label>
<div class="input-container">
  <div
    [ngClass]="['custom-dropdown', showDropdown ? 'open' : 'closed', isError() ? 'invalid' : '']"
  >
    <div
      [ngClass]="['dropdown-toggle', showDropdown ? 'open' : 'closed', empty ? 'empty' : '']"
      (click)="toggleDropdown()"
    >
      <span *ngIf="!empty && selectedOption; else placeholder_element">{{ selectedOption }}</span>
      <ng-template #placeholder_element>{{ placeholder }}</ng-template>
      <img
        [ngClass]="['dropdown-toggle-icon', showDropdown ? 'open' : 'closed']"
        src="/assets/svg/disclosure_down.svg"
      />
    </div>
    <div [ngClass]="['dropdown-menu', showDropdown ? 'open' : 'closed']">
      <div
        [ngClass]="['dropdown-item', i === 0 ? 'first' : '']"
        *ngFor="let label of getOptionLabels(); let i = index"
        (click)="onSelectOption(label)"
      >
        {{ label }}
      </div>
    </div>
  </div>
</div>
<ng-container *ngIf="isError()">
  <div class="validation-box">
    <ion-text>
      {{ errorMessage | translate }}
    </ion-text>
  </div>
</ng-container>
