import { Component, Input } from '@angular/core';
import { ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Utils } from '../../../../utils/utils';

@Component({
  selector: 'app-inactive-copyable-input-field',
  templateUrl: './inactive-copyable-input-field.component.html',
  styleUrls: ['./inactive-copyable-input-field.component.scss'],
})
export class InactiveCopyableInputFieldComponent {
  @Input() text: string;
  @Input() labelKey: string;
  @Input() isHidden = false;

  constructor(
    public toastController: ToastController,
    public translate: TranslateService,
  ) {}

  copyValueToClipboard(): void {
    Utils.copyToClipboard(this.text);
    this.showSuccessMessage();
  }

  async showSuccessMessage(): Promise<void> {
    const toast = await this.toastController.create({
      message: this.translate.instant('global.copied'),
      color: 'success',
      position: 'bottom',
      duration: 5000,
      cssClass: 'global-toast',
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }
}
