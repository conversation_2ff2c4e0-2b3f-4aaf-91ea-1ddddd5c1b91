@import './../../../../../theme/variables_v2';

ion-grid,
ion-row,
ion-col {
  padding: 0;
}

.input-container {
  border: 1px solid $color-light-gray;
  border-radius: 8px;
  margin-top: 6px;
  cursor: pointer;

  ::ng-deep {
    ion-input {
      margin: 0 56px 0 16px;
      height: 54px;
      color: $color-hint-text-grey;
      border: none;
      background-color: transparent;
      overflow: hidden;
      text-overflow: ellipsis;

      > input {
        --placeholder-color: var(--ion-color-placeholder-V2);
        --placeholder-opacity: 1;
        margin-bottom: 0;
        cursor: pointer;
      }
    }
  }

  &.invalid {
    border-color: $color-danger;
  }
}

.label-container {
  color: var(--ion-color-dark-grey-V2);
  text-transform: uppercase;
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
}

.ripple-parent {
  position: relative;
  overflow: hidden;
  border-radius: 7px;
}

.copy-icon {
  width: 26px;
  height: 26px;
  cursor: pointer;
  position: absolute;
  right: 16px;
  top: 13px;
}
