import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-not-found',
  templateUrl: './not-found.component.html',
  styleUrls: ['./not-found.component.scss'],
})
export class NotFoundComponent {
  @Input() message: string | null;

  constructor(private translate: TranslateService) {}

  public get errorMessage(): string {
    return this.message != null
      ? this.message
      : this.translate.instant('questionnaire.message.notFound');
  }
}
