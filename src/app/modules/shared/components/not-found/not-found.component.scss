@import './../../../../../theme/variables_v2';

ion-grid,
ion-col {
  padding: 0;
  height: 100%;
}

.error-page-img {
  position: relative;
  height: 100%;
  width: 100%;
  z-index: 1;
  background: url('/assets/img/error_404.webp') top;
  background-size: cover;
}

.logo {
  position: absolute;
  width: 100px;
  height: auto;
  right: 46px;
  top: 46px;
}

.content-container {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: auto 24px;

  .not-found-icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .not-found-icon {
      width: 60px;
      height: auto;
    }
  }

  .message-container {
    padding: 0 80px;
    align-items: center;
    text-align: center;

    .title {
      font-weight: 900;
      font-size: large;
    }
  }
}

.img-container {
  position: relative;
}

.scroll-content {
  overflow: scroll;
  height: 100vh;
}

.horizontal-spacer {
  height: 1rem;
  width: 100%;
}
