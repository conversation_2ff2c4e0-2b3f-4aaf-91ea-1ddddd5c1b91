<ion-label class="label-container">{{ label.toUpperCase() }}</ion-label>
<div class="input-container" [ngClass]="{ 'invalid': isError(), 'v2': isV2() }">
  <mat-form-field
    [ngClass]="{ 'input': true, 'disabled-field': isDisabled }"
    (click)="isDisabled ? picker.close() : picker.open()"
  >
    <input
      *ngIf="isV2()"
      [ngClass]="{ 'disabled-field': isDisabled }"
      matInput
      [matDatepicker]="picker"
      [value]="inputValue"
      disabled
      (dateChange)="writeValue($event.value)"
      [max]="getMaxDate()"
    />
    <input
      *ngIf="!isV2()"
      [ngClass]="{ 'disabled-field': isDisabled }"
      matInput
      [matDatepicker]="picker"
      [value]="inputValue"
      disabled
      (dateChange)="writeValue($event.value)"
      [placeholder]="v1Placeholder"
      [max]="getMaxDate()"
    />
    <mat-datepicker-toggle *ngIf="!isDisabled && isV2()" matSuffix [for]="picker">
      <mat-icon class="cal-icon" [ngClass]="{ 'v2': isV2() }" matDatepickerToggleIcon>
        <img src="assets/svg/calendar.svg" alt="toggle datepicker" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker disabled="false"></mat-datepicker>
  </mat-form-field>
</div>
<ng-container *ngIf="isError()">
  <div class="validation-box">
    <ion-text>
      {{ errorMessage | translate }}
    </ion-text>
  </div>
</ng-container>
