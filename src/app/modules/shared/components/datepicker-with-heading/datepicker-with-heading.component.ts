import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import {
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
  MomentDateAdapter,
} from '@angular/material-moment-adapter';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { UiVersion } from '../../../../utils/utils';
import { Moment } from 'moment';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL',
  },
  display: {
    dateInput: 'LL',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-datepicker-with-heading',
  templateUrl: './datepicker-with-heading.component.html',
  styleUrls: ['./datepicker-with-heading.component.scss'],
  providers: [
    {
      provide: MAT_DATE_LOCALE,
      useValue: 'de-DE',
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: MY_FORMATS,
    },
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: DatepickerWithHeadingComponent,
      multi: true,
    },
  ],
})
export class DatepickerWithHeadingComponent implements ControlValueAccessor {
  @Input() label = '';
  @Input() v1Placeholder = '';
  @Input() errorMessage?: string;
  @Input() inputValue: string;
  @Input() isDisabled = false;
  @Input() version = UiVersion.v2;
  @Output() handleChange?: EventEmitter<Moment> = new EventEmitter<Moment>();
  @Input() hasMaxDate = false;

  private onChange = (value: Moment) => {
    this.handleChange.emit(value);
  };

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {}

  writeValue(value: Moment): void {
    this.onChange(value);
  }

  isError(): boolean {
    return this.errorMessage != null;
  }

  isV2(): boolean {
    return this.version === UiVersion.v2;
  }

  getMaxDate(): string {
    if (this.hasMaxDate) {
      return new Date().toISOString().split('T')[0];
    }
    return new Date('9999-12-31').toISOString().split('T')[0];
  }
}
