@import './../../../../../theme/variables_v2';

.input-container {
  height: 58px;
  border-radius: 8px;
  padding-right: 8px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  mat-form-field {
    margin-bottom: -0.5rem;
  }

  ::ng-deep {
    .mat-mdc-form-field {
      width: 100%;
      cursor: pointer;

      &.disabled-field {
        cursor: default;
      }

      .mat-mdc-input-element {
        cursor: pointer;
        &:disabled {
          color: $color-primary-v2;
        }

        &.disabled-field {
          cursor: default;
          color: $color-hint-text-grey;
          -webkit-text-fill-color: $color-hint-text-grey;
          opacity: 1;
        }
      }
    }
  }

  &.v2 {
    border: 1px solid $color-light-gray;
    padding-top: 10px;
    margin-top: 8px;
    padding-left: 16px;
  }

  &.invalid {
    border-color: $color-danger;
  }
}

.cal-icon {
  margin-bottom: 10px !important;

  &.v2 {
    margin-bottom: 0 !important;
  }
}

.label-container {
  color: var(--ion-color-dark-grey-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
}

.validation-box {
  padding-top: 6px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
  color: $color-danger;
}
