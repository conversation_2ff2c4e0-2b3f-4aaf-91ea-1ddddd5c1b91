<ion-grid class="page-break">
  <ion-row>
    <ion-col>
      <ion-text class="trait-title">{{ traitTitle }}</ion-text>
      <br />
      <ion-text class="trait-explanation">{{ traitExplanation }}</ion-text>
      <app-bar-chart [trait]="trait" [educators]="educators" [scale]="scale"></app-bar-chart>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <div>
        <div class="branch-container" *ngFor="let branch of branches">
          <div [ngClass]="getColorClass(colorTargets.TRAIT_BG_COLOR, branch)">
            <ion-text class="branch-title">{{ branch.localizationKey() | translate }}</ion-text>
          </div>
          <ion-text class="branch-explanation">{{ getBranchExplanation(branch) }}</ion-text>
          <div class="assessment-wrapper">
            <ion-text class="branch-score">{{ getScoreFromBranch(branch) }}</ion-text>
            <div [ngClass]="getColorClass(colorTargets.DIVIDER_COLOR, branch)"></div>
            <ion-text class="assessment-text">{{ getAssessment(branch) }}</ion-text>
          </div>
          <div *ngIf="isNoteAvailable(branch)" class="note-wrapper">
            <ion-text class="note-title">{{
              'analysis.label.evaluatorComment' | translate
            }}</ion-text>
            <br />
            <ion-text class="note-text">{{ branch.notes.get(educator.id()) }}</ion-text>
          </div>
        </div>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
