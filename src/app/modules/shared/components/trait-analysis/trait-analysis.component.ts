import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Trait } from '../../../../services/evaluation/models/trait';
import { Educator } from '../../../../services/evaluation/models/educator';
import { ScaleDefinition } from '../../../../services/evaluation/models/scale-definition';
import { Branch } from '../../../../services/evaluation/models/branch';
import { ColorTargets, getColorSchemeFor } from '../../../../utils/color-scheme-for-scale';
import { ScaleService } from '../../../../services/evaluation/scale.service';

@Component({
  selector: 'app-trait-analysis',
  templateUrl: './trait-analysis.component.html',
  styleUrls: ['./trait-analysis.component.scss'],
})
export class TraitAnalysisComponent implements OnInit {
  @Input() trait: Trait;
  @Input() educators: Educator[];
  @Input() scale: ScaleDefinition;

  public branches: Branch[];

  public traitTitle: string;
  public traitExplanation: string;
  public educator: Educator;
  public colorTargets = ColorTargets;

  constructor(
    private translate: TranslateService,
    private scaleService: ScaleService,
  ) {}

  ngOnInit(): void {
    this.init();
  }

  init(): void {
    this.traitTitle = this.translate.instant(this.trait.localizationKey());
    this.traitExplanation = this.translate.instant(this.trait.getExplanationLocalizationKey());
    this.branches = Array.from(this.trait.branches.values());
    this.educator = this.educators[0];
  }

  isNoteAvailable(branch: Branch): boolean {
    return branch.notes.has(this.educator.id());
  }

  getAssessment(branch: Branch): string {
    const assessment = branch.scores.get(this.educator.id());
    let key: string;

    if (this.scaleService.hasScoringSchemeV2(this.scale)) {
      switch (assessment) {
        case 1:
          key = `${branch.localizationKeyPrefix()}.analysis.${assessment.toString()}`;
          break;
        case 2:
          key = `${branch.localizationKeyPrefix()}.analysis.3`;
          break;
        case 2.5:
          key = `${branch.localizationKeyPrefix()}.analysis.4`;
          break;
        case 3:
          key = `${branch.localizationKeyPrefix()}.analysis.5`;
          break;
        case 4:
          key = `${branch.localizationKeyPrefix()}.analysis.7`;
          break;
        default:
          key = `${branch.localizationKeyPrefix()}.analysis.9`;
      }
    } else {
      key = `${branch.localizationKeyPrefix()}.analysis.${assessment.toString()}`;
    }
    return this.translate.instant(key);
  }

  getScoreFromBranch(branch: Branch): number | string {
    return branch.scores.get(this.educator.id()) === 9
      ? '-'
      : branch.scores.get(this.educator.id());
  }

  getBranchExplanation(branch: Branch): string {
    const key = `${branch.localizationKeyPrefix()}.analysis.explanation`;
    return this.translate.instant(key);
  }

  getColorClass(target: ColorTargets, branch: Branch): string {
    return getColorSchemeFor(target, this.scale, branch.trait);
  }
}
