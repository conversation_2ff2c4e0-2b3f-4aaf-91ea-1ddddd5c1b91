@import 'src/theme/_variables_v2.scss';

.info-section {
  background: $color-info-box-background;
  border-radius: 0.5rem;
  width: 100%;

  ion-grid {
    margin: 0 2rem;
  }

  .section-title {
    color: var(--ion-color-dark-grey-V2);
    text-transform: uppercase;
    font-size: 13px;
    line-height: 18px;
    font-weight: 700;
  }

  .bold {
    font-weight: 700;
  }
}

.horizontal-spacer {
  height: 1rem;
}
