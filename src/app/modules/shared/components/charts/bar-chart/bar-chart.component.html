<div class="ion-padding">
  <h3 *ngIf="isComparisonReport">{{ comparisonReportParams.illustrationTitle }}</h3>
  <div
    [hidden]="isChartHidden"
    #chartContainer
    [className]="showBarLabels ? 'bar-chart-container-with-labels' : 'bar-chart-container'"
  >
    <canvas #barCanvas></canvas>
  </div>
  <div
    #imgWrap
    [className]="showBarLabels ? 'bar-image-container-with-labels' : 'bar-image-container'"
  ></div>

  <div *ngIf="legendConfiguration">
    <div class="spacing-medium"></div>
    <app-chart-legend
      *ngIf="isComparisonReport"
      [configuration]="this.legendConfiguration"
    ></app-chart-legend>
    <div class="spacing-medium"></div>
  </div>
</div>
