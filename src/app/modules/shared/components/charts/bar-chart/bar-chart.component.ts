import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Chart } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import _ from 'lodash';
import { EvaluationViewModel } from '../../../../../pages/evaluator-analysis/evaluator-analysis.page';
import { Educator } from '../../../../../services/evaluation/models/educator';
import { ScaleDefinition } from '../../../../../services/evaluation/models/scale-definition';
import { Trait } from '../../../../../services/evaluation/models/trait';
import { ScaleService } from '../../../../../services/evaluation/scale.service';
import { ComparisonReportEvaluationBarChartParams } from '../../../../reports/shared-report-components/comparison-report-evaluations/comparison-report-evaluations.component';
import { LegendEntry } from '../chart-legend/chart-legend.component';
import { DataSetModel, DataSetsModel } from '../report-chart-models';

@Component({
  selector: 'app-bar-chart',
  templateUrl: './bar-chart.component.html',
  styleUrls: ['./bar-chart.component.scss'],
})
export class BarChartComponent implements OnInit, AfterViewInit {
  @Input() trait: Trait;
  @Input() educators: Educator[];
  @Input() evaluations: EvaluationViewModel[];
  @Input() scale: ScaleDefinition;
  @Input() comparisonReportParams?: ComparisonReportEvaluationBarChartParams | undefined;
  @ViewChild('barCanvas', { static: true }) barCanvas: ElementRef;
  @ViewChild('imgWrap', { static: false }) imgWrap: ElementRef;
  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef;

  private barChart: Chart;

  private singleBarWidth: number;
  private maxBarThickness: number;
  private categoryPercentage: number;
  private barPercentage: number;
  private barColor: string;
  private barBorderColor: string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private traitColors: any;

  private labelHeight: number;
  private comparisonReportColor = '#000000';
  isComparisonReport = false;
  showBarLabels: boolean;
  isChartHidden = false;
  isV2ScoreCalculation = false;
  legendConfiguration: LegendEntry[];

  constructor(
    private renderer: Renderer2,
    private translate: TranslateService,
    private scaleService: ScaleService,
  ) {}

  ngOnInit(): void {
    this.isComparisonReport = this.comparisonReportParams !== undefined;
    this.initTraitColors();
    this.initChartConfig();
    this.initLegendConfig();
  }

  private initLegendConfig(): void {
    if (this.isComparisonReport) {
      this.legendConfiguration = [
        {
          description: this.evaluations[0].dateOfSurveyFormatted,
          color: this.comparisonReportColor,
        },
        {
          description: this.evaluations[1].dateOfSurveyFormatted,
          color: this.traitColors[this.trait.id],
        },
      ];
    }
  }

  private initChartConfig(): void {
    this.isV2ScoreCalculation =
      this.scale === ScaleDefinition.GRAZIAS_V2_ZERO_TO_SIX ||
      this.scale === ScaleDefinition.GRAZIAS_V2_SELF_EVALUATION_ZERO_TO_SIX;
    if (this.isEvaluationComparison()) {
      this.maxBarThickness = this.evaluations.length > 1 ? 25 : 75;
      this.categoryPercentage = this.evaluations.length > 1 ? 0.4 : 0.4;
      this.barPercentage = this.evaluations.length > 1 ? 0.8 : 1;
      this.evaluations.sort((a, b) => {
        return a.dateOfSurvey < b.dateOfSurvey ? -1 : 1;
      });
    } else {
      this.maxBarThickness = this.educators.length > 1 ? 25 : 75;
      this.categoryPercentage = this.educators.length > 1 ? 0.2 : 0.4;
      this.barPercentage = this.educators.length > 1 ? 0.8 : 1;
    }

    this.singleBarWidth = 300;
    this.barColor = this.traitColors[this.trait.id];
    this.barBorderColor = this.isComparisonReport ? this.comparisonReportColor : this.barColor;
    this.showBarLabels = this.isEvaluationComparison() && !this.isComparisonReport;
    this.labelHeight = this.showBarLabels ? 200 : 0;
  }

  private isEvaluationComparison(): boolean {
    return this.evaluations !== undefined;
  }

  initTraitColors(): void {
    this.traitColors = this.scaleService.getTraitColorsForScale(this.scale);
  }

  ngAfterViewInit(): void {
    // makeRoundedCorners(this.barChart);
    this.renderChart();
  }

  renderChart(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const context = this;
    const imageClass = this.isEvaluationComparison() ? 'bar-image-with-labels' : 'bar-image';
    const yAxisDefinition = this.isV2ScoreCalculation ? [0, 1, 2, 3, 4] : [0, 1, 3, 5, 7];

    this.barChart = new Chart(this.barCanvas.nativeElement, {
      plugins: [ChartDataLabels],
      type: 'bar',
      // @ts-expect-error data type does not match
      data: this.generateDataForBarChart(),
      options: {
        responsive: true,
        maintainAspectRatio: false,

        borderColor: this.barColor,
        animation: {
          onComplete(): void {
            const barImage = this.toBase64Image();
            if (barImage !== 'data:,') {
              const img = context.renderer.createElement('img');
              context.renderer.addClass(img, imageClass);
              img.src = barImage;
              context.renderer.appendChild(context.imgWrap.nativeElement, img);
              context.isChartHidden = true;
            }
          },
        },
        legend: {
          display: false,
        },
        cornerRadius: 20,
        scales: {
          yAxes: [
            {
              ticks: {
                fontColor: 'black',
                fontSize: 30,
                padding: 20,
                min: 0,
                max: this.isV2ScoreCalculation ? 4 : 7,
                callback(value): string | number {
                  if (value === 0) {
                    return '';
                  } else {
                    return value;
                  }
                },
              },
              afterBuildTicks(): number[] {
                return yAxisDefinition;
              },
              gridLines: {
                drawBorder: false,
                color: this.traitColors.disabledBranch,
                zeroLineColor: this.traitColors.disabledBranch,
              },
            },
          ],
          xAxes: [
            {
              ticks: {
                fontColor: 'black',
                fontSize: 22,
                maxRotation: 0,
                minRotation: 0,
                // @ts-expect-error data type does not match
                callback(label): string[] | string | number {
                  // Break line of word is too long
                  if (typeof label === 'string' && label.includes('\n')) {
                    return label.split('\n');
                  } else {
                    return label;
                  }
                },
              },
              gridLines: {
                display: false,
                zeroLineColor: this.barColor,
              },
            },
          ],
        },
        layout: {
          padding: {
            left: 0,
            right: 0,
            top: this.labelHeight,
            bottom: 0,
          },
        },
        plugins: {
          datalabels: {
            align: 'top',
            color: 'black',
            rotation: -90,
            anchor: 'start',
            backgroundColor: null,
            borderColor: null,
            borderRadius: 4,
            borderWidth: 1,
            clamp: true,
            font: {
              size: 20,
              weight: 400,
            },
            offset(context): number {
              return context.chart.chartArea.bottom - context.chart.chartArea.top + 15;
            },
            formatter(value, context): number {
              // @ts-expect-error labels does not exist on ChartDataSets
              return context.chart.data.datasets[context.datasetIndex].labels[context.dataIndex];
            },
          },
        },
      },
    });
  }

  generateDataForBarChart(): DataSetsModel {
    const labelData = this.generateChartLabels();
    const scores = this.isComparisonReport
      ? this.collectScoreForComparisonReport()
      : this.isEvaluationComparison()
        ? this.collectScoreByEvaluation()
        : this.collectScoreByEducator();
    const length = this.isEvaluationComparison() ? this.evaluations.length : this.educators.length;
    const perBarLabels = this.isEvaluationComparison()
      ? this.collectLabelsByEvaluations(labelData.length)
      : [];

    this.renderer.setStyle(
      this.chartContainer.nativeElement,
      'width',
      labelData.length * this.singleBarWidth + 'px',
    );

    const sortedScoresByBranch = this.rearrangeScoresByBranch(scores, labelData.length);
    const data = this.generateBarGroups(sortedScoresByBranch, perBarLabels, length);

    return {
      labels: labelData,
      datasets: data,
    };
  }

  generateChartLabels(): string[] {
    const labelData = [];

    for (const branch of this.trait.branches.values()) {
      const chartTitleLocalizationKey = branch.localizationKeyPrefix() + '.chartLabel';
      const localizedChartTitle = this.translate.instant(chartTitleLocalizationKey);
      const chartTitle =
        localizedChartTitle === chartTitleLocalizationKey
          ? this.translate.instant(branch.localizationKey())
          : localizedChartTitle;

      labelData.push(chartTitle);
    }

    return labelData;
  }

  collectScoreByEducator(): number[][] {
    const scoresByEducator: number[][] = [];

    for (const educator of this.educators) {
      const educatorScores = [];
      for (const branch of this.trait.branches.values()) {
        const eduBranchScore =
          branch.scores.get(educator.id()) === 9 ? 0.5 : branch.scores.get(educator.id());
        educatorScores.push(eduBranchScore);
      }
      scoresByEducator.push(educatorScores);
    }
    return scoresByEducator;
  }

  collectScoreByEvaluation(): number[][] {
    const scoresByEvaluation: number[][] = [];

    for (const evaluation of this.evaluations) {
      evaluation.evaluation.scales
        .values()
        .next()
        .value.traits.forEach((trait) => {
          const educator = this.educators[0];

          if (this.trait.id === trait.id) {
            const evaluationScores = [];
            for (const branch of trait.branches.values()) {
              const evaluationBranchScore =
                branch.scores.get(educator.id()) === 9 ? 0.5 : branch.scores.get(educator.id());
              evaluationScores.push(evaluationBranchScore);
            }
            scoresByEvaluation.push(evaluationScores);
          }
        });
    }

    return scoresByEvaluation;
  }

  collectScoreForComparisonReport(): number[][] {
    const scoresByEvaluation: number[][] = [];

    for (const evaluation of this.evaluations) {
      evaluation.evaluation.scales
        .values()
        .next()
        .value.traits.forEach((trait) => {
          if (this.trait.id === trait.id) {
            const evaluationScores = [];
            for (const branch of trait.branches.values()) {
              const scores = [];
              branch.scores.forEach((value) => {
                if (value !== 9) {
                  scores.push(value);
                }
              });
              let sum = 0;
              for (const score of scores) {
                sum += parseInt(score, 10);
              }
              const evaluationBranchScore = scores.length === 0 ? 0.5 : sum / scores.length;

              evaluationScores.push(evaluationBranchScore);
            }
            scoresByEvaluation.push(evaluationScores);
          }
        });
    }

    return scoresByEvaluation;
  }

  collectLabelsByEvaluations(scoresCount: number): string[][] {
    if (this.isComparisonReport) {
      return [];
    }
    const datesOfEvaluations: string[] = [];
    for (const evaluation of this.evaluations) {
      datesOfEvaluations.push(evaluation.dateOfSurveyBarChartFormatted.toUpperCase());
    }

    const labelsByEvaluations: string[][] = [];
    for (let i = 0; i < scoresCount; i++) {
      labelsByEvaluations.push(datesOfEvaluations);
    }
    return labelsByEvaluations;
  }

  rearrangeScoresByBranch(scoresByEducator: number[][], numberOfBranches: number): number[][] {
    const sortedScoresByBranch = [];

    if (this.isEvaluationComparison()) {
      for (let i = 0; i < numberOfBranches; i++) {
        const scores = scoresByEducator.map((it) => it[i]);
        sortedScoresByBranch.push(scores);
      }
    } else {
      for (let i = 0; i < numberOfBranches; i++) {
        const scores = scoresByEducator.map((it) => it[i]);
        sortedScoresByBranch.push(_.sortBy(scores).reverse());
      }
    }

    return sortedScoresByBranch;
  }

  generateBarGroups(
    sortedScoresByBranch: number[][],
    sortedLabelsByBranch: string[][],
    length: number,
  ): DataSetModel[] {
    const data: DataSetModel[] = [];

    // grouping is not really by educator due to sorting (but the number of groupings is still the same as the number of educators)
    for (let i = 0; i < length; i++) {
      const scores = sortedScoresByBranch.map((it) => it[i]);
      const labels = sortedLabelsByBranch.map((it) => it[i]);

      data.push({
        barThickness: 'flex',
        maxBarThickness: this.maxBarThickness,
        barPercentage: this.barPercentage,
        categoryPercentage: this.categoryPercentage,
        data: scores,
        labels,
        backgroundColor: this.getBarColor(i === 0, scores),
        borderColor: this.barBorderColor,
        borderWidth: 2,
        borderSkipped: false,
      });
    }

    return data;
  }

  private getBarColor(isFirst: boolean, scores: number[]) {
    const barColor =
      this.isComparisonReport && isFirst
        ? this.comparisonReportColor
        : this.traitColors[this.trait.id];
    return scores.map((score) => (score === 0.5 ? this.traitColors.disabledBranch : barColor));
  }
}
