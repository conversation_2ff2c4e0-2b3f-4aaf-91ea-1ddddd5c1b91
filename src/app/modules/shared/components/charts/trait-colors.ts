// IMPORTANT: Colors must also be changed in color.scss

export enum TraitColorsDidacticsZeroToThree {
  t1 = '#FFF099',
  t2 = '#f8d714',
  disabledBranch = '#D2D2D2',
}

export enum TraitColorsDidacticsThreeToSix {
  t1 = TraitColorsDidacticsZeroToThree.t1,
  t2 = TraitColorsDidacticsZeroToThree.t2,
  disabledBranch = TraitColorsDidacticsZeroToThree.disabledBranch,
}

export enum TraitColorsDidacticsZeroToSix {
  t1 = TraitColorsDidacticsZeroToThree.t1,
  t2 = TraitColorsDidacticsZeroToThree.t2,
  disabledBranch = TraitColorsDidacticsZeroToThree.disabledBranch,
}

export enum TraitColorsGraziasZeroToThree {
  t1 = '#A0D2FE',
  t2 = '#FDF860',
  t3 = '#B9F8FE',
  t4 = '#FFE9F0',
  t5 = '#EFB2EB',
  t6 = '#FBCDE9',
  t7 = '#FFD74F',
  t8 = '#B6F5DC',
  t9 = '#D0B3F9',
  t10 = '#D5FAA6',
  t11 = '#FFAD94',
  t12 = '#D5D9FF',
  disabledBranch = '#D2D2D2',
}

enum GraziasAdditionalColors {
  ZeroToSixT13 = '#FFC686',
  ZeroToSixT14 = '#FFC4B9',
}

export enum TraitColorsGraziasThreeToSix {
  t1 = TraitColorsGraziasZeroToThree.t1,
  t2 = TraitColorsGraziasZeroToThree.t2,
  t3 = TraitColorsGraziasZeroToThree.t3,
  t4 = TraitColorsGraziasZeroToThree.t4,
  t5 = TraitColorsGraziasZeroToThree.t5,
  t6 = TraitColorsGraziasZeroToThree.t6,
  t7 = TraitColorsGraziasZeroToThree.t7,
  t8 = TraitColorsGraziasZeroToThree.t8,
  t9 = TraitColorsGraziasZeroToThree.t9,
  t10 = TraitColorsGraziasZeroToThree.t10,
  t11 = TraitColorsGraziasZeroToThree.t11,
  t12 = TraitColorsGraziasZeroToThree.t12,
  disabledBranch = TraitColorsGraziasZeroToThree.disabledBranch,
}

export enum TraitColorsGraziasZeroToSix {
  t1 = TraitColorsGraziasZeroToThree.t1,
  t2 = TraitColorsGraziasZeroToThree.t2,
  t3 = TraitColorsGraziasZeroToThree.t3,
  t4 = TraitColorsGraziasZeroToThree.t4,
  t5 = TraitColorsGraziasZeroToThree.t5,
  t6 = TraitColorsGraziasZeroToThree.t6,
  t7 = TraitColorsGraziasZeroToThree.t7,
  t8 = TraitColorsGraziasZeroToThree.t8,
  t9 = TraitColorsGraziasZeroToThree.t9,
  t10 = TraitColorsGraziasZeroToThree.t10,
  t11 = TraitColorsGraziasZeroToThree.t11,
  t12 = TraitColorsGraziasZeroToThree.t12,
  t13 = GraziasAdditionalColors.ZeroToSixT13,
  t14 = GraziasAdditionalColors.ZeroToSixT14,
  disabledBranch = TraitColorsGraziasZeroToThree.disabledBranch,
}

export enum TraitColorsGraziasV2 {
  t1 = TraitColorsGraziasZeroToThree.t1,
  t2 = TraitColorsGraziasZeroToThree.t2,
  t3 = TraitColorsGraziasZeroToThree.t3,
  t4 = TraitColorsGraziasZeroToThree.t4,
  t5 = TraitColorsGraziasZeroToThree.t5,
  t6 = TraitColorsGraziasZeroToThree.t6,
  t7 = TraitColorsGraziasZeroToThree.t7,
  t8 = TraitColorsGraziasZeroToThree.t8,
  t9 = TraitColorsGraziasZeroToThree.t9,
  t10 = TraitColorsGraziasZeroToThree.t10,
  t10a = TraitColorsGraziasZeroToThree.t10,
  t10b = TraitColorsGraziasZeroToThree.t10,
  t10c = TraitColorsGraziasZeroToThree.t10,
  t11 = TraitColorsGraziasZeroToThree.t11,
  t12 = TraitColorsGraziasZeroToThree.t12,
  t13 = GraziasAdditionalColors.ZeroToSixT13,
  t14 = GraziasAdditionalColors.ZeroToSixT14,
  disabledBranch = TraitColorsGraziasZeroToThree.disabledBranch,
}

export enum TraitColorsMathZeroToThree {
  t1 = '#3DB5D0',
  t2 = '#41A7CE',
  t3 = '#469BCD',
  t4 = '#528ED3',
  t5 = '#627EDB',
  t6 = '#7270E4',
  disabledBranch = '#D2D2D2',
}

export enum TraitColorsMathThreeToSix {
  t1 = TraitColorsMathZeroToThree.t1,
  t2 = TraitColorsMathZeroToThree.t2,
  t3 = TraitColorsMathZeroToThree.t3,
  t4 = TraitColorsMathZeroToThree.t4,
  t5 = TraitColorsMathZeroToThree.t5,
  t6 = TraitColorsMathZeroToThree.t6,
  disabledBranch = TraitColorsGraziasZeroToThree.disabledBranch,
}

export enum TraitColorsMathZeroToSix {
  t1 = TraitColorsMathZeroToThree.t1,
  t2 = TraitColorsMathZeroToThree.t2,
  t3 = TraitColorsMathZeroToThree.t3,
  t4 = TraitColorsMathZeroToThree.t4,
  t5 = TraitColorsMathZeroToThree.t5,
  t6 = TraitColorsMathZeroToThree.t6,
  disabledBranch = TraitColorsMathZeroToThree.disabledBranch,
}

export enum TraitColorsNatureZeroToThree {
  t1 = '#F4C100',
  t2 = '#F6B800',
  t3 = '#F8AE00',
  t4 = '#F9A600',
  t5 = '#FB9C00',
  t6 = '#FC9400',
  t7 = '#FD8B07',
  t8 = '#FE8917',
  t9 = '#FE8527',
  t10 = '#FE8134',
  t11 = '#FF7F42',
  t12 = '#FF7A53',
  disabledBranch = '#D2D2D2',
}

export enum TraitColorsNatureThreeToSix {
  t1 = TraitColorsNatureZeroToThree.t1,
  t2 = TraitColorsNatureZeroToThree.t2,
  t3 = TraitColorsNatureZeroToThree.t3,
  t4 = TraitColorsNatureZeroToThree.t4,
  t5 = TraitColorsNatureZeroToThree.t5,
  t6 = TraitColorsNatureZeroToThree.t6,
  t7 = TraitColorsNatureZeroToThree.t7,
  t8 = TraitColorsNatureZeroToThree.t8,
  t9 = TraitColorsNatureZeroToThree.t9,
  t10 = TraitColorsNatureZeroToThree.t10,
  t11 = TraitColorsNatureZeroToThree.t11,
  t12 = TraitColorsNatureZeroToThree.t12,
  disabledBranch = TraitColorsNatureZeroToThree.disabledBranch,
}

export enum TraitColorsNatureZeroToSix {
  t1 = TraitColorsNatureZeroToThree.t1,
  t2 = TraitColorsNatureZeroToThree.t2,
  t3 = TraitColorsNatureZeroToThree.t3,
  t4 = TraitColorsNatureZeroToThree.t4,
  t5 = TraitColorsNatureZeroToThree.t5,
  t6 = TraitColorsNatureZeroToThree.t6,
  t7 = TraitColorsNatureZeroToThree.t7,
  t8 = TraitColorsNatureZeroToThree.t8,
  t9 = TraitColorsNatureZeroToThree.t9,
  t10 = TraitColorsNatureZeroToThree.t10,
  t11 = TraitColorsNatureZeroToThree.t11,
  t12 = TraitColorsNatureZeroToThree.t12,
  disabledBranch = TraitColorsNatureZeroToThree.disabledBranch,
}
