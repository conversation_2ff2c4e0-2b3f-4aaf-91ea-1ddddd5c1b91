import { Component, Input } from '@angular/core';
import { SingleBarChartConfiguration } from '../single-bar-chart/single-bar-chart.component';

export interface SingleBarChartWithDescriptionConfiguration {
  description: string;
  barChartConfiguration: SingleBarChartConfiguration;
}

@Component({
  selector: 'app-single-bar-chart-with-description',
  templateUrl: './single-bar-chart-with-description.component.html',
  styleUrls: ['./single-bar-chart-with-description.component.scss'],
})
export class SingleBarChartWithDescriptionComponent {
  @Input() public configuration: SingleBarChartWithDescriptionConfiguration;
}
