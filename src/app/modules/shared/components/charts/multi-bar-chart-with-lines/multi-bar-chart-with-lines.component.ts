import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Chart } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeStyle } from '@angular/platform-browser';
import { LegendEntry } from '../chart-legend/chart-legend.component';
import { DataSetModel } from '../report-chart-models';

export interface MultiBarChartWithGridConfiguration {
  labelSuffix: string;
  barConfigurations: BarConfiguration[];
  color: string;
}

export interface BarConfiguration {
  value: number;
  label: string;
}

@Component({
  selector: 'app-multi-bar-chart-with-lines',
  templateUrl: './multi-bar-chart-with-lines.component.html',
  styleUrls: ['./multi-bar-chart-with-lines.component.scss'],
})
export class MultiBarChartWithLinesComponent implements AfterViewInit, OnInit {
  @Input() configuration: MultiBarChartWithGridConfiguration;
  @Input() title: string;
  @Input() header: string;
  @Input() description: string;
  @Input() footer: string;
  @Input() legendConfiguration: LegendEntry[];

  @ViewChild('chartCanvas', { static: true }) chartCanvas: ElementRef;
  @ViewChild('imgWrap', { static: false }) imgWrap: ElementRef;

  public isChartHidden = false;
  private gridColor = '#dbdbdb';
  private readonly emptyValue = 0.5;

  private maxBarThickness: number;
  private categoryPercentage: number;
  private barPercentage: number;
  private chartLabelFontSize: number;
  public labelSuffix: string;
  public chartStyle: SafeStyle;

  constructor(
    private renderer: Renderer2,
    private sanitizer: DomSanitizer,
  ) {}

  ngOnInit(): void {
    this.initChartConfig();
  }

  private initChartConfig(): void {
    this.maxBarThickness = 45;
    this.categoryPercentage = 0.8;
    this.barPercentage = 0.7;
    this.chartLabelFontSize = 10; // px
    this.labelSuffix = this.configuration.labelSuffix;
    this.chartStyle = this.sanitizer.bypassSecurityTrustStyle(
      'height: ' +
        (50 * this.configuration.barConfigurations.length).toString() +
        'px; width: 650px;',
    );
  }

  ngAfterViewInit(): void {
    this.renderChart();
  }

  renderChart(): void {
    const context = this;

    // tslint:disable-next-line:no-unused-expression
    new Chart(this.chartCanvas.nativeElement, {
      plugins: [ChartDataLabels],
      type: 'horizontalBar',
      data: this.generateDataForBarChart(),
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
          onComplete(): void {
            const barImage = this.toBase64Image();
            if (barImage !== 'data:,') {
              const img = context.renderer.createElement('img');
              img.src = barImage;
              context.renderer.appendChild(context.imgWrap.nativeElement, img);
              context.isChartHidden = true;
            }
          },
        },
        legend: {
          display: false,
        },
        scales: {
          yAxes: [
            {
              ticks: {
                fontColor: 'black',
                fontSize: this.chartLabelFontSize,
                padding: 20,
                // @ts-ignore
                callback: (value, index, ticks) => {
                  if (typeof value === 'string' && value.includes('\n')) {
                    return value.split('\n');
                  } else {
                    return value;
                  }
                },
              },
              position: 'right',
            },
          ],
          xAxes: [
            {
              ticks: {
                fontColor: this.gridColor,
                fontSize: this.chartLabelFontSize,
                padding: 20,
                min: 0,
                max: 100,
                maxTicksLimit: 3,
                callback: (value, index, ticks) => {
                  return `${value} ${context.labelSuffix}`;
                },
              },
              gridLines: {
                drawBorder: true,
                display: true,
                zeroLineColor: this.gridColor,
              },
            },
          ],
        },
        layout: {
          padding: {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
          },
        },
        plugins: {
          datalabels: {
            color: 'white',
            font: {
              size: 12,
              weight: 900,
            },
            formatter(value, _): number {
              // @ts-ignore
              return value === context.emptyValue ? '' : `${value} ${context.labelSuffix}`;
            },
          },
        },
      },
    });
  }

  generateDataForBarChart(): any {
    const data = this.generateDataSet();

    return {
      labels: this.configuration.barConfigurations.map((bar) => bar.label),
      datasets: data,
    };
  }

  generateDataSet(): DataSetModel[] {
    return [
      {
        barThickness: 'flex',
        maxBarThickness: this.maxBarThickness,
        barPercentage: this.barPercentage,
        categoryPercentage: this.categoryPercentage,
        data: this.configuration.barConfigurations.map((bar) =>
          bar.value === 0 ? this.emptyValue : bar.value,
        ),
        labels: [],
        backgroundColor: Array(this.configuration.barConfigurations.length).fill(
          this.configuration.color,
        ),
        borderColor: '',
        borderWidth: 0,
        borderSkipped: false,
      },
    ];
  }
}
