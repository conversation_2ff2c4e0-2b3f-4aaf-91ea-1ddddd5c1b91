import {
  AfterViewChecked,
  After<PERSON>iew<PERSON>nit,
  Component,
  ElementRef,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { D3PackedBubbleChartUtils } from 'src/app/utils/d3-packed-bubble-chart.utils';
import { Educator } from '../../../../../services/evaluation/models/educator';
import { Evaluation } from '../../../../../services/evaluation/models/evaluation';
import { ScaleDefinition } from '../../../../../services/evaluation/models/scale-definition';
import { Trait } from '../../../../../services/evaluation/models/trait';
import { ScaleService } from '../../../../../services/evaluation/scale.service';

@Component({
  selector: 'app-bubble-chart',
  templateUrl: './bubble-chart.component.html',
  styleUrls: ['./bubble-chart.component.scss'],
})
export class BubbleChartComponent implements OnInit, AfterViewChecked, AfterViewInit {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public traitColors: any;

  constructor(
    private translate: TranslateService,
    private scaleService: ScaleService,
  ) {}
  @Input() evaluation: Evaluation;
  @Input() title: string;
  @Input() educator?: Educator;
  @Input() scaleDefinition: ScaleDefinition;

  @ViewChild('bubbleChart', { static: false }) bubbleChart: ElementRef;

  ngOnInit(): void {
    this.initTraitColors();
  }

  initTraitColors(): void {
    this.traitColors = this.scaleService.getTraitColorsForScale(this.scaleDefinition);
  }

  ngAfterViewInit(): void {
    const scale = this.evaluation.scales.get(this.scaleDefinition);

    const bubbles = [];
    Array.from(scale.traits.values()).forEach((trait) => {
      const score = this.getScoreForTrait(trait);

      bubbles.push({
        name: this.translate.instant(trait.getBubbleChartTitleLocalizationKey()),
        count: score <= 0 ? 1 : score,
        color: this.getColorForTrait(trait, score),
      });
    });

    const dataset = {
      children: [
        {
          name: 'Educators',
          children: bubbles,
        },
      ],
    };

    D3PackedBubbleChartUtils.renderChart(dataset, this.bubbleChart.nativeElement, 700, 700);
  }

  ngAfterViewChecked(): void {
    D3PackedBubbleChartUtils.resizeSvg();
  }

  getScoreForTrait(trait: Trait): number {
    return this.educator ? trait.scores.get(this.educator.id()) : trait.teamScore;
  }

  getColorForTrait(trait: Trait, score: number): string {
    return score <= 0 ? this.traitColors.disabledBranch : this.traitColors[trait.id];
  }
}
