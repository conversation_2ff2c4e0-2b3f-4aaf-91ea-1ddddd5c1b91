<h3 *ngIf="this.title">{{ this.title }}</h3>

<p *ngIf="this.header">
  <i>{{ this.header }}</i>
</p>

<div [hidden]="isChartHidden" #chartContainer>
  <canvas height="{{ this.height }}" #chartCanvas></canvas>
</div>
<div #imgWrap class="{{ this.imageContainerClass }}"></div>
<div *ngIf="this.legendConfiguration">
  <div class="spacing-large"></div>
  <app-chart-legend [configuration]="this.legendConfiguration"></app-chart-legend>
</div>

<p *ngIf="this.footer">
  <i>{{ this.footer }}</i>
</p>
