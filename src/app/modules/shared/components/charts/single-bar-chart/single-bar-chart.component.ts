import { LegendEntry } from '../chart-legend/chart-legend.component';
import { AfterViewInit, Component, ElementRef, Input, Renderer2, ViewChild } from '@angular/core';
import { Chart } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { SegmentConfiguration } from '../report-chart-models';

export interface SingleBarChartConfiguration {
  data: SegmentConfiguration[];
  labelSuffix: string;
}

@Component({
  selector: 'app-single-bar-chart',
  templateUrl: './single-bar-chart.component.html',
  styleUrls: ['./single-bar-chart.component.scss'],
})
export class SingleBarChartComponent implements AfterViewInit {
  @Input() configuration: SingleBarChartConfiguration;
  @Input() height = 48;
  @Input() legendConfiguration: LegendEntry[];
  @Input() title: string;
  @Input() header: string;
  @Input() footer: string;

  @ViewChild('chartCanvas', { static: true }) chartCanvas: ElementRef;
  @ViewChild('imgWrap', { static: false }) imgWrap: ElementRef;
  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef;

  private chart: Chart;
  public readonly imageContainerClass = 'bar-chart-image-container';
  public isChartHidden = false;

  constructor(private renderer: Renderer2) {}

  ngAfterViewInit(): void {
    this.chart = new Chart(this.chartCanvas.nativeElement, {
      plugins: [ChartDataLabels],
      type: 'horizontalBar',
      data: this.generateData(),
      options: this.options(),
    });
  }

  private generateData(): any {
    const sum = this.configuration.data.reduce((p, c) => p + c.value, 0);
    return {
      label: ['data'],
      datasets: this.configuration.data
        .filter((c) => c.value !== 0)
        .map((c, i) => ({
          label: `${i}`,
          data: [c.value / sum],
          backgroundColor: c.color,
          barPercentage: 1,
        })),
    };
  }

  private options(): any {
    const context = this;

    return {
      responsive: true,
      animation: {
        onComplete(): void {
          const barImage = this.toBase64Image();
          if (barImage !== 'data:,') {
            const img = context.renderer.createElement('img');
            context.renderer.addClass(img, context.imageContainerClass);
            img.src = barImage;
            context.renderer.appendChild(context.imgWrap.nativeElement, img);
            context.isChartHidden = true;
          }
        },
      },
      tooltips: {
        enabled: false,
      },
      legend: {
        display: false,
      },
      layout: {
        padding: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
        },
      },
      scales: {
        xAxes: [
          {
            stacked: true,
            display: false,
          },
        ],
        yAxes: [
          {
            stacked: true,
            display: false,
          },
        ],
      },
      plugins: {
        datalabels: {
          align: 'center',
          anchor: 'center',
          color: 'white',
          backgroundColor: null,
          borderColor: null,
          borderRadius: 4,
          borderWidth: 1,
          clamp: true,
          font: {
            size: 12,
            weight: '900',
          },
          formatter: (value, _) => {
            const sum = this.configuration.data.reduce((p, c) => p + c.value, 0);
            return `${Math.floor(value * sum)}${this.configuration.labelSuffix}`;
          },
        },
      },
    };
  }
}
