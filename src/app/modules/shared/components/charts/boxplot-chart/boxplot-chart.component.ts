import { AfterViewInit, Component, ElementRef, Input, Renderer2, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Chart } from 'chart.js';
import * as BoxPlot from 'chartjs-chart-box-and-violin-plot';
import { EvaluationType } from '../../../../organization/organizations/utils';

export interface BoxplotChartConfiguration {
  data: {
    values: number[];
    color: string;
    label: string;
  }[];
  scaleY?: {
    min: number;
    max: number;
  };
}

@Component({
  selector: 'app-boxplot-chart',
  templateUrl: './boxplot-chart.component.html',
})
export class BoxplotChartComponent implements AfterViewInit {
  @Input() configuration: BoxplotChartConfiguration;
  @Input() title: string;
  @Input() header: string;
  @Input() description: string;
  @Input() footer: string;
  @Input() evaluationType: EvaluationType;

  @ViewChild('chartCanvas', { static: true }) chartCanvas: ElementRef;
  @ViewChild('imgWrap', { static: false }) imgWrap: ElementRef;

  private chart: Chart;
  public isChartHidden = false;

  constructor(
    private renderer: Renderer2,
    private translate: TranslateService,
  ) {}

  ngAfterViewInit(): void {
    this.chart = new Chart(this.chartCanvas.nativeElement, {
      plugins: [BoxPlot],
      type: 'boxplot',
      data: this.generateData(),
      options: this.options(),
    });
  }

  private generateData() {
    const { data } = this.configuration;
    return {
      labels: data.map((d) => d.label),
      datasets: [
        {
          barThickness: 40,
          backgroundColor: data.map((d) => d.color),
          borderColor: 'gray',
          outlierRadius: 0,
          borderWidth: 1,
          medianColor: 'black',
          itemRadius: 0,
          datalabels: {
            display: false,
          },
          data: data.map((d) => d.values),
        },
      ],
    };
  }

  private options() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const context = this;
    return {
      responsive: true,
      animation: {
        onComplete(): void {
          const barImage = this.toBase64Image();
          if (barImage !== 'data:,') {
            const img = context.renderer.createElement('img');
            context.renderer.addClass(img, 'boxplot-chart-image-container');
            img.src = barImage;
            context.renderer.appendChild(context.imgWrap.nativeElement, img);
            context.isChartHidden = true;
          }
        },
      },
      scales: {
        yAxes: [
          {
            ticks: {
              stepSize: 1,
              suggestedMin: this.configuration.scaleY ? this.configuration.scaleY.min : undefined,
              suggestedMax: this.configuration.scaleY ? this.configuration.scaleY.max : undefined,
              callback: function (value, index, ticks) {
                if (context.evaluationType === EvaluationType.EVALUATION) {
                  return ticks[index];
                } else {
                  switch (ticks[index]) {
                    case 1:
                      return context.translate
                        .instant('childrenEvaluation.report.barChartLabel.disagreeCompletely')
                        .split('\n');
                    case 2:
                      return context.translate
                        .instant('childrenEvaluation.report.barChartLabel.disagreeToModerateDegree')
                        .split('\n');
                    case 3:
                      return context.translate
                        .instant('childrenEvaluation.report.barChartLabel.agreeToModerateDegree')
                        .split('\n');
                    case 4:
                      return context.translate
                        .instant('childrenEvaluation.report.barChartLabel.agreeCompletely')
                        .split('\n');
                    default:
                      return value;
                  }
                }
              },
            },
          },
        ],
      },
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
    };
  }
}
