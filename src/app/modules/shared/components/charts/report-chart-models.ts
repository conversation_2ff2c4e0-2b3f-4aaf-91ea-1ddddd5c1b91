export interface SegmentConfiguration {
  value: number;
  color: string;
}

export interface DataSetsModel {
  labels: string[];
  datasets: DataSetModel[];
}

export interface DataSetModel {
  barPercentage: number;
  categoryPercentage: number;
  data: number[];
  labels: string[];
  backgroundColor: string[];
  barThickness: string;
  maxBarThickness: number;
  borderColor: string;
  borderWidth: number;
  borderSkipped: boolean;
}
