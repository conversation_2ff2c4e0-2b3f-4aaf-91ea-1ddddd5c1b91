import { AfterViewInit, Component, ElementRef, Input, Renderer2, ViewChild } from '@angular/core';
import { SegmentConfiguration } from '../report-chart-models';
import { Chart } from 'chart.js';

export interface DoughnutChartConfiguration {
  data: SegmentConfiguration[];
  labelSuffix: string;
  description: DoughnutChartDescription;
}

interface DoughnutChartDescription {
  duration: string;
  locations: number;
  answers?: number;
}

@Component({
  selector: 'app-donut-chart',
  templateUrl: './donut-chart.component.html',
  styleUrls: ['./donut-chart.component.scss'],
})
export class DoughnutChartComponent implements AfterViewInit {
  @Input() configuration: DoughnutChartConfiguration;
  @Input() height: number;

  @ViewChild('chartCanvas', { static: true }) chartCanvas: ElementRef;
  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef;
  @ViewChild('imgWrap', { static: false }) imgWrap: ElementRef;

  private chart: Chart;
  public readonly imageContainerClass = 'bar-chart-image-container';
  public isChartHidden = false;

  constructor(private renderer: Renderer2) {}

  ngAfterViewInit(): void {
    this.renderChart();
  }

  renderChart(): void {
    const context = this;

    const { data, labelSuffix } = this.configuration;
    const dataExists = data.length !== 0;
    this.chart = new Chart(this.chartCanvas.nativeElement, {
      type: 'doughnut',
      data: this.getDataSets(),
      options: {
        responsive: true,
        maintainAspectRatio: true,
        cutoutPercentage: 60,
        animation: {
          onComplete(): void {
            const barImage = this.toBase64Image();
            if (barImage !== 'data:,') {
              const img = context.renderer.createElement('img');
              context.renderer.addClass(img, context.imageContainerClass);
              img.src = barImage;
              context.renderer.appendChild(context.imgWrap.nativeElement, img);
              context.isChartHidden = true;
            }
          },
        },
        tooltips: {
          enabled: false,
        },
        legend: {
          display: false,
        },
        plugins: {
          datalabels: {
            color: 'white',
            font: {
              size: 16,
              weight: 'bold',
            },
            formatter: (value) => {
              return dataExists ? value + labelSuffix : '';
            },
          },
        },
      },
    });
  }

  getDataSets(): any {
    const { data } = this.configuration;
    let datasets: any[];
    let labels: any[];
    if (!data.length) {
      labels = ['no data'];
      datasets = [
        {
          data: [100],
          borderWidth: 0,
        },
      ];
    } else {
      labels = data.map(() => 'data');
      datasets = [
        {
          data: data.filter((d) => d.value !== 0).map((d) => d.value),
          backgroundColor: data.map((d) => d.color),
        },
      ];
    }
    return {
      labels,
      datasets,
    };
  }
}
