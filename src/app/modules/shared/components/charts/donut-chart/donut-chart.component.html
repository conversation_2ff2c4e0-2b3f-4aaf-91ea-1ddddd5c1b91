<div class="doughnut-chart-container">
  <div [hidden]="isChartHidden" #chartContainer>
    <canvas height="{{ this.height }}" #chartCanvas></canvas>
  </div>
  <div #imgWrap class="{{ this.imageContainerClass }}"></div>
  <div class="doughnut-chart-content">
    <span class="doughnut-chart-content-header">{{ this.configuration.description.duration }}</span>
    <span class="doughnut-chart-content-description"
      >{{ this.configuration.description.locations }}
      {{ 'reports.survey.ABB_015.locations' | translate }}</span
    >
    <span class="doughnut-chart-content-description"
      >{{ this.configuration.description.answers }}
      {{ 'reports.survey.ABB_015.family_answers' | translate }}</span
    >
  </div>
</div>
