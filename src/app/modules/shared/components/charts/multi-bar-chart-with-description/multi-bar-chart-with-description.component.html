<h3 *ngIf="title">{{ title }}</h3>

<p *ngIf="header">
  <i>{{ header }}</i>
</p>

<p *ngIf="description">
  {{ description }}
</p>

<div class="multi-bar-chart-entry-container" *ngFor="let config of configuration">
  <app-single-bar-chart-with-description
    [configuration]="config"
  ></app-single-bar-chart-with-description>
</div>
<div *ngIf="legendConfiguration">
  <div class="spacing-large"></div>
  <app-chart-legend [configuration]="legendConfiguration"></app-chart-legend>
</div>

<p *ngIf="footer">
  <i>{{ footer }}</i>
</p>
