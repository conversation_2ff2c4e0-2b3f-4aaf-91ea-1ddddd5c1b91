import { LegendEntry } from '../chart-legend/chart-legend.component';
import { Component, Input } from '@angular/core';
import { SingleBarChartWithDescriptionConfiguration } from '../single-bar-chart-with-description/single-bar-chart-with-description.component';

@Component({
  selector: 'app-multi-bar-chart-with-description',
  templateUrl: './multi-bar-chart-with-description.component.html',
  styleUrls: ['./multi-bar-chart-with-description.component.scss'],
})
export class MultiBarChartWithDescriptionComponent {
  @Input() public configuration: SingleBarChartWithDescriptionConfiguration[];
  @Input() legendConfiguration: LegendEntry[];
  @Input() title: string;
  @Input() header: string;
  @Input() description: string;
  @Input() footer: string;
}
