import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-input-field',
  templateUrl: './input-field.component.html',
  styleUrls: ['./input-field.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: InputFieldComponent,
      multi: true,
    },
  ],
})
export class InputFieldComponent implements ControlValueAccessor {
  @Input() type: string;
  @Input() placeholder: string;
  @Input() invalid = false;
  @Input() inputValue: string;
  @Output() onFocus: EventEmitter<void> = new EventEmitter<void>();
  @Output() onBlur: EventEmitter<void> = new EventEmitter<void>();

  private onChange = (value: string) => {};

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {}

  writeValue(value: string): void {
    this.onChange(value);
  }

  onIonFocus(): void {
    this.onFocus.emit();
  }

  onIonBlur(): void {
    this.onBlur.emit();
  }

  onInputChange(value: string): void {
    this.inputValue = value;
    this.onChange(value);
  }
}
