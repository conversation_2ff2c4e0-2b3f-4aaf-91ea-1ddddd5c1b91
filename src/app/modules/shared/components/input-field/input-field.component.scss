@import './../../../../../theme/variables_v2';

.input-container {
  border: solid 1px white;
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 16px;

  ::ng-deep {
    ion-input {
      height: 56px;
      color: white;
      border: none;
      margin: 0;
      background-color: transparent;

      .input-highlight.sc-ion-input-md {
        display: none;
      }

      > input {
        --placeholder-color: var(--ion-color-placeholder-V2);
        --placeholder-opacity: 1;
        margin-bottom: 0;
      }
    }
  }

  &.invalid {
    border-color: $color-danger;
  }
}
