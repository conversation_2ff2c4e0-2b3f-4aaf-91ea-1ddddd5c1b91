import { Component, EventEmitter, Input, Output } from '@angular/core';
import { PrimaryButtonType } from '../primary-button/primary-button.component';

@Component({
  selector: 'app-empty-view',
  templateUrl: './empty-view.component.html',
  styleUrls: ['./empty-view.component.scss'],
})
export class EmptyViewComponent {
  @Input() title: string;
  @Input() text: string;
  @Input() imagePath: string;
  @Input() buttonLabel: string;
  @Output() onReload = new EventEmitter<void>();

  public buttonType = PrimaryButtonType;

  onReloadClick(): void {
    this.onReload.emit();
  }
}
