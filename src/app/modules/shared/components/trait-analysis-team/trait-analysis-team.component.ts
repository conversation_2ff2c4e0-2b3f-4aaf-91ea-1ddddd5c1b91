import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Educa<PERSON> } from '../../../../services/evaluation/models/educator';
import { Trait } from '../../../../services/evaluation/models/trait';
import { ScaleDefinition } from '../../../../services/evaluation/models/scale-definition';
import { Branch } from '../../../../services/evaluation/models/branch';
import { ColorTargets, getColorSchemeFor } from '../../../../utils/color-scheme-for-scale';

@Component({
  selector: 'app-trait-analysis-team',
  templateUrl: './trait-analysis-team.component.html',
  styleUrls: ['./trait-analysis-team.component.scss'],
})
export class TraitAnalysisTeamComponent implements OnInit {
  @Input() educators: Educator[];
  @Input() trait: Trait;
  @Input() scale: ScaleDefinition;

  public branches: Branch[];

  public traitTitle: string;
  public traitExplanation: string;

  constructor(private translate: TranslateService) {}

  ngOnInit(): void {
    this.init();
  }

  init(): void {
    this.traitTitle = this.translate.instant(this.trait.localizationKey());
    this.traitExplanation = this.translate.instant(this.trait.getExplanationLocalizationKey());
    this.branches = Array.from(this.trait.branches.values());
  }

  getBranchExplanation(branch: Branch): string {
    const key = `${branch.localizationKeyPrefix()}.analysis.explanation`;
    return this.translate.instant(key);
  }

  getColorClass(branch: Branch): string {
    return getColorSchemeFor(ColorTargets.TRAIT_BG_COLOR, this.scale, branch.trait);
  }
}
