import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Trait } from '../../../../services/evaluation/models/trait';
import { ScaleDefinition } from '../../../../services/evaluation/models/scale-definition';
import { EvaluationViewModel } from '../../../../pages/evaluator-analysis/evaluator-analysis.page';
import { Educator } from '../../../../services/evaluation/models/educator';
import { ComparisonReportEvaluationBarChartParams } from '../../../reports/shared-report-components/comparison-report-evaluations/comparison-report-evaluations.component';
import { Branch } from '../../../../services/evaluation/models/branch';
import { ColorTargets, getColorSchemeFor } from '../../../../utils/color-scheme-for-scale';

@Component({
  selector: 'app-trait-analysis-educator-comparison',
  templateUrl: './trait-analysis-educator-comparison.component.html',
  styleUrls: ['./trait-analysis-educator-comparison.component.scss'],
})
export class TraitAnalysisEducatorComparisonComponent implements OnInit {
  @Input() trait: Trait;
  @Input() scale: ScaleDefinition;
  @Input() evaluations: EvaluationViewModel[];
  @Input() educator: Educator[];
  @Input() comparisonReportParams?: ComparisonReportEvaluationBarChartParams | undefined;
  public branches: Branch[];

  public traitTitle: string;
  public traitExplanation: string;

  constructor(private translate: TranslateService) {}

  ngOnInit(): void {
    this.init();
  }

  init(): void {
    this.traitTitle = this.translate.instant(this.trait.localizationKey());
    this.traitExplanation = this.translate.instant(this.trait.getExplanationLocalizationKey());
    this.branches = Array.from(this.trait.branches.values());
  }

  getBranchExplanation(branch: Branch): string {
    const key = `${branch.localizationKeyPrefix()}.analysis.explanation`;
    return this.translate.instant(key);
  }

  getColorClass(branch: Branch): string {
    return getColorSchemeFor(ColorTargets.TRAIT_BG_COLOR, this.scale, branch.trait);
  }
}
