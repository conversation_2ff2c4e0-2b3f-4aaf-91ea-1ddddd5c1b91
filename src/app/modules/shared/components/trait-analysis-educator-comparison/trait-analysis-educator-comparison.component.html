<ion-grid>
  <ion-row>
    <ion-col>
      <ion-text class="trait-title page-break">{{ traitTitle }}</ion-text>
      <br />
      <ion-text class="trait-explanation">{{ traitExplanation }}</ion-text>
      <app-bar-chart
        [trait]="trait"
        [educators]="educator"
        [evaluations]="evaluations"
        [scale]="scale"
        [comparisonReportParams]="comparisonReportParams"
      ></app-bar-chart>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <div>
        <div class="branch-container" *ngFor="let branch of branches">
          <div [ngClass]="getColorClass(branch)">
            <ion-text class="branch-title">{{ branch.localizationKey() | translate }}</ion-text>
          </div>
          <ion-text class="branch-explanation">{{ getBranchExplanation(branch) }}</ion-text>
        </div>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
