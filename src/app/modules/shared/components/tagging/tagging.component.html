<ion-grid class="form-spacing">
  <ion-row>
    <ion-col class="ion-no-padding">
      <app-input-with-heading-and-suggestions
        type="text"
        [label]="'create_tag.title' | translate"
        [suggestions]="suggestions"
        (inputChanged)="fetchSuggestions($event)"
        (submit)="addTag($event)"
      ></app-input-with-heading-and-suggestions>
    </ion-col>
    <ion-col></ion-col>
  </ion-row>
  <div class="sized-box"></div>
  <ion-row>
    <ion-col class="ion-no-padding">
      <ion-chip *ngFor="let tag of tags" class="custom-chip">
        <ion-label>{{ tag }}</ion-label>
        <img
          src="../../../../../assets/svg/ic_cancel.svg"
          alt="open"
          class="menu-toggle"
          (click)="removeTag(tag)"
        />
      </ion-chip>
    </ion-col>
  </ion-row>
</ion-grid>
