import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AdminService } from '../../../../services/admin/admin.service';

@Component({
  selector: 'app-tagging',
  templateUrl: './tagging.component.html',
  styleUrls: ['./tagging.component.scss'],
})
export class TaggingComponent {
  @Input() tags: string[] = [];
  @Output() tagsChanged = new EventEmitter<string[]>();

  public suggestions: string[] = [];

  constructor(
    public translate: TranslateService,
    private adminService: AdminService,
  ) {}

  public removeTag(tag: string): void {
    const index = this.tags.indexOf(tag);
    if (index !== -1) {
      this.tags.splice(index, 1);
    }
    this.tagsChanged.emit(this.tags);
  }

  public addTag(tag: string): void {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
      this.tagsChanged.emit(this.tags);
    }
    this.clearSuggestions();
  }

  public fetchSuggestions(tag: string): void {
    if (tag === '' || tag === null) {
      this.clearSuggestions();
      return;
    }
    this.adminService.retrieveTagsLike(tag).subscribe(async (result) => {
      if (result.success) {
        this.suggestions = result.data.result.sort((a, b) => a.localeCompare(b));
      } else {
        this.clearSuggestions();
      }
    });
  }

  private clearSuggestions(): void {
    this.suggestions = [];
  }
}
