import { Component, Input } from '@angular/core';
import { SpssFilterDetailPage } from '../../../../pages/admin/spss-filter-detail/spss-filter-detail.page';

@Component({
  selector: 'app-sub-filter-list-tile',
  templateUrl: './sub-filter-list-tile.component.html',
  styleUrls: ['./sub-filter-list-tile.component.scss'],
})
export class SubFilterListTileComponent {
  @Input() title: string;
  @Input() description: string;
  @Input() parent: SpssFilterDetailPage;
  @Input() isUserListTile: boolean;

  onClick(): void {
    this.isUserListTile
      ? this.parent.removeUser(this.title)
      : this.parent.changeSelectedState(this.title);
  }
}
