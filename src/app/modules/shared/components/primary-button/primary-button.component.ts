import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-primary-button',
  templateUrl: './primary-button.component.html',
  styleUrls: ['./primary-button.component.scss'],
})
export class PrimaryButtonComponent {
  @Input() label: string;
  @Input() isLoading = false;
  @Input() isDisabled = false;
  @Input() type: string;
  @Input() expand: string;
  @Input() buttonType: PrimaryButtonType = PrimaryButtonType.BRIGHT;
  @Input() border = false;
  @Input() small = false;
  @Input() destructive = false;
  // tslint:disable-next-line:no-output-on-prefix
  @Output() onClick?: EventEmitter<void> = new EventEmitter<void>();

  performClickAction(): void {
    this.onClick.emit();
  }

  getButtonScssClass(): string {
    let cssClass = '';
    cssClass += this.border ? 'border-radius' : '';
    cssClass += this.small ? ' small' : '';
    cssClass += this.destructive ? ' destructive' : '';
    return cssClass;
  }

  getButtonContainerStyleClass(): string {
    if (this.isDisabled) {
      return 'button-container disabled';
    }
    const baseClass = 'button-container';
    switch (this.buttonType) {
      case PrimaryButtonType.BRIGHT:
        return baseClass;
      case PrimaryButtonType.DARK:
        return baseClass + ' inverted';
      case PrimaryButtonType.GRADIENT:
        return baseClass + ' background-gradient';
      default:
        return baseClass;
    }
  }
}

export enum PrimaryButtonType {
  BRIGHT,
  DARK,
  GRADIENT,
}
