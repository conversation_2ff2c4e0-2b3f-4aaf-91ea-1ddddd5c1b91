@import './../../../../../theme/variables_v2';

.background-gradient {
  ion-button {
    --background: linear-gradient(
      to top right,
      var(--ion-color-gradient-lila-V2),
      var(--ion-color-gradient-blue-V2),
      var(--ion-color-gradient-yellow-V2)
    );

    &:hover {
      --background-hover: linear-gradient(
        to top right,
        var(--ion-color-gradient-lila-V2),
        var(--ion-color-gradient-blue-V2),
        var(--ion-color-gradient-yellow-V2)
      );
    }

    //both a required (LongPress and click)
    &:active {
      --background-activated: linear-gradient(
        to top right,
        var(--ion-color-gradient-lila-V2),
        var(--ion-color-gradient-blue-V2),
        var(--ion-color-gradient-yellow-V2)
      );
    }
    --background-activated: linear-gradient(
      to top right,
      var(--ion-color-gradient-lila-V2),
      var(--ion-color-gradient-blue-V2),
      var(--ion-color-gradient-yellow-V2)
    );
  }
}

.button-container {
  height: 56px;
  width: 100%;
  display: flex;
  align-items: center;
  &:hover {
    text-decoration: underline;
  }

  ::ng-deep {
    ion-button {
      --background: white;
      height: 100%;
      width: 100%;
      margin: 0;
      --border-radius: 8px;

      &.border-radius {
        --background: transparent;
        border: 1px solid $color-light-gray;
        border-radius: 8px;
      }

      &:hover {
        --background-hover: white;
      }

      //both a required (LongPress and click)
      &:active {
        --background-activated: white;
      }
      --background-activated: white;

      &.small {
        height: 2rem;
      }

      ion-spinner {
        color: var(--ion-color-primary-V2);
      }

      ion-text {
        color: var(--ion-color-primary-V2);
        font-size: 16px;
        line-height: 22px;
        font-weight: 800;
        padding-left: 48px;
        padding-right: 48px;
        letter-spacing: 0.5px;
      }

      &.destructive {
        ion-text {
          color: var(--ion-color-danger-V2);
        }
      }
    }
  }

  &.inverted {
    ::ng-deep {
      ion-button {
        --background: var(--ion-color-primary-V2);
        --border-radius: 8px;
        border: 1px solid white;
        border-radius: 8px;

        &.border-radius {
          border: 1px solid white;
          border-radius: 8px;
        }

        &:hover {
          --background-hover: var(--ion-color-primary-V2);
        }

        //both a required (LongPress and click)
        &:active {
          --background-activated: var(--ion-color-primary-V2);
        }
        --background-activated: var(--ion-color-primary-V2);

        ion-spinner {
          color: white;
        }

        ion-text {
          color: white;
          font-size: 16px;
          line-height: 22px;
          font-weight: 800;
          padding-left: 48px;
          padding-right: 48px;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  &.disabled {
    &:hover {
      text-decoration: none !important;
    }

    ::ng-deep {
      ion-button {
        --background: var(--ion-color-light-grey-V2);

        ion-text {
          color: var(--ion-color-dark-grey-V2);
        }
      }
    }
  }
}
