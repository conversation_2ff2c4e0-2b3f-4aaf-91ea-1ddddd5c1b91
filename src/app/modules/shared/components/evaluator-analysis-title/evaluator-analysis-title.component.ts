import { Component, Input } from '@angular/core';
import { ScaleDefinition } from '../../../../services/evaluation/models/scale-definition';
import { ScaleService } from '../../../../services/evaluation/scale.service';
import { getScaleTypeFromDefinition } from '../../../../services/evaluation/models/scale-type-definition';

@Component({
  selector: 'app-evaluator-analysis-title',
  templateUrl: './evaluator-analysis-title.component.html',
  styleUrls: ['./evaluator-analysis-title.component.scss'],
})
export class EvaluatorAnalysisTitleComponent {
  @Input() scale: ScaleDefinition;
  @Input() title: string;
  @Input() customerServiceId: string;
  @Input() name?: string;
  @Input() date: string;
  @Input() hasPageBreakBefore?: boolean;
  @Input() isAggregated: boolean;

  constructor(public scaleService: ScaleService) {}

  getTitleFromScale(): string {
    return this.scaleService.getFullScaleName(getScaleTypeFromDefinition(this.scale));
  }
}
