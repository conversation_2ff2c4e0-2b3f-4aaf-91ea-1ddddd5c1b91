<div class="master-data-container" [ngClass]="hasPageBreakBefore ? 'page-break' : ''">
  <div class="logo"></div>
  <div class="scale">{{ getTitleFromScale() }}</div>
  <div *ngIf="!isAggregated" class="institution">{{ title }}</div>
  <div *ngIf="!isAggregated" class="info">{{ customerServiceId }}</div>
  <div class="info">
    <span *ngIf="name">{{ name }}</span>
    <span *ngIf="name && !isAggregated"> | </span>
    <span *ngIf="!isAggregated">{{ date }}</span>
  </div>
</div>
