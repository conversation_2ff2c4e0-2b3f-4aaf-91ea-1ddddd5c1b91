import { Inject, InjectionToken, Optional, Pipe, PipeTransform } from '@angular/core';
import { DateTime } from 'luxon';

const formats = {
  short: DateTime.DATE_SHORT,
  medium: DateTime.DATE_MED,
  full: DateTime.DATE_FULL,
  huge: DateTime.DATE_HUGE,
  yearOnly: 'YEAR_ONLY',
};

export const ISO_PIPE_DEFAULT_LOCALE = new InjectionToken<string>('DATE_PIPE_DEFAULT_TIMEZONE');

@Pipe({
  name: 'isoDate',
  standalone: true,
})
export class IsoDatePipe implements PipeTransform {
  constructor(@Inject(ISO_PIPE_DEFAULT_LOCALE) @Optional() private defaultLocale?: string | null) {}

  /**
   * Usage `{{ value_expression | isoDate [ : locale [ : format ] ] }}`
   * @param isoDate date as ISO-string ('yyyy-MM-dd')
   * @param locale defaults to 'de'
   * @param format defaults to 'medium'
   * @returns Localized formatted date string
   */
  transform(
    isoDate: string,
    locale = 'de',
    format: 'short' | 'medium' | 'full' | 'huge' | 'yearOnly' = 'medium',
  ): string {
    const parsedDate = DateTime.fromISO(isoDate);
    const currentLocale = this.defaultLocale ?? locale;

    if (parsedDate.isValid) {
      if (format === 'yearOnly') {
        return parsedDate.setLocale(currentLocale).toLocaleString({ year: 'numeric' });
      } else {
        return parsedDate.setLocale(currentLocale).toLocaleString(formats[format]);
      }
    } else {
      return '';
    }
  }
}
