import { LOCATION_INITIALIZED } from '@angular/common';
import {
  HttpClient,
  HttpClientModule,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import { APP_INITIALIZER, ErrorHandler, Injector, NgModule } from '@angular/core';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouteReuseStrategy } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import {
  MissingTranslationHandler,
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { createErrorHandler } from '@sentry/angular';
import { Settings } from 'luxon';
import moment from 'moment';
import { environment } from '../environments/environment';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AdminNavBarComponent } from './modules/organization/admin-nav-bar/admin-nav-bar.component';
import { AssociateEvaluationModalPageModule } from './modules/organization/organizations/associate-evaluation-modal/associate-evaluation-modal.module';
import { ReportSelectionErrorModalPageModule } from './modules/organization/organizations/report-selection-error-modal/report-selection-error-modal.module';
import { SurveyFormComponent } from './modules/organization/survey-form/survey-form.component';
import { SharedModule } from './modules/shared/shared.module';
import { SurveyModule } from './modules/survey/survey.module';
import { UserService } from './services/user/user.service';
import { GraziasMissingTranslationHandler } from './utils/missingTranslationHandler';
import { unauthorizedInterceptor } from './unauthorized.interceptor';

export function servicesFactory(
  userService: UserService,
  translate: TranslateService,
  injector: Injector,
) {
  return async () => {
    await new Promise<void>((resolve) => {
      const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve(null));
      locationInitialized.then(() => {
        const locale = translate.getBrowserLang();
        const defaultLocale = 'en';
        translate.setDefaultLang(defaultLocale);

        moment.locale(locale);
        Settings.defaultLocale = locale; // Luxon

        translate.use(locale).subscribe({
          next: () => {},
          error: (err) => {
            console.error(`Problem with '${locale}' language initialization. Error: '${err}'`);
            resolve();
          },
          complete: () => {
            resolve();
          },
        });
      });
    });

    userService.init();
  };
}

@NgModule({
  declarations: [AppComponent, AdminNavBarComponent, SurveyFormComponent],
  imports: [
    BrowserModule,
    IonicModule.forRoot(),
    SharedModule,
    AppRoutingModule,
    HttpClientModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient],
      },
    }),
    BrowserAnimationsModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    SurveyModule,
    AssociateEvaluationModalPageModule,
    ReportSelectionErrorModalPageModule,
  ],
  providers: [
    {
      provide: MissingTranslationHandler,
      useClass: GraziasMissingTranslationHandler,
    },
    ScreenOrientation,
    { provide: ErrorHandler, useValue: createErrorHandler() },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    {
      provide: APP_INITIALIZER,
      useFactory: servicesFactory,
      deps: [UserService, TranslateService, Injector],
      multi: true,
    },
    provideHttpClient(withInterceptors([unauthorizedInterceptor])),
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}

export function createTranslateLoader(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http, `${environment.webBaseUrl}/assets/i18n/`, '.json');
}
