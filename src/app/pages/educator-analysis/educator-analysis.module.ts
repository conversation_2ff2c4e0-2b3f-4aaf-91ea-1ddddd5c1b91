import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { EducatorAnalysisPage } from './educator-analysis.page';
import { TranslateModule } from '@ngx-translate/core';
import { GraziasMissingTranslationHandler } from '../../utils/missingTranslationHandler';
import { SharedModule } from '../../modules/shared/shared.module';

const routes: Routes = [
  {
    path: '',
    component: EducatorAnalysisPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
    SharedModule,
  ],
  declarations: [EducatorAnalysisPage],
})
export class EducatorAnalysisPageModule {}
