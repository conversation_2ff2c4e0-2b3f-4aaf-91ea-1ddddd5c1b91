import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { EvaluationService } from 'src/app/services/evaluation/evaluation.service';
import { PrintService, PrintStyles } from 'src/app/services/print.service';
import { Educator } from '../../services/evaluation/models/educator';
import { Evaluation } from '../../services/evaluation/models/evaluation';
import { ScaleDefinition } from '../../services/evaluation/models/scale-definition';
import { Trait } from '../../services/evaluation/models/trait';
import { ScaleService } from '../../services/evaluation/scale.service';
import { EvaluationViewModel } from '../evaluator-analysis/evaluator-analysis.page';

@Component({
  selector: 'app-educator-analysis',
  templateUrl: './educator-analysis.page.html',
  styleUrls: ['./educator-analysis.page.scss'],
})
export class EducatorAnalysisPage {
  @ViewChild('printSection', { read: ElementRef, static: false }) printSection: ElementRef;
  public evaluation = new EvaluationViewModel(new Evaluation(), this.translate);
  public isLoading = false;
  public isError = false;
  public errorMessage = '';
  public traits: Array<Trait> = [];
  public educators: Array<Educator> = [];

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private evaluationService: EvaluationService,
    private translate: TranslateService,
    private printService: PrintService,
    private loadingController: LoadingController,
    public scaleService: ScaleService,
  ) {}

  ionViewWillEnter(): void {
    this.fetchRouteParams();
  }

  fetchRouteParams(): void {
    this.route.params.subscribe((param) => {
      this.translate.use(param.lang).subscribe(() => {
        this.http.get(`./assets/i18n/${param.lang}.json`).subscribe((translationFile) => {
          this.translate.setTranslation(param.lang, translationFile);
          this.loadEvalResults(param.accessId, param.evaluationRemoteId);
        });
      });
    });
  }

  async loadEvalResults(accessId: string, evaluationRemoteId: string): Promise<void> {
    if (this.isLoading) {
      return;
    }

    this.isLoading = true;

    const loadingUi = await this.loadingController.create({
      message: '',
    });
    await loadingUi.present();

    this.evaluationService
      .getSelfEvaluationResult(accessId, evaluationRemoteId)
      .subscribe((resp) => {
        if (resp.success) {
          resp.data.educators.forEach((it) => this.educators.push(it));
          resp.data.scales
            .values()
            .next()
            .value.traits.forEach((trait) => {
              this.traits.push(trait);
            });
          this.evaluation = new EvaluationViewModel(resp.data, this.translate);
        } else {
          this.isError = true;
          this.errorMessage = this.translate.instant('global.error.generic');
        }

        this.isLoading = false;
        loadingUi.dismiss();
      });
  }

  customPrint(): void {
    const printContent = this.printSection.nativeElement;
    this.printService.openBrowserPrintDialog(printContent, PrintStyles.EVALUATOR_ANALYSIS);
  }

  isGraziasScale(scale: ScaleDefinition): boolean {
    return scale.includes('GRAZIAS');
  }

  getTraitsForScale(scaleDef: ScaleDefinition): Array<Trait> {
    return Array.from(this.evaluation.evaluation.getScaleFromDefinition(scaleDef).traits.values());
  }
}
