<ion-content *ngIf="isError">{{ errorMessage }}</ion-content>
<ion-content *ngIf="!isError && !isLoading && educators !== undefined && educators.length > 0">
  <ion-header no-border>
    <ion-toolbar>
      <div class="print-bttn-container">
        <ion-button size="large" class="print-bttn" (click)="customPrint()"
          >{{'global.button.print' | translate}}</ion-button
        >
      </div>
    </ion-toolbar>
  </ion-header>

  <ion-grid #printSection class="screen-container">
    <div
      *ngFor="let scaleDef of evaluation.evaluation.getSortedScaleDefinitions(); let index=index;"
    >
      <ion-row>
        <ion-col size="12">
          <div *ngFor="let educator of educators">
            <app-evaluator-analysis-title
              [scale]="scaleDef"
              [title]="evaluation.institutionName"
              [customerServiceId]="evaluation.customerServicesId"
              [name]="educator.fullName()"
              [date]="evaluation.dateOfSurveyFormatted"
              [hasPageBreakBefore]="index !== 0"
            ></app-evaluator-analysis-title>
            <app-bubble-chart
              *ngIf="isGraziasScale(scaleDef)"
              [title]="scaleService.getScaleAndAgeLocalizedString(scaleDef, evaluation.evaluation)"
              [evaluation]="evaluation.evaluation"
              [educator]="educator"
              [scaleDefinition]="scaleDef"
            ></app-bubble-chart>
            <div *ngFor="let trait of getTraitsForScale(scaleDef)">
              <app-trait-analysis
                [trait]="trait"
                [educators]="[educator]"
                [scale]="scaleDef"
              ></app-trait-analysis>
            </div>
          </div>
        </ion-col>
      </ion-row>
    </div>
  </ion-grid>
</ion-content>
