import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NavController } from '@ionic/angular';

export enum PolicySource {
  CHILD_EVALUATION = 'CHILD_EVALUATION',
  SURVEYS = 'SURVEYS',
  SELF_EVALUATION = 'SELF_EVALUATION',
}

@Component({
  selector: 'app-terms',
  templateUrl: './terms.page.html',
  styleUrls: ['./terms.page.scss'],
})
export class TermsPage implements OnInit {
  translationSource: string | undefined;

  constructor(
    public translate: TranslateService,
    private route: ActivatedRoute,
    private navCtrl: NavController,
  ) {}

  async navigateBack(): Promise<void> {
    await this.navCtrl.pop();
  }

  ngOnInit(): void {
    const translationSourceParam = this.route.snapshot.paramMap.get('source');
    if (PolicySource[translationSourceParam] !== undefined) {
      this.translationSource = this.getTranslationKeyFromSource(
        PolicySource[translationSourceParam],
      );
    }
  }

  private getTranslationKeyFromSource(source: PolicySource): string {
    switch (source) {
      case PolicySource.CHILD_EVALUATION:
        return 'privacyPolicy.childEvaluation.content';
      case PolicySource.SURVEYS:
        return 'privacyPolicy.surveys.content';
      case PolicySource.SELF_EVALUATION:
        return 'privacyPolicy.selfEvaluation.content';
    }
  }
}
