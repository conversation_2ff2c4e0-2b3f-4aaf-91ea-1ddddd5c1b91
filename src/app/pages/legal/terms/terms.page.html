<ion-content *ngIf="translationSource">
  <ion-header>
    <ion-toolbar>
      <ion-title class="page-title">
        <a (click)="navigateBack()">
          <img class="back-image" src="assets/img/ic_back.png" [alt]="'global.back' | translate" />
        </a>
        {{ 'privacyPolicy.title' | translate }}</ion-title
      >
    </ion-toolbar>
  </ion-header>

  <ion-grid>
    <ion-row>
      <ion-col>
        <div class="container" [innerHTML]="translationSource | translate"></div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>

<ion-content *ngIf="!translationSource">
  <app-not-found [message]="'privacyPolicy.notFound' | translate"> </app-not-found>
</ion-content>
