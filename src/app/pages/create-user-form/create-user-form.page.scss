:host {
  overflow: hidden;
}

.radio {
  margin-right: 1rem;
  display: inline-block;
  height: 1rem;
  width: 1rem;
  background: url('/assets/img/ic_radiobutton_no_green.png') top center / contain no-repeat;

  &.activated {
    background: url('/assets/img/ic_radiobutton_yes_green.png') top center / contain no-repeat;
  }
}

.radio-container {
  margin-bottom: 1rem;
}

.container {
  margin: 1rem 2.5rem;
}

.form-field-title {
  display: block;
  text-transform: uppercase;
  color: var(--ion-color-primary);
  font-size: 0.7rem;

  &.disabled {
    color: var(--ion-color-medium);
  }
}

.title {
  margin-bottom: 2rem;
  font-weight: 700;
}

.sub-title {
  font-weight: 700;
  line-height: 40px;
}

.margin-bottom {
  margin-bottom: 1.5rem;
}

.back-image {
  width: 20px;
  cursor: pointer;
}

.lock {
  margin-left: 1rem;
  float: right;
  height: 1.375rem;
  width: 1.375rem;
  background: url('/assets/svg/ic_lock_closed.svg') top center / contain no-repeat;

  &.unlocked {
    background: url('/assets/svg/ic_lock_open.svg') top center / contain no-repeat;
  }
}

.short-input {
  display: inline-block;
  width: calc(100% - 2.4rem);
}

.radio-txt-compensation {
  margin-left: -1rem;
}

.form-field-container {
  margin-bottom: 1rem;

  &.first {
    margin-top: 2rem;
  }
}

.page-padding {
  padding-left: 45px;
  padding-right: 45px;
  padding-bottom: 20px;
}

.content {
  height: calc(100vh - 128px);
}
