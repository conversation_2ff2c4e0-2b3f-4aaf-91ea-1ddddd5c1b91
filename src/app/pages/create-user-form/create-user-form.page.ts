import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { AlertController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { RouteStateService } from 'src/app/services/RouteStateService';
import { AdminService } from 'src/app/services/admin/admin.service';
import { UserAdministrationOption, UserListItemViewModel } from 'src/app/utils/utils';

enum UserRole {
  ADMIN_USER = 'ADMIN_USER',
  APP_USER = 'APP_USER',
}

export class DeleteEvalOrAdminUserRequest {
  userName: string;
}

export class CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
}

export class UpdateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  currentUserName: string;
  role: UserRole;
}
@Component({
  selector: 'app-create-user-form',
  templateUrl: './create-user-form.page.html',
  styleUrls: ['./create-user-form.page.scss'],
})
export class CreateUserFormPage {
  constructor(
    private translate: TranslateService,
    private formBuilder: UntypedFormBuilder,
    private adminService: AdminService,
    private toastController: ToastController,
    private navController: NavController,
    private routeStateService: RouteStateService,
    private alertController: AlertController,
  ) {
    this.createUserForm = this.formBuilder.group({
      role: ['', [Validators.required]],
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      mail: ['', [Validators.required, Validators.email]],
    });
    this.getRouteState();
  }
  public createUserForm: UntypedFormGroup;
  public userRole = UserRole;
  public adminToggleClass = 'radio';
  public evaluatorToggleClass = 'radio activated';
  public submitBttnText = '';
  public pageTitle = '';
  public requiredLabel = '';
  public userName = '';
  public trackingId = '';

  private routeParams: UserListItemViewModel;
  private mode: UserAdministrationOption;
  private currentMailAddress: string;

  getRouteState(): void {
    this.routeStateService.getAdminState().subscribe((state) => {
      this.routeParams = state.user;
      this.mode = state.mode;
      if (state.mode === UserAdministrationOption.UPDATE) {
        this.currentMailAddress = state.user.mailAddress;
        this.trackingId = state.user.trackingId;
        this.initUpdateMode();
      } else if (state.mode === UserAdministrationOption.CREATE) {
        this.initCreateMode();
      }
    });
  }

  initUpdateMode(): void {
    this.loadUserData();
    this.submitBttnText = this.translate.instant('admin.createUserForm.button.update');
    this.pageTitle = this.translate.instant('admin.createUserForm.updateTitle');
  }

  initCreateMode(): void {
    this.requiredLabel = '*';
    this.submitBttnText = this.translate.instant('admin.createUserForm.button.create');
    this.pageTitle = this.translate.instant('admin.createUserForm.createTitle');
    this.setEvaluatorRole();
  }

  loadUserData(): void {
    this.createUserForm.controls.firstName.setValue(this.routeParams.firstName);
    this.createUserForm.controls.lastName.setValue(this.routeParams.lastName);
    this.createUserForm.controls.mail.setValue(this.routeParams.mailAddress);
    this.userName = this.routeParams.userName;

    if (this.routeParams.role === UserRole.ADMIN_USER) {
      this.setAdminRole();
    } else {
      this.setEvaluatorRole();
    }
  }

  submitUserForm(): void {
    if (this.createUserForm.valid) {
      this.isCreateUserMode() ? this.createUser() : this.updateUser();
    } else {
      this.showToast(
        this.translate.instant('admin.createUserForm.message.invalidForm'),
        'danger',
      );
    }
  }

  async showDeleteConfirmation(): Promise<void> {
    const alert = await this.alertController.create({
      header: this.translate.instant('admin.createUserForm.label.deleteUser'),
      message: this.translate.instant('admin.createUserForm.message.deleteUser'),
      buttons: [
        {
          text: this.translate.instant('admin.createUserForm.button.cancel'),
          role: 'cancel',
        },
        {
          text: this.translate.instant('admin.createUserForm.button.deleteUser'),
          handler: () => {
            this.deleteUser();
          },
        },
      ],
      backdropDismiss: false,
    });

    await alert.present();
  }

  private createUser(): void {
    const data = new CreateUserRequest();
    data.firstName = this.createUserForm.value.firstName;
    data.lastName = this.createUserForm.value.lastName;
    data.email = this.createUserForm.value.mail;
    data.role = this.createUserForm.value.role;
    this.adminService.createUser(data).subscribe(async (result) => {
      if (result.success) {
        this.showToast(
          this.translate.instant('admin.createUserForm.message.createUserSuccess'),
          'primary',
        );
        this.navigateAdminDashboard();
      } else {
        const message = result.errorMessage
          ? result.errorMessage
          : this.translate.instant('admin.createUserForm.message.createUserFailure');
        this.showToast(message, 'danger');
      }
    });
  }

  private updateUser(): void {
    const data = new UpdateUserRequest();
    data.firstName = this.createUserForm.value.firstName;
    data.lastName = this.createUserForm.value.lastName;
    data.email = this.createUserForm.value.mail;
    data.currentUserName = this.userName;
    data.role = this.createUserForm.value.role;
    this.adminService.updateUser(data).subscribe(async (result) => {
      if (result.success) {
        const message =
          this.routeParams.mailAddress +
          ' ' +
          this.translate.instant('admin.createUserForm.message.editUserSuccess');
        this.showToast(message, 'primary');
        this.navigateAdminDashboard();
      } else {
        const message = result.errorMessage
          ? result.errorMessage
          : this.translate.instant('admin.createUserForm.message.editUserFailure');
        this.showToast(message, 'danger');
      }
    });
  }
  private deleteUser(): void {
    const data = new DeleteEvalOrAdminUserRequest();
    data.userName = this.userName;
    this.adminService.anonymizeEvalOrAdminUser(data).subscribe(async (result) => {
      if (result.success) {
        const message =
          this.routeParams.mailAddress +
          ' ' +
          this.translate.instant('admin.createUserForm.message.deleteUserSuccess');
        this.showToast(message, 'primary');
        this.navigateAdminDashboard();
      } else {
        const message = result.errorMessage
          ? result.errorMessage
          : this.translate.instant('admin.createUserForm.message.deleteUserFailure');
        this.showToast(message, 'danger');
      }
    });
  }

  toggleRadio(target: UserRole): void {
    switch (target) {
      case UserRole.ADMIN_USER:
        this.setAdminRole();
        break;
      case UserRole.APP_USER:
        this.setEvaluatorRole();
        break;
    }
  }

  setAdminRole(): void {
    this.createUserForm.controls.role.setValue(this.userRole.ADMIN_USER);
    this.adminToggleClass = 'radio activated';
    this.evaluatorToggleClass = 'radio';
  }

  setEvaluatorRole(): void {
    this.createUserForm.controls.role.setValue(this.userRole.APP_USER);
    this.adminToggleClass = 'radio';
    this.evaluatorToggleClass = 'radio activated';
  }

  async showToast(message: string, color: string): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color,
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    toast.present();
  }

  navigateAdminDashboard(): void {
    this.navController.navigateRoot('admin/dashboard');
  }

  isCreateUserMode(): boolean {
    return this.mode === UserAdministrationOption.CREATE;
  }
}
