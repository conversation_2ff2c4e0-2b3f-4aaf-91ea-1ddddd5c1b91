<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateAdminDashboard()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{ 'admin.dashboard.title' | translate }}</b>
      </ion-row>
      <ion-title>{{ pageTitle }}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content class="design-v2 content">
  <div class="page-padding">
    <ion-grid>
      <form [formGroup]="createUserForm">
        <ion-row>
          <ion-col size="6" class="container">
            <h1 class="title">{{ 'admin.createUserForm.label.userData' | translate }}</h1>
            <ion-grid class="ion-no-padding">
              <ion-row class="radio-container" (click)="toggleRadio(userRole.ADMIN_USER)">
                <ion-col class="ion-no-padding" size="1">
                  <div [ngClass]="adminToggleClass"></div>
                </ion-col>
                <ion-col class="ion-no-padding" size="11">
                  <ion-text class="radio-txt-compensation">
                    {{ 'admin.createUserForm.label.admin' | translate }}
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="radio-container" (click)="toggleRadio(userRole.APP_USER)">
                <ion-col class="ion-no-padding" size="1">
                  <div [ngClass]="evaluatorToggleClass"></div>
                </ion-col>
                <ion-col class="ion-no-padding" size="11">
                  <ion-text class="radio-txt-compensation"
                  >{{ 'admin.createUserForm.label.appUser' | translate }}
                  </ion-text
                  >
                </ion-col>
              </ion-row>
            </ion-grid>

            <div class="form-field-container first">
              <div *ngIf="!isCreateUserMode()">
                <span class="sub-title">{{ 'global.username' | translate }}: </span>
                <span>{{ userName }}</span>
              </div>
              <div *ngIf="!isCreateUserMode()" class="margin-bottom">
                <span class="sub-title">{{ 'global.label.trackingId' | translate }}: </span>
                <span>{{ trackingId }}</span>
              </div>

              <ion-text class="form-field-title">
                {{ 'admin.createUserForm.label.firstName' | translate }} *
              </ion-text>
              <ion-input
                [ngClass]="isCreateUserMode()? '' : 'short-input'"
                formControlName="firstName"
                inputMode="text"
                type="text"
                [placeholder]="'admin.createUserForm.label.firstName' | translate"
              ></ion-input>
            </div>

            <div class="form-field-container">
              <ion-text class="form-field-title"
              >{{ 'admin.createUserForm.label.lastName' | translate }} *
              </ion-text
              >
              <ion-input
                [ngClass]="isCreateUserMode()? '' : 'short-input'"
                formControlName="lastName"
                inputMode="text"
                type="text"
                [placeholder]="'admin.createUserForm.label.lastName' | translate"
              ></ion-input>
            </div>

            <div class="form-field-container">
              <ion-text class="form-field-title"
              >{{ 'admin.createUserForm.label.mail' | translate }} *
              </ion-text
              >
              <ion-input
                [ngClass]="isCreateUserMode()? '' : 'short-input'"
                formControlName="mail"
                inputMode="text"
                type="email"
                [placeholder]="'admin.createUserForm.label.mail' | translate"
              ></ion-input>
            </div>

            <ion-grid class="ion-no-padding ion-padding-top">
              <ion-row class="ion-no-padding ion-justify-content-between">
                <ion-col class="ion-no-padding" size="5.5">
                  <ion-button
                    class="form-bttn full-width cancel"
                    (click)="navigateAdminDashboard()"
                    *ngIf="isCreateUserMode()"
                  >
                    {{ 'admin.createUserForm.button.cancel' | translate }}
                  </ion-button>
                  <ion-button
                    class="form-bttn full-width delete"
                    (click)="showDeleteConfirmation()"
                    *ngIf="!isCreateUserMode()"
                  >
                    {{ 'admin.createUserForm.button.deleteUser' | translate }}
                  </ion-button>
                </ion-col>
                <ion-col class="ion-no-padding" size="5.5">
                  <ion-button class="form-bttn full-width" (click)="submitUserForm()"
                  >{{ submitBttnText }}
                  </ion-button
                  >
                </ion-col>
              </ion-row>
            </ion-grid>
          </ion-col>
        </ion-row>
      </form>
    </ion-grid>
  </div>
</ion-content>
