import { Component, OnInit } from '@angular/core';
import { LoadingController, NavController } from '@ionic/angular';
import { NavigationExtras } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Utils } from '../../../utils/utils';
import { AdminService } from '../../../services/admin/admin.service';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.page.html',
  styleUrls: ['./projects.page.scss'],
})
export class ProjectsPage implements OnInit {
  public loadingUi: HTMLIonLoadingElement = null;

  public projects: Array<Project> = [];

  public loading = false;

  constructor(
    private navController: NavController,
    private adminService: AdminService,
    public loadingController: LoadingController,
    public translate: TranslateService,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.fetchProjects();
  }

  async fetchProjects(): Promise<void> {
    this.loading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getAvailableProjects().subscribe((response) => {
      if (response.success) {
        this.projects = response.data.projects.map((value) => new Project(value.id, value.name));
      }
      this.loadingUi.dismiss();
      this.loading = false;
    });
  }

  navigateDetail(project: Project): void {
    const navigationExtras: NavigationExtras = {
      queryParams: {
        project,
        projectDetailMode:
          project != undefined ? ProjectDetailMode.UPDATE : ProjectDetailMode.CREATE,
      },
    };
    this.navController.navigateRoot('admin/project-detail', navigationExtras);
  }
}

export enum ProjectDetailMode {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
}

export class Project {
  id: number;
  name: string;

  constructor(id: number, name: string) {
    this.id = id;
    this.name = name;
  }
}
