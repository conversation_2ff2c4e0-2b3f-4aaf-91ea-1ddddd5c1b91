@import 'src/theme/_variables_v2.scss';

.flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
}

.main-content {
  height: 100%;
}

.content {
  height: calc(100vh - 128px);
}

.list-entry {
  height: 55px;
  border: solid $color-light-gray 1px;
  border-radius: 8px;
  display: flex;
  margin-top: 8px;
  padding: 5px 16px;
  align-items: center;

  .navigation-hitbox {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .horizontal-spacer {
    width: 10px;
  }
}

.page-padding {
  padding-left: 94px;
  padding-right: 94px;
  padding-bottom: 20px;
}
