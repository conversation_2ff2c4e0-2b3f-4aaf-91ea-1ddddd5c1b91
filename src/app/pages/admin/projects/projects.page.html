<div class="main-content">
  <ion-header class="header-v2">
    <ion-toolbar class="toolbar-v2">
      <div class="flex">
        <ion-title>{{ 'projects.title' | translate }}</ion-title>
        <app-primary-button
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="navigateDetail(null)"
          [isDisabled]="false"
          [isLoading]="false"
          [label]="'projects.newProject' | translate | uppercase"
        >
        </app-primary-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <ion-content class="design-v2 content">
    <div class="page-padding">
      <div *ngIf="projects.length > 0 || loading; else emptyView">
        <div *ngFor="let project of projects" class="list-entry ion-activatable ripple-parent">
          <div class="navigation-hitbox" (click)="navigateDetail(project)">
            <span class="name">{{ project.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </ion-content>
</div>

<ng-template #emptyView>
  <app-empty-view
    [title]="'organization.empty_view.title' | translate"
    [text]="'projects.dashboard' | translate"
    imagePath="assets/svg/projects.svg"
  ></app-empty-view>
</ng-template>
