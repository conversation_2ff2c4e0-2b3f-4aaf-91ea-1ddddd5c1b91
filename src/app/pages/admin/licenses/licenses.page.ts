import { Component, OnInit } from '@angular/core';
import { AppUserLicense, LicenseService } from '../../../services/license/license.service';
import { LoadingController, NavController, ToastController } from '@ionic/angular';
import { NavigationExtras } from '@angular/router';
import moment from 'moment';
import { TranslateService } from '@ngx-translate/core';
import { Utils } from '../../../utils/utils';

@Component({
  selector: 'app-licenses',
  templateUrl: './licenses.page.html',
  styleUrls: ['./licenses.page.scss'],
})
export class LicensesPage implements OnInit {
  public loadingUi: HTMLIonLoadingElement = null;
  public loading = false;
  public licenses: Array<License> = [];
  public noLicensesAvailableError = false;
  public filterElements: Array<FilterElement> = [];

  constructor(
    private licenseService: LicenseService,
    private navController: NavController,
    private toastController: ToastController,
    public loadingController: Loading<PERSON>ontroller,
    public translate: TranslateService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.initFilter();
    await this.fetchLicenses(LicensesFilter.ALL);
  }

  async fetchLicenses(filter: LicensesFilter): Promise<void> {
    this.loading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.licenseService.getLicenses(filter).subscribe((result) => {
      if (result.success) {
        this.licenses = [];
        result.data.licenses.forEach((it) => this.licenses.push(new License(it, this.translate)));
        this.licenses = this.getLicensesSortedAfterExpirationDate(this.licenses);
      } else {
        this.noLicensesAvailableError = true;
      }
      this.loadingUi.dismiss();
      this.loading = false;
    });
  }

  initFilter(): void {
    this.filterElements.push(
      new FilterElement(this.translate.instant('global.all'), LicensesFilter.ALL, true),
    );
    this.filterElements.push(
      new FilterElement(
        this.translate.instant('childrenEvaluation.license.generation_type.auto'),
        LicensesFilter.AUTO_GENERATED,
        false,
      ),
    );
    this.filterElements.push(
      new FilterElement(
        this.translate.instant('childrenEvaluation.license.generation_type.manually'),
        LicensesFilter.CREATED_BY_ADMIN,
        false,
      ),
    );
  }

  navigateDetail(license?: AppUserLicense): void {
    const licenseDetailMode =
      license == undefined ? LicenseDetailMode.CREATE : LicenseDetailMode.UPDATE;
    const navigationExtras: NavigationExtras = {
      queryParams: {
        license,
        licenseDetailMode,
      },
    };
    this.navController.navigateRoot('admin/license-detail', navigationExtras);
  }

  toggleFilter(filter: LicensesFilter): void {
    const currentSelection = this.filterElements.filter((it) => {
      return it.isActive;
    });

    if (currentSelection[0].type !== filter) {
      this.filterElements.forEach((it) => {
        it.isActive = it.type === filter;
      });
      this.fetchLicenses(filter);
    }
  }

  copyToClipboard(license: License): void {
    if (
      typeof license.licenseKey === 'string' &&
      license.licenseKey !== '' &&
      license.licenseKey != null
    ) {
      Utils.copyToClipboard(license.licenseKey);
      this.showMessage(this.translate.instant('global.copied'), true);
    }
  }

  private async showMessage(message: string, success: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color: success ? 'success' : 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }

  private getLicensesSortedAfterExpirationDate(licenses: Array<License>): Array<License> {
    function compare(a, b) {
      if (a.primarySortingPriority < b.primarySortingPriority) {
        return -1;
      }
      if (a.primarySortingPriority > b.primarySortingPriority) {
        return 1;
      }

      if (a.currentState === LicenseState.FUTURE && b.currentState === LicenseState.FUTURE) {
        return moment(a.validFrom).isBefore(b.validFrom) ? 1 : -1;
      } else {
        return moment(a.validUntil).isBefore(b.validUntil) ? -1 : 1;
      }
    }

    return licenses.sort(compare);
  }
}

export enum LicensesFilter {
  ALL = 'ALL',
  AUTO_GENERATED = 'AUTO_GENERATED',
  CREATED_BY_ADMIN = 'CREATED_BY_ADMIN',
}

export enum LicenseDetailMode {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
}

class FilterElement {
  name: string;
  type: LicensesFilter;
  isActive: boolean;

  constructor(name: string, type: LicensesFilter, isActive: boolean) {
    this.name = name;
    this.type = type;
    this.isActive = isActive;
  }
}

class License extends AppUserLicense {
  now: number;
  schedulingImagePath: string;
  expirationMessage: string;
  generationType: string;
  primarySortingPriority: number;
  currentState: LicenseState;

  constructor(
    license: AppUserLicense,
    public translate: TranslateService,
  ) {
    super(
      license.id,
      license.name,
      license.licenseKey,
      license.totalActivations,
      license.remainingActivations,
      license.validFrom,
      license.validUntil,
      license.autoGenerated,
      license.childrenEvaluationData,
      license.externalEvaluationData,
      license.selfEvaluationData,
    );

    this.now = Date.now();
    this.schedulingImagePath = this.initLicenseState();
    this.expirationMessage = this.initExpirationMessage();
    this.generationType = this.autoGenerated
      ? this.translate.instant('childrenEvaluation.license.generation_type.auto')
      : this.translate.instant('childrenEvaluation.license.generation_type.manually');
  }

  private initLicenseState(): string {
    if (moment(this.validUntil).isBefore(moment(this.now))) {
      this.primarySortingPriority = 2;
      this.currentState = LicenseState.CLOSED;
      return 'assets/svg/state_license_closed.svg';
    }
    if (
      moment(this.validFrom).isBefore(moment(this.now)) &&
      moment(this.validUntil).isAfter(moment(this.now))
    ) {
      this.primarySortingPriority = 1;
      this.currentState = LicenseState.ACTIVE;
      return 'assets/svg/state_license_ongoing.svg';
    }
    if (moment(this.validFrom).isAfter(moment(this.now))) {
      this.primarySortingPriority = 0;
      this.currentState = LicenseState.FUTURE;
      return 'assets/svg/state_license_future.svg';
    } else {
      return '';
    }
  }

  private initExpirationMessage(): string {
    if (moment(this.validFrom).isAfter(moment(this.now))) {
      return this.translate.instant('childrenEvaluation.license.expiration.from', {
        date: moment(this.validFrom).format('DD.MM.yyyy'),
      });
    }

    if (
      moment(this.validUntil).isBefore(moment(this.now).add(14, 'days')) &&
      moment(this.validUntil).isAfter(moment(this.now))
    ) {
      const duration = moment.duration(moment(this.validUntil).diff(moment(this.now)));
      const daysBetween = duration.add(1, 'days').days();
      return daysBetween === 1
        ? this.translate.instant('childrenEvaluation.license.expiration.days.one')
        : this.translate.instant('childrenEvaluation.license.expiration.days.more', {
            days: daysBetween,
          });
    }

    if (moment(this.validUntil).isAfter(moment(this.now).add(14, 'days'))) {
      return this.translate.instant('childrenEvaluation.license.expiration.until', {
        date: moment(this.validUntil).format('DD.MM.yyyy'),
      });
    }

    return '';
  }
}

enum LicenseState {
  ACTIVE = 'ACTIVE',
  FUTURE = 'FUTURE',
  CLOSED = 'CLOSED',
}
