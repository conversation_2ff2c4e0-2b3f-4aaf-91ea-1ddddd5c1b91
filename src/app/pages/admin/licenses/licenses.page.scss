@import 'src/theme/_variables_v2.scss';

.flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
}

.main-content {
  height: 100%;
}

.content {
  height: calc(100vh - 128px);
}

.list-entry {
  height: 55px;
  border: solid $color-light-gray 1px;
  border-radius: 8px;
  display: flex;
  margin-top: 8px;
  padding: 5px 0;
  align-items: center;

  .navigation-hitbox {
    display: flex;
    align-items: center;
    width: 95%;
    cursor: pointer;

    .list-icon {
      margin-left: 8px;
      margin-right: 15px;
      width: 35px;
      height: 35px;
    }

    .name {
      width: 50%;
    }

    .expiration,
    .generated {
      width: 25%;
    }
  }

  .horizontal-spacer {
    width: 10px;
  }

  .copy-icon {
    width: 5%;
    display: flex;
    justify-content: flex-end;
    cursor: pointer;

    ion-img {
      margin-right: 12px;
      height: 22px;
      width: 22px;
    }
  }
}

.page-padding {
  padding-left: 94px;
  padding-right: 94px;
  padding-bottom: 20px;
}

.custom-chip {
  color: var(--ion-color-primary-V2);
  background-color: $color-info-box-background;
  border-radius: 7px;
  margin: 12px 12px 8px 0;
  padding: 0 16px;
  height: 48px;

  ion-label {
    font-size: 16px;
  }

  &.active {
    background-color: $color-primary-v2;
    color: white;
  }
}

.ripple-parent {
  position: relative;
  overflow: hidden;
}
