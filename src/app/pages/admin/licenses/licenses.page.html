<div class="main-content">
  <ion-header class="header-v2">
    <ion-toolbar class="toolbar-v2">
      <div class="flex">
        <ion-title>{{ 'navigation.licenses' | translate }}</ion-title>
        <app-primary-button
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="navigateDetail(null)"
          [isDisabled]="false"
          [isLoading]="false"
          [label]="'childrenEvaluation.license.new' | translate | uppercase"
        >
        </app-primary-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <ion-content class="design-v2 content">
    <div class="page-padding">
      <ion-chip
        *ngFor="let filter of filterElements"
        [ngClass]="filter.isActive ? 'custom-chip active' : 'custom-chip'"
        (click)="toggleFilter(filter.type)"
      >
        <ion-label>{{ filter.name }}</ion-label>
      </ion-chip>
      <div *ngIf="(licenses.length > 0 && !noLicensesAvailableError) || loading; else emptyView">
        <div *ngFor="let license of licenses" class="list-entry ion-activatable ripple-parent">
          <div class="navigation-hitbox" (click)="navigateDetail(license)">
            <ion-img class="list-icon" [src]="license.schedulingImagePath"></ion-img>
            <span class="name">{{ license.name }}</span>
            <span class="horizontal-spacer"></span>
            <span class="expiration">{{ license.expirationMessage }}</span>
            <span class="generated">{{ license.generationType }} </span>
          </div>
          <div class="copy-icon" (click)="copyToClipboard(license)">
            <ion-img src="assets/svg/copy.svg"></ion-img>
          </div>
          <ion-ripple-effect type="unbounded"></ion-ripple-effect>
        </div>
      </div>
    </div>
  </ion-content>
</div>

<ng-template #emptyView>
  <app-empty-view
    [title]="'organization.empty_view.title' | translate"
    [text]="'childrenEvaluation.license.empty_view.text' | translate"
    imagePath="assets/svg/license.svg"
  ></app-empty-view>
</ng-template>
