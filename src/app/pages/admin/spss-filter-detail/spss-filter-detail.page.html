<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'spssFilter.title' | translate}}</b>
      </ion-row>
      <ion-title>{{'spssFilter.title' | translate}}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content #content class="design-v2 content">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col>
        <form [formGroup]="filterForm" class="input-container">
          <ion-grid class="form-spacing">
            <ion-row>
              <ion-col size="5.5">
                <app-dropdown
                  formControlName="type"
                  [label]="'spssFilter.evaluation_type' | translate"
                  [options]="getAllEvaluationTypeOptions()"
                  [placeholder]="'mobileAppV2.generic.select' | translate"
                  [errorMessage]="evaluationTypeErrorMessage"
                >
                </app-dropdown>
              </ion-col>
              <ion-col size="1"></ion-col>
              <ion-col size="5.5">
                <app-dropdown
                  class="{{isExternalEvaluationSelected ? '' : 'hide-element'}}"
                  formControlName="scaleType"
                  [label]="'spssFilter.scale_type' | translate"
                  [options]="getAllScaleTypeOptions()"
                  [placeholder]="'mobileAppV2.generic.select' | translate"
                  [errorMessage]="scaleTypeErrorMessage"
                >
                </app-dropdown>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="5.5">
                <app-datepicker-with-heading
                  formControlName="validFrom"
                  [label]="'spssFilter.valid_from' | translate"
                  [errorMessage]="validFromErrorMessage"
                  [hasMaxDate]="true"
                >
                </app-datepicker-with-heading>
              </ion-col>
              <ion-col size="1"></ion-col>
              <ion-col size="5.5">
                <app-datepicker-with-heading
                  formControlName="validUntil"
                  [label]="'spssFilter.valid_until' | translate"
                  [errorMessage]="validUntilErrorMessage"
                  [hasMaxDate]="true"
                >
                </app-datepicker-with-heading>
              </ion-col>
            </ion-row>

            <ion-col size="5.5">
              <ion-card-title class="{{isEvaluationTypeSelected ? '' : 'hide-element'}}">
                <b>{{'spssFilter.projects_header' | translate}}</b>
              </ion-card-title>
              <ion-col size="1"></ion-col>
              <ion-col
                size="5.5"
                class="{{isEvaluationTypeSelected ? '' : 'hide-element'}}"
                *ngFor="let project of getProjectFilterList()"
              >
                <app-sub-filter-list-tile
                  [title]="project.name"
                  [parent]="this"
                  [isUserListTile]="false"
                ></app-sub-filter-list-tile>
              </ion-col>
              <ion-col size="1"></ion-col>
              <ion-col size="5.5" class="{{isEvaluationTypeSelected ? '' : 'hide-element'}}">
                <app-dropdown
                  formControlName="project"
                  [options]="getAvailableProjectOptions()"
                  [placeholder]="'mobileAppV2.generic.select' | translate"
                  [displaySelected]="false"
                >
                </app-dropdown>
              </ion-col>
            </ion-col>

            <div class="horizontal-spacer"></div>
            <ion-col size="5.5">
              <ion-card-title class="{{isEvaluationTypeSelected ? '' : 'hide-element'}}">
                <b>{{'spssFilter.user_header' | translate}}</b>
              </ion-card-title>
              <ion-col
                size="5.5"
                class="{{isEvaluationTypeSelected ? '' : 'hide-element'}}"
                *ngFor="let user of getSelectedUsers()"
              >
                <app-sub-filter-list-tile
                  [description]="getFullName(user.firstName, user.lastName)"
                  [title]="user.trackingId"
                  [parent]="this"
                  [isUserListTile]="true"
                ></app-sub-filter-list-tile>
              </ion-col>
              <ion-col size="5.5" class="{{isEvaluationTypeSelected ? '' : 'hide-element'}}">
                <app-primary-button
                  expand="block"
                  (onClick)="openPopup()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'spssFilter.add.user' | translate | uppercase"
                >
                </app-primary-button>
              </ion-col>
            </ion-col>

            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col size="6"></ion-col>
              <ion-col size="3">
                <app-primary-button
                  expand="block"
                  (onClick)="navigateBack()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'global.button.cancel' | translate"
                >
                </app-primary-button>
              </ion-col>
              <ion-col size="3">
                <app-primary-button
                  type="submit"
                  expand="block"
                  (onClick)="submitForm()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [label]="'global.create' | translate"
                  [buttonType]="buttonType.GRADIENT"
                >
                </app-primary-button>
              </ion-col>
            </ion-row>
            <div class="bottom-safe-area"></div>
          </ion-grid>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
