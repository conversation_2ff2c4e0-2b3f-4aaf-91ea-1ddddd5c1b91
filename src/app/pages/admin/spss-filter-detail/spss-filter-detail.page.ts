import { Component, OnInit } from '@angular/core';
import { UntypedForm<PERSON>uilder, Validators } from '@angular/forms';
import { Loading<PERSON>ontroller, ModalController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { DateTime } from 'luxon';
import { DataExportType, getDataExportTypeTranslationKey } from 'src/app/model/DataExportType';
import { FormValidationHelper } from '../../../modules/organization/helpers/form-validation-helper';
import { AddUserPopUpComponent } from '../../../modules/shared/components/add-user-pop-up/add-user-pop-up.component';
import type { Option } from '../../../modules/shared/components/dropdown/Option';
import { PrimaryButtonType } from '../../../modules/shared/components/primary-button/primary-button.component';
import {
  AdminService,
  DataExportFilterRequest,
  UserResponse,
} from '../../../services/admin/admin.service';
import { Utils } from '../../../utils/utils';
import {
  FilterEvaluationType,
  getEvaluationTypeTranslationKey,
} from '../spss-filter/spss-filter.page';

@Component({
  selector: 'app-spss-filter-detail',
  templateUrl: './spss-filter-detail.page.html',
  styleUrls: ['./spss-filter-detail.page.scss'],
})
export class SpssFilterDetailPage implements OnInit {
  constructor(
    public translate: TranslateService,
    private navController: NavController,
    private formBuilder: UntypedFormBuilder,
    private adminService: AdminService,
    private toastController: ToastController,
    private modalController: ModalController,
    public loadingController: LoadingController,
  ) {}

  public buttonType = PrimaryButtonType;
  public validFromErrorMessage?: string;
  public validUntilErrorMessage?: string;
  public evaluationTypeErrorMessage?: string;
  public scaleTypeErrorMessage?: string;

  public availableProjects: ProjectUiModel[];
  public selectedUsers: UserResponse[] = [];

  public isEvaluationTypeSelected = false;
  public isExternalEvaluationSelected = false;
  public isSelfEvaluationSelected = false;
  public loading = false;

  public loadingUi: HTMLIonLoadingElement = null;
  public filterForm = this.formBuilder.group({
    type: ['', [Validators.required]],
    validFrom: [''],
    validUntil: [''],
    project: [''],
    scaleType: [''],
  });

  async ngOnInit(): Promise<void> {
    await this.fetchProjects();
    this.getAllEvaluationTypeOptions();
    this.getAllScaleTypeOptions();
    this.filterForm.controls.project.valueChanges.subscribe((selectedProject) => {
      this.changeSelectedState(selectedProject);
    });
    this.filterForm.controls.type.valueChanges.subscribe((evaluationType) => {
      this.evaluationTypeErrorMessage = undefined;
      this.onFilterConditionsChanged(evaluationType);
    });
    this.filterForm.controls.scaleType.valueChanges.subscribe(() => {
      this.scaleTypeErrorMessage = undefined;
    });
  }

  private validateFields(): void {
    const start = new Date(this.filterForm.controls.validFrom.value);
    const end = new Date(this.filterForm.controls.validUntil.value);

    if (start >= end) {
      this.filterForm.setErrors({ range: true });
      this.validFromErrorMessage = this.translate.instant(
        'generic.error.endDateCantBeLargerThanStart',
      );
      this.validUntilErrorMessage = '';
    } else {
      this.filterForm.setErrors(null);
      this.evaluationTypeErrorMessage = FormValidationHelper.handleValidationError(
        this.filterForm.controls.type,
      );
      if (this.isExternalEvaluationSelected) {
        this.scaleTypeErrorMessage = FormValidationHelper.handleValidationError(
          this.filterForm.controls.scaleType,
        );
      } else {
        this.scaleTypeErrorMessage = undefined;
      }
    }
  }

  private async showMessage(
    shouldReturn: boolean,
    message: string,
    success: boolean,
  ): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color: success ? 'success' : 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }

  private async onFilterConditionsChanged(inputType: string): Promise<void> {
    this.loading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.isEvaluationTypeSelected = inputType.toLowerCase().includes('evaluation');
    this.isExternalEvaluationSelected = inputType.toLowerCase() === 'external_evaluation';
    this.isSelfEvaluationSelected = inputType.toLowerCase() === 'self_evaluation';
    if (this.isExternalEvaluationSelected) {
      this.filterForm.controls.scaleType.setValidators([Validators.required]);
    } else {
      this.filterForm.controls.scaleType.clearValidators();
    }
    this.filterForm.controls.scaleType.updateValueAndValidity();
    this.loading = false;
    await this.loadingUi.dismiss();
  }

  private async fetchProjects(): Promise<void> {
    this.loading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getAvailableProjects().subscribe((response) => {
      if (response.success) {
        this.availableProjects = response.data.projects.map(
          (value) => new ProjectUiModel(value.id, value.name, false),
        );
      }
    });
    this.loading = false;
    await this.loadingUi.dismiss();
  }

  private createDataExportFilterRequest(): DataExportFilterRequest {
    const projectIds =
      this.availableProjects.filter((project) => project.isSelected).map((project) => project.id) ??
      [];
    const trackingIds = this.getSelectedUsers().map((user) => user.trackingId) ?? [];
    const scaleType = this.isExternalEvaluationSelected
      ? this.filterForm.controls.scaleType.value
      : this.isSelfEvaluationSelected
        ? DataExportType.SELF_EVALUATION_GRAZIAS_V2.valueOf()
        : DataExportType.CHILDREN_EVALUATION.valueOf();

    const dateFrom =
      this.filterForm.controls.validFrom.value !== ''
        ? DateTime.fromISO(this.filterForm.controls.validFrom.value.toISOString()).toISODate()
        : null;

    const dateUntil =
      this.filterForm.controls.validUntil.value !== ''
        ? DateTime.fromISO(this.filterForm.controls.validUntil.value.toISOString()).toISODate()
        : null;

    return new DataExportFilterRequest(
      dateFrom,
      dateUntil,
      projectIds,
      trackingIds,
      this.getDataExportType(scaleType),
    );
  }

  private getDataExportType(selectedScaleType: string): DataExportType {
    switch (selectedScaleType) {
      case DataExportType.EXTERNAL_EVALUATION_GRAZIAS_V2.valueOf():
        return DataExportType.EXTERNAL_EVALUATION_GRAZIAS_V2;
      case DataExportType.EXTERNAL_EVLUATION_DIDACTICS.valueOf():
        return DataExportType.EXTERNAL_EVLUATION_DIDACTICS;
      case DataExportType.EXTERNAL_EVALUATION_MATH.valueOf():
        return DataExportType.EXTERNAL_EVALUATION_MATH;
      case DataExportType.EXTERNAL_EVLUATION_TEBI.valueOf():
        return DataExportType.EXTERNAL_EVLUATION_TEBI;
      case DataExportType.SELF_EVALUATION_GRAZIAS_V2.valueOf():
        return DataExportType.SELF_EVALUATION_GRAZIAS_V2;
      case DataExportType.CHILDREN_EVALUATION.valueOf():
        return DataExportType.CHILDREN_EVALUATION;
    }
  }

  async openPopup(): Promise<void> {
    const modal = await this.modalController.create({
      component: AddUserPopUpComponent,
      componentProps: {
        parent: this,
      },
      cssClass: 'add-user-modal',
    });
    return await modal.present();
  }

  navigateBack(): void {
    this.navController.navigateRoot('admin/spss-filter');
  }

  getAllScaleTypeOptions(): Option<string>[] {
    return Object.values(DataExportType)
      .filter(
        (value) =>
          value !== DataExportType.CHILDREN_EVALUATION &&
          value !== DataExportType.SELF_EVALUATION_GRAZIAS_V2,
      )
      .map((value) => {
        return {
          label: this.translate.instant(getDataExportTypeTranslationKey(value, true)),
          value,
        };
      });
  }

  getAllEvaluationTypeOptions(): Option<string>[] {
    return Object.values(FilterEvaluationType).map((value) => {
      return { label: this.translate.instant(getEvaluationTypeTranslationKey(value)), value };
    });
  }

  getAvailableProjectOptions(): Option<string>[] {
    return this.availableProjects === undefined
      ? []
      : this.availableProjects
          .filter((project) => !project.isSelected)
          .map((project) => {
            return { label: project.name, value: project.name };
          });
  }

  getProjectFilterList(): ProjectUiModel[] {
    return this.availableProjects === undefined
      ? []
      : this.availableProjects.filter((project) => project.isSelected);
  }

  getSelectedUsers(): UserResponse[] {
    return this.selectedUsers === undefined ? [] : this.selectedUsers;
  }

  changeSelectedState(selectedProject: string): void {
    this.availableProjects.find((project) => project.name == selectedProject).isSelected =
      !this.availableProjects.find((project) => project.name == selectedProject).isSelected;
  }

  removeUser(trackingId: string): void {
    const user = this.selectedUsers.find((u) => u.trackingId == trackingId);
    const userIndex = this.selectedUsers.indexOf(user);
    if (userIndex !== -1) {
      this.selectedUsers.splice(userIndex);
    }
  }

  getFullName(firstname: string, lastname: string): string {
    return firstname + ' ' + lastname;
  }

  async submitForm(): Promise<void> {
    this.validateFields();
    if (this.filterForm.valid) {
      this.loading = true;
      this.loadingUi = await this.loadingController.create(
        Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
      );
      await this.loadingUi.present();
      this.adminService
        .createSPSSFilter(this.createDataExportFilterRequest())
        .subscribe(async (response) => {
          if (response.success) {
            const message = this.translate.instant('spssFilter.successMessage');
            // do not await otherwise it will get stuck
            Utils.copyToClipboard(response.data.selectStatement);
            this.loading = false;
            await this.loadingUi.dismiss();
            await this.showMessage(true, message, true);
          } else {
            const message = response['httpStatus'].toString().startsWith('4')
              ? this.translate.instant('spssFilter.error')
              : this.translate.instant('global.error.generic');
            this.loading = false;
            await this.loadingUi.dismiss();
            await this.showMessage(false, message, false);
          }
        });
    }
  }
}

class ProjectUiModel {
  constructor(id, name, isSelected) {
    this.id = id;
    this.name = name;
    this.isSelected = isSelected;
  }

  id: number;
  name: string;
  isSelected: boolean;
}
