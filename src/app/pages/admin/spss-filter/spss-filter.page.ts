import { Component, OnInit } from '@angular/core';
import { LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Utils } from '../../../utils/utils';
import { AdminService, FilterResponse } from '../../../services/admin/admin.service';
import moment from 'moment/moment';
import { DataExportType, getDataExportTypeTranslationKey } from 'src/app/model/DataExportType';

@Component({
  selector: 'app-spss-filter',
  templateUrl: './spss-filter.page.html',
  styleUrls: ['./spss-filter.page.scss'],
})
export class SpssFilterPage implements OnInit {
  public loadingUi: HTMLIonLoadingElement = null;

  public filters: Array<FilterResponse> = [];

  public loading = false;

  constructor(
    private navController: NavController,
    private adminService: AdminService,
    public loadingController: LoadingController,
    public translate: TranslateService,
    public toastController: ToastController,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.fetchFilters();
  }

  async fetchFilters(): Promise<void> {
    this.loading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getAvailableSPSSFilters().subscribe((response) => {
      if (response.success) {
        this.filters = response.data.filter.sort((a, b) => {
          return a.queryCreatedAt > b.queryCreatedAt ? -1 : 1;
        });
      }
      this.loading = false;
      this.loadingUi.dismiss();
    });
  }

  async deleteFilter(exportType: DataExportType, queryId: string): Promise<void> {
    this.adminService.deleteSPSSFilter(queryId, exportType).subscribe((response) => {
      if (response.success) {
        this.fetchFilters();
      }
    });
  }

  async deleteAllFilter(): Promise<void> {
    this.adminService.deleteAllSPSSFilter().subscribe((response) => {
      if (response.success) {
        this.fetchFilters();
      }
    });
  }

  navigateDetail(): void {
    this.navController.navigateRoot('admin/spss-filter-detail');
  }

  getTimeFrame(validFrom: Date, validUntil: Date): string {
    return validFrom && validUntil
      ? moment(validFrom).format('DD.MM.yyyy') + ' - ' + moment(validUntil).format('DD.MM.yyyy')
      : validFrom
        ? this.translate.instant('spssFilter.from') + moment(validFrom).format('DD.MM.yyyy')
        : validUntil
          ? this.translate.instant('spssFilter.until') + moment(validUntil).format('DD.MM.yyyy')
          : '-';
  }

  getDateFormatted(createdAt: Date): string {
    return moment(createdAt, moment.ISO_8601).format('DD.MM.YYYY HH:mm');
  }

  getTranslationKey(evaluationType: DataExportType): string {
    return getDataExportTypeTranslationKey(evaluationType);
  }

  copyToClipboard(selectStatement: string): void {
    Utils.copyToClipboard(selectStatement);
    this.showMessage(this.translate.instant('global.copied'), true);
  }

  private async showMessage(message: string, success: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color: success ? 'success' : 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }
}

export enum FilterEvaluationType {
  EXTERNAL_EVALUATION = 'EXTERNAL_EVALUATION',
  SELF_EVALUATION = 'SELF_EVALUATION',
  CHILD_EVALUATION = 'CHILD_EVALUATION',
}

export const getEvaluationTypeTranslationKey = (evaluationType: FilterEvaluationType): string => {
  switch (evaluationType.valueOf()) {
    case FilterEvaluationType.EXTERNAL_EVALUATION.valueOf():
      return 'spssFilter.evaluationType.externalEvaluation';
    case FilterEvaluationType.SELF_EVALUATION.valueOf():
      return 'spssFilter.evaluationType.selfEvaluation';
    case FilterEvaluationType.CHILD_EVALUATION.valueOf():
      return 'spssFilter.evaluationType.childEvaluation';
  }
};
