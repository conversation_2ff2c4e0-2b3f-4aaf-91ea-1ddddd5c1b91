import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { SpssFilterPage } from './spss-filter.page';
import { SharedModule } from 'src/app/modules/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { GraziasMissingTranslationHandler } from '../../../utils/missingTranslationHandler';

const routes: Routes = [
  {
    path: '',
    component: SpssFilterPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    SharedModule,
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
  ],
  declarations: [SpssFilterPage],
})
export class SpssFilterModule {}
