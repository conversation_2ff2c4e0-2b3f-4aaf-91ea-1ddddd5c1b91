<div class="main-content">
  <ion-header class="header-v2">
    <ion-toolbar class="toolbar-v2">
      <div class="flex">
        <ion-title>{{ 'spssFilter.title' | translate }}</ion-title>
        <app-primary-button
          class="spss-filter-delete-button"
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="deleteAllFilter()"
          [isDisabled]="false"
          [isLoading]="false"
          [label]="'spssFilter.deleteAllFilter' | translate | uppercase"
          [destructive]="true"
        >
        </app-primary-button>
        <app-primary-button
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="navigateDetail()"
          [isDisabled]="false"
          [isLoading]="false"
          [label]="'spssFilter.newFilter' | translate | uppercase"
        >
        </app-primary-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <ion-content class="design-v2 content">
    <div class="page-padding">
      <div *ngIf="filters.length > 0 || loading; else emptyView">
        <div *ngFor="let filter of filters" class="flex-container">
          <div
            class="list-entry ion-activatable ripple-parent flex"
            (click)="copyToClipboard(filter.selectStatement)"
          >
            <span>{{ getDateFormatted(filter.queryCreatedAt) }}</span>
            <span>{{ getTranslationKey(filter.type) | translate }} </span>
            <span class="timeframe"
              >{{ getTimeFrame(filter.filteredFrom, filter.filteredUntil) }}</span
            >
            <div class="copy-icon" (click)="copyToClipboard(filter.selectStatement)">
              <ion-img src="assets/svg/copy.svg"></ion-img>
            </div>
            <ion-ripple-effect type="unbounded"></ion-ripple-effect>
          </div>
          <app-icon-button
            (click)="deleteFilter(filter.type, filter.queryId)"
            class="remove-button-container"
            iconPath="assets/img/btn_remove.svg"
          ></app-icon-button>
        </div>
      </div>
    </div>
  </ion-content>
</div>

<ng-template #emptyView>
  <app-empty-view
    [title]="'organization.empty_view.title' | translate"
    [text]="'spssFilter.emptyViewText' | translate"
    imagePath="assets/svg/spss_filter.svg"
  ></app-empty-view>
</ng-template>
