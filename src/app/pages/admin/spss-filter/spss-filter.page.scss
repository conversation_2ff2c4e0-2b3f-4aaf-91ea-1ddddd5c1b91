@import 'src/theme/_variables_v2.scss';

.flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
}

.main-content {
  height: 100%;
}

.content {
  height: calc(100vh - 128px);
}

.list-entry {
  height: 55px;
  border: solid $color-light-gray 1px;
  border-radius: 8px;
  margin-top: 8px;
  padding: 5px 16px;
  align-items: center;
  width: 100%;

  .timeframe {
    width: 12em;
  }

  .horizontal-spacer {
    width: 10px;
  }

  .copy-icon {
    width: 5%;
    display: flex;
    justify-content: flex-end;
    cursor: pointer;

    ion-img {
      height: 22px;
      width: 22px;
    }
  }
}

.page-padding {
  padding-left: 94px;
  padding-right: 94px;
  padding-bottom: 20px;
}

.ripple-parent {
  position: relative;
  overflow: hidden;
}

.flex-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.remove-button-container {
  margin-top: 8px;
}

.flex-container > * + * {
  margin-left: 10px;
}

.spss-filter-delete-button {
  margin-right: 10px;
}
