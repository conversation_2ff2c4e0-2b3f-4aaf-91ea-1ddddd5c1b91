import { AfterViewChecked, Component, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  AdminService,
  EducatorDeleteRequest,
  EducatorSearchRequest,
  EducatorSearchResponse,
} from 'src/app/services/admin/admin.service';
import {
  <PERSON>ert<PERSON>ontroller,
  IonSearchbar,
  LoadingController,
  NavController,
  ToastController,
} from '@ionic/angular';

@Component({
  selector: 'app-educator-search',
  templateUrl: './educator-search.page.html',
  styleUrls: ['./educator-search.page.scss'],
})
export class EducatorSearchPage implements AfterViewChecked {
  @ViewChild('educatorSearchbar', { static: false }) educatorSearchbar: IonSearchbar;

  public pageTitle = this.translate.instant('admin.educatorSearch.title');
  public heading = this.translate.instant('admin.educatorSearch.label.title');
  public infoText = this.translate.instant('admin.educatorSearch.label.info');
  public searchbarPlaceholder = this.translate.instant('admin.educatorSearch.placeholder');
  public deleteButton = this.translate.instant('admin.educatorSearch.label.delete');
  public searchButton = this.translate.instant('admin.educatorSearch.label.search');
  public educatorResponse: EducatorSearchResponse = null;

  private searchIcon;
  private loadingUi = null;

  constructor(
    private translate: TranslateService,
    private adminService: AdminService,
    private toastController: ToastController,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private navController: NavController,
  ) {}

  ngOnViewDidLoad(): void {
    this.educatorResponse = null;
  }

  ngAfterViewChecked(): void {
    this.searchIcon = document.body.getElementsByClassName('searchbar-search-icon')[0];
  }

  // Service Calls

  async sendSearchRequest(email: string): Promise<void> {
    await this.showLoadingUi(
      this.translate.instant('admin.educatorSearch.message.loading.searchText'),
    );
    const request = new EducatorSearchRequest(email);
    this.adminService.getEducatorSearchResult(request).subscribe(async (result) => {
      if (result.success) {
        this.educatorResponse = result.data;
      } else {
        this.showToast(
          this.translate.instant('admin.educatorSearch.message.noUserFound'),
          'danger',
        );
      }
      await this.hideLoadingUi();
    });
  }

  async sendDeleteEducatorRequest(): Promise<void> {
    await this.showLoadingUi(
      this.translate.instant('admin.educatorSearch.message.loading.deleteText'),
    );
    const request = new EducatorDeleteRequest(this.educatorResponse.generatedId);
    this.adminService.deleteEducator(request).subscribe(async (result) => {
      if (result.success) {
        this.showToast(
          this.translate.instant('admin.educatorSearch.message.deleteSuccess'),
          'primary',
        );
        this.navController.navigateRoot('admin/dashboard');
      } else {
        this.showToast(
          this.translate.instant('admin.educatorSearch.message.deleteError'),
          'danger',
        );
      }
      await this.hideLoadingUi();
    });
  }

  // UI Actions

  changeSearchbarState(): void {
    this.educatorResponse = null;
    if (this.educatorSearchbar.value !== '') {
      this.searchIcon.setAttribute('style', 'display: none');
    } else {
      this.showSearchIcon();
    }
  }

  showSearchIcon(): void {
    this.educatorResponse = null;
    this.searchIcon.setAttribute('style', 'display: block');
  }

  enterPressed(): void {
    if (this.educatorSearchbar.value !== '') {
      this.sendSearchRequest(this.educatorSearchbar.value);
    }
  }

  async showLoadingUi(message: string): Promise<void> {
    this.loadingUi = await this.loadingController.create({
      message,
    });
    await this.loadingUi.present();
  }

  async hideLoadingUi(): Promise<void> {
    await this.loadingUi.dismiss();
  }

  async showToast(message: string, color: string): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color,
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    toast.present();
  }

  async confirmDelete(): Promise<void> {
    const alert = await this.alertController.create({
      header: this.translate.instant('admin.educatorSearch.message.alert.title'),
      message: this.translate.instant('admin.educatorSearch.message.alert.message'),
      buttons: [
        {
          text: this.translate.instant('global.alert.confirm.cancel'),
          role: 'cancel',
        },
        {
          text: this.translate.instant('global.alert.confirm.ok'),
          cssClass: 'alert-button-danger',
          handler: () => {
            this.sendDeleteEducatorRequest();
          },
        },
      ],
      backdropDismiss: false,
    });

    await alert.present();
  }

  navigateAdminDashboard(): void {
    this.navController.navigateRoot('admin/dashboard');
  }
}
