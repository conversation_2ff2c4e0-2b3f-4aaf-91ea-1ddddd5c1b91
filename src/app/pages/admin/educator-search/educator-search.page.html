<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateAdminDashboard()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'admin.dashboard.title' | translate}}</b>
      </ion-row>
      <ion-title>{{pageTitle}}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content #content class="design-v2 content educator-search">
  <div class="page-padding">
    <ion-grid class="h-100 searchbar-container">
      <ion-row>
        <ion-col size="6">
          <h3>{{heading}}</h3>
          <ion-searchbar
            #educatorSearchbar
            class="custom-search-bar"
            (input)="changeSearchbarState()"
            (ionClear)="showSearchIcon()"
            (keyup.enter)="enterPressed()"
            [placeholder]="searchbarPlaceholder"
            inputmode="email"
            type="email"
          ></ion-searchbar>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="ion-align-self-end" size="3">
          <ion-button class="form-bttn search" (click)="enterPressed()"
            >{{searchButton}}</ion-button
          >
          <ion-button *ngIf="educatorResponse" class="form-bttn delete" (click)="confirmDelete()"
            >{{deleteButton}}</ion-button
          >
        </ion-col>
      </ion-row>
      <ion-row *ngIf="!educatorResponse" class="info-container ion-text-center">
        <ion-col size="6"> {{infoText}} </ion-col>
      </ion-row>
    </ion-grid>
  </div>
</ion-content>
