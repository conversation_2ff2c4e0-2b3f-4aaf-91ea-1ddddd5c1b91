<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'navigation.licenses' | translate}}</b>
      </ion-row>
      <ion-title>{{getToolbarTitle()}}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content #content class="design-v2 content">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col>
        <form [formGroup]="licenseForm" (ngSubmit)="submitForm()" class="input-container">
          <ion-grid class="form-spacing">
            <ion-row>
              <ion-col size="5.5">
                <app-input-with-heading
                  formControlName="name"
                  type="text"
                  [label]="'childrenEvaluation.license.name' | translate"
                  [setFocus]="true"
                  [inputValue]="licenseForm.controls.name.value"
                  [errorMessage]="licenseNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
              <ion-col size="1"></ion-col>
              <ion-col *ngIf="licenseDetailMode == detailMode.UPDATE" size="5.5">
                <app-inactive-copyable-input-field
                  labelKey="childrenEvaluation.license.key"
                  [text]="license.licenseKey"
                  [isHidden]="false"
                >
                </app-inactive-copyable-input-field>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="5.5">
                <app-datepicker-with-heading
                  formControlName="validFrom"
                  [label]="'childrenEvaluation.license.valid_from' | translate"
                  [inputValue]="licenseForm.controls.validFrom.value"
                  [errorMessage]="licenseValidFromErrorMessage"
                >
                </app-datepicker-with-heading>
              </ion-col>
              <ion-col size="1"></ion-col>
              <ion-col size="5.5">
                <app-datepicker-with-heading
                  formControlName="validUntil"
                  [label]="'childrenEvaluation.license.valid_until' | translate"
                  [inputValue]="licenseForm.controls.validUntil.value"
                  [errorMessage]="licenseValidUntilErrorMessage"
                >
                </app-datepicker-with-heading>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="5.5">
                <div>
                  <app-input-with-heading
                    formControlName="remainingActivations"
                    type="number"
                    [label]="'childrenEvaluation.license.total_activations.label' | translate"
                    [inputValue]="licenseForm.controls.remainingActivations.value"
                    [errorMessage]="licenseRemainingActivationsErrorMessage"
                  >
                  </app-input-with-heading>
                  <div class="explanation">
                    {{ 'childrenEvaluation.license.total_activations.explanation' | translate }}
                  </div>
                </div>
              </ion-col>
              <ion-col> </ion-col>
            </ion-row>

            <ion-row class="section-padding">
              <ion-col size="5.5">
                <div>
                  <div class="toggle-row">
                    <ion-toggle
                      #childrenEvaluationLicenseEnabled
                      mode="ios"
                      class="toggle-button"
                      (ionChange)="setValidatorsAndResetValuesFor(licenseType.CHILDREN_EVALUATION)"
                    ></ion-toggle>
                    {{ 'childrenEvaluation.license.childrenEvaluation.label' | translate }}
                  </div>
                  <app-input-with-heading
                    formControlName="totalChildrenEvaluationsRemaining"
                    type="number"
                    [label]="'childrenEvaluation.license.total_number_of_evaluations.label' | translate"
                    [inputValue]="licenseForm.controls.totalChildrenEvaluationsRemaining.value"
                    [errorMessage]="totalChildrenEvaluationsRemainingErrorMessage"
                    [disabled]="!isLicenseTypeEnabled(licenseType.CHILDREN_EVALUATION)"
                  >
                  </app-input-with-heading>
                  <div class="explanation">
                    {{ 'childrenEvaluation.license.total_number_of_evaluations.explanation' |
                    translate }}
                  </div>
                </div>
              </ion-col>
              <ion-col> </ion-col>
            </ion-row>

            <ion-row class="section-padding">
              <ion-col size="5.5">
                <div class="toggle-row">
                  <ion-toggle
                    #externalEvaluationLicenseEnabled
                    mode="ios"
                    class="toggle-button"
                    (ionChange)="setValidatorsAndResetValuesFor(licenseType.EXTERNAL_EVALUATION)"
                  ></ion-toggle>
                  {{ 'childrenEvaluation.license.externalEvaluation.label' | translate }}
                </div>
                <div>
                  <app-input-with-heading
                    formControlName="totalExternalEvaluationsRemaining"
                    type="number"
                    [label]="'childrenEvaluation.license.total_number_of_evaluations.label' | translate"
                    [inputValue]="licenseForm.controls.totalExternalEvaluationsRemaining.value"
                    [errorMessage]="totalExternalEvaluationsRemainingErrorMessage"
                    [disabled]="!isLicenseTypeEnabled(licenseType.EXTERNAL_EVALUATION)"
                  >
                  </app-input-with-heading>
                  <div class="explanation">
                    {{ 'childrenEvaluation.license.total_number_of_evaluations.explanation' |
                    translate }}
                  </div>
                </div>
              </ion-col>
              <ion-col size="1"> </ion-col>
              <ion-col size="5.5">
                <div class="checkbox-title">
                  {{ 'childrenEvaluation.license.externalEvaluation.scales.label' | translate }}
                </div>
                <div class="checkboxes">
                  <mat-checkbox
                    #graziasV2Checkbox
                    (click)="toggleScaleTypePermission(scaleTypePermission.GRAZIAS_V2)"
                  >
                    {{ 'mobileAppV2.generic.scale.grazias' | translate }}
                  </mat-checkbox>
                  <mat-checkbox
                    #mathCheckbox
                    (click)="toggleScaleTypePermission(scaleTypePermission.MATH)"
                  >
                    {{ 'mobileAppV2.generic.scale.math' | translate }}
                  </mat-checkbox>
                  <mat-checkbox
                    #tebiCheckbox
                    (click)="toggleScaleTypePermission(scaleTypePermission.NATURE)"
                  >
                    {{ 'mobileAppV2.generic.scale.tebi' | translate }}
                  </mat-checkbox>
                  <mat-checkbox
                    #didaCheckbox
                    (click)="toggleScaleTypePermission(scaleTypePermission.DIDACTICS)"
                  >
                    {{ 'mobileAppV2.generic.scale.dida' | translate }}
                  </mat-checkbox>
                  <div
                    class="error-message"
                    *ngIf="scaleTypePermissionErrorMessage != null && isLicenseTypeEnabled(licenseType.EXTERNAL_EVALUATION)"
                  >
                    {{scaleTypePermissionErrorMessage}}
                  </div>
                </div>
              </ion-col>
            </ion-row>

            <ion-row class="section-padding">
              <ion-col size="5.5">
                <div>
                  <div class="toggle-row">
                    <ion-toggle
                      #selfEvaluationLicenseEnabled
                      mode="ios"
                      class="toggle-button"
                      (ionChange)="setValidatorsAndResetValuesFor(licenseType.SELF_EVALUATION)"
                    ></ion-toggle>
                    {{ 'childrenEvaluation.license.selfEvaluation.label' | translate }}
                  </div>
                  <app-input-with-heading
                    formControlName="totalSelfEvaluationsRemaining"
                    type="number"
                    [label]="'childrenEvaluation.license.total_number_of_evaluations.label' | translate"
                    [inputValue]="licenseForm.controls.totalSelfEvaluationsRemaining.value"
                    [errorMessage]="totalSelfEvaluationsRemainingErrorMessage"
                    [disabled]="!isLicenseTypeEnabled(licenseType.SELF_EVALUATION)"
                  >
                  </app-input-with-heading>
                  <div class="explanation">
                    {{ 'childrenEvaluation.license.total_number_of_evaluations.explanation' |
                    translate}}
                  </div>
                </div>
                <div class="error-message with-padding">
                  {{this.noEvaluationChosenErrorMessage}}
                </div>
              </ion-col>
              <ion-col> </ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col size="6"></ion-col>
              <ion-col size="3">
                <app-primary-button
                  expand="block"
                  (onClick)="navigateBack()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'global.button.cancel' | translate"
                >
                </app-primary-button>
              </ion-col>
              <ion-col size="3">
                <app-primary-button
                  type="submit"
                  expand="block"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [label]="getSubmitButtonLabel()"
                  [buttonType]="buttonType.GRADIENT"
                >
                </app-primary-button>
              </ion-col>
            </ion-row>
            <div class="bottom-safe-area"></div>
          </ion-grid>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
