@import '../../../../theme/variables_v2';

.horizontal-spacer {
  height: 3rem;
}

.horizontal-spacer-small {
  height: 1rem;
}

.bottom-safe-area {
  height: 80px;
}

.indentation {
  margin: 0 4.6rem;
  max-width: var(--content-max-width);
}

ion-grid {
  &.form-spacing {
    ion-col {
      padding-top: 16px;
    }
  }
}

.content {
  height: calc(100vh - 128px);
}

.back-button {
  padding-left: 8px;
  text-transform: uppercase;
  margin: 0;
  font-size: small;
}

.button-row {
  cursor: pointer;
}

.section-padding {
  padding-top: 48px;
}

.hint-text {
  font-size: 16px;
  line-height: 18px;
  color: $color-hint-text-grey;
}

.explanation {
  padding-top: 16px;
  color: $color-hint-text-grey;
}

.toggle-row {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  padding-bottom: 16px;

  .toggle-button {
    --background: var(--ion-color-light-grey-V2);
    --background-checked: var(--ion-color-primary-V2);
    --handle-background: var(--ion-color-dark-grey-V2);
    --handle-background-checked: white;

    zoom: 0.7;
    padding-right: 16px;
  }
}

.checkbox-title {
  padding-top: 42px;
  font-size: 13px;
  line-height: 18px;
  font-weight: bold;
  text-transform: uppercase;
  color: $color-hint-text-grey;
}

.checkboxes {
  display: flex;
  flex-direction: column;
  padding-top: 8px;

  .mat-mdc-checkbox {
    padding-bottom: 8px;
  }
}

.error-message {
  color: var(--ion-color-danger-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: bold;

  &.with-padding {
    padding-top: 40px;
  }
}
