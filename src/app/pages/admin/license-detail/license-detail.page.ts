import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { MatCheckbox } from '@angular/material/checkbox';
import { ActivatedRoute } from '@angular/router';
import { IonToggle, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { FormValidationHelper } from '../../../modules/organization/helpers/form-validation-helper';
import { PrimaryButtonType } from '../../../modules/shared/components/primary-button/primary-button.component';
import {
  AppUserLicense,
  LicenseService,
  ChildrenEvaluationLicenseRequest,
  ExternalEvaluationLicenseRequest,
  SelfEvaluationLicenseRequest,
  UpsertLicenseRequest,
} from '../../../services/license/license.service';
import { LicenseDetailMode } from '../licenses/licenses.page';
import { AdminRoutes } from '../../../app-routes';

@Component({
  selector: 'app-license-detail',
  templateUrl: './license-detail.page.html',
  styleUrls: ['./license-detail.page.scss'],
})
export class LicenseDetailPage implements OnInit, AfterViewInit {
  constructor(
    public translate: TranslateService,
    private navController: NavController,
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    private licenseService: LicenseService,
    private toastController: ToastController,
  ) {}

  public buttonType = PrimaryButtonType;
  public scaleTypePermission = ScaleTypePermission;
  public licenseType = LicenseType;
  public license: AppUserLicense;
  public licenseDetailMode: LicenseDetailMode;
  public detailMode = LicenseDetailMode;
  public licenseNameErrorMessage?: string;
  public licenseValidFromErrorMessage?: string;
  public licenseValidUntilErrorMessage?: string;
  public licenseRemainingActivationsErrorMessage?: string;
  public totalChildrenEvaluationsRemainingErrorMessage?: string;
  public totalExternalEvaluationsRemainingErrorMessage?: string;
  public totalSelfEvaluationsRemainingErrorMessage?: string;
  public scaleTypePermissionErrorMessage?: string;
  public noEvaluationChosenErrorMessage?: string;
  private routeParamSubscription: Subscription;

  @ViewChild('graziasV2Checkbox') private graziasV2Checkbox: MatCheckbox;
  @ViewChild('mathCheckbox') private mathCheckbox: MatCheckbox;
  @ViewChild('tebiCheckbox') private tebiCheckbox: MatCheckbox;
  @ViewChild('didaCheckbox') private didaCheckbox: MatCheckbox;

  @ViewChild('childrenEvaluationLicenseEnabled')
  private childrenEvaluationLicenseEnabled: IonToggle;
  @ViewChild('externalEvaluationLicenseEnabled')
  private externalEvaluationLicenseEnabled: IonToggle;
  @ViewChild('selfEvaluationLicenseEnabled') private selfEvaluationLicenseEnabled: IonToggle;

  public licenseForm = this.formBuilder.group({
    name: ['', [Validators.required]],
    remainingActivations: [0, [Validators.required]],
    totalChildrenEvaluationsRemaining: [0, []],
    totalExternalEvaluationsRemaining: [0, []],
    totalSelfEvaluationsRemaining: [0, []],
    validFrom: ['', [Validators.required]],
    validUntil: ['', [Validators.required]],
    scaleTypePermissionGraziasV2: [false, []],
    scaleTypePermissionMath: [false, []],
    scaleTypePermissionNature: [false, []],
    scaleTypePermissionDidactics: [false, []],
    scaleTypePermissionErrorControl: [null, []],
  });

  ngOnInit(): void {
    this.routeParamSubscription = this.route.queryParams.subscribe(async (params) => {
      this.license = params.license;
      this.licenseDetailMode = params.licenseDetailMode;
    });
    this.initForm();
  }

  ngAfterViewInit(): void {
    this.initViewData();
  }

  private initViewData(): void {
    if (this.isUpdateMode()) {
      this.graziasV2Checkbox.checked =
        this.license.externalEvaluationData?.scaleTypePermissionGraziasV2 ?? false;
      this.mathCheckbox.checked =
        this.license.externalEvaluationData?.scaleTypePermissionMath ?? false;
      this.tebiCheckbox.checked =
        this.license.externalEvaluationData?.scaleTypePermissionNature ?? false;
      this.didaCheckbox.checked =
        this.license.externalEvaluationData?.scaleTypePermissionDidactics ?? false;

      this.childrenEvaluationLicenseEnabled.checked = this.license.childrenEvaluationData != null;
      this.externalEvaluationLicenseEnabled.checked = this.license.externalEvaluationData != null;
      this.selfEvaluationLicenseEnabled.checked = this.license.selfEvaluationData != null;
    }
    this.graziasV2Checkbox.disabled = this.license?.externalEvaluationData == null;
    this.mathCheckbox.disabled = this.license?.externalEvaluationData == null;
    this.tebiCheckbox.disabled = this.license?.externalEvaluationData == null;
    this.didaCheckbox.disabled = this.license?.externalEvaluationData == null;
  }

  private initForm(): void {
    if (this.isUpdateMode()) {
      this.licenseForm.controls.name.setValue(this.license.name);
      this.licenseForm.controls.remainingActivations.setValue(this.license.remainingActivations);
      this.licenseForm.controls.validFrom.setValue(this.license.validFrom);
      this.licenseForm.controls.validUntil.setValue(this.license.validUntil);
      this.licenseForm.controls.totalChildrenEvaluationsRemaining.setValue(
        this.license.childrenEvaluationData?.totalEvaluationsRemaining ?? 0,
      );
      this.licenseForm.controls.totalSelfEvaluationsRemaining.setValue(
        this.license.selfEvaluationData?.totalEvaluationsRemaining ?? 0,
      );
      this.licenseForm.controls.totalExternalEvaluationsRemaining.setValue(
        this.license.externalEvaluationData?.totalEvaluationsRemaining ?? 0,
      );
      this.setScaleTypePermission(
        ScaleTypePermission.GRAZIAS_V2,
        this.license.externalEvaluationData?.scaleTypePermissionGraziasV2 ?? false,
      );
      this.setScaleTypePermission(
        ScaleTypePermission.MATH,
        this.license.externalEvaluationData?.scaleTypePermissionMath ?? false,
      );
      this.setScaleTypePermission(
        ScaleTypePermission.NATURE,
        this.license.externalEvaluationData?.scaleTypePermissionNature ?? false,
      );
      this.setScaleTypePermission(
        ScaleTypePermission.DIDACTICS,
        this.license.externalEvaluationData?.scaleTypePermissionDidactics ?? false,
      );
    }
  }

  private setScaleTypePermission(scaleTypePermission: ScaleTypePermission, value: boolean): void {
    this.licenseForm.controls[scaleTypePermission].setValue(value);
  }

  isLicenseTypeEnabled(type: LicenseType): boolean {
    switch (type) {
      case LicenseType.CHILDREN_EVALUATION:
        return this.childrenEvaluationLicenseEnabled?.checked ?? false;
      case LicenseType.EXTERNAL_EVALUATION:
        return this.externalEvaluationLicenseEnabled?.checked ?? false;
      case LicenseType.SELF_EVALUATION:
        return this.selfEvaluationLicenseEnabled?.checked ?? false;
    }
  }

  toggleScaleTypePermission(scaleTypePermission: ScaleTypePermission): void {
    this.setScaleTypePermission(
      scaleTypePermission,
      !this.licenseForm.controls[scaleTypePermission].value,
    );
  }

  getToolbarTitle(): string {
    const suffix = this.isUpdateMode() ? ` ${this.license.name}` : '';
    return `${this.translate.instant('childrenEvaluation.license.title')}${suffix}`;
  }

  navigateBack(): void {
    this.navController.navigateRoot(AdminRoutes.licenses);
  }

  getSubmitButtonLabel(): string {
    const key = this.isUpdateMode() ? 'global.save' : 'global.create';
    return this.translate.instant(key);
  }

  submitForm(): void {
    this.validateFields();
    if (this.licenseForm.valid) {
      const licenseRequest = new UpsertLicenseRequest();
      licenseRequest.licenseId = this.isUpdateMode() ? this.license.id : null;
      licenseRequest.name = this.licenseForm.controls.name.value;
      licenseRequest.remainingActivations = this.licenseForm.controls.remainingActivations.value;
      licenseRequest.validFrom = this.licenseForm.controls.validFrom.value;
      licenseRequest.validUntil = this.licenseForm.controls.validUntil.value;

      if (this.isLicenseTypeEnabled(LicenseType.CHILDREN_EVALUATION)) {
        licenseRequest.childrenEvaluationLicenseRequest = new ChildrenEvaluationLicenseRequest();
        licenseRequest.childrenEvaluationLicenseRequest.childrenEvaluationLicenseId =
          this.isUpdateMode() ? this.license.childrenEvaluationData?.licenseId ?? null : null;
        licenseRequest.childrenEvaluationLicenseRequest.totalEvaluationsRemaining =
          this.licenseForm.controls.totalChildrenEvaluationsRemaining.value;
      }

      if (this.isLicenseTypeEnabled(LicenseType.EXTERNAL_EVALUATION)) {
        licenseRequest.externalEvaluationLicenseRequest = new ExternalEvaluationLicenseRequest();
        licenseRequest.externalEvaluationLicenseRequest.externalEvaluationLicenseId =
          this.isUpdateMode() ? this.license.externalEvaluationData?.licenseId ?? null : null;
        licenseRequest.externalEvaluationLicenseRequest.totalEvaluationsRemaining =
          this.licenseForm.controls.totalExternalEvaluationsRemaining.value;
        licenseRequest.externalEvaluationLicenseRequest.scaleTypePermissionGraziasV2 =
          this.licenseForm.controls.scaleTypePermissionGraziasV2.value;
        licenseRequest.externalEvaluationLicenseRequest.scaleTypePermissionMath =
          this.licenseForm.controls.scaleTypePermissionMath.value;
        licenseRequest.externalEvaluationLicenseRequest.scaleTypePermissionNature =
          this.licenseForm.controls.scaleTypePermissionNature.value;
        licenseRequest.externalEvaluationLicenseRequest.scaleTypePermissionDidactics =
          this.licenseForm.controls.scaleTypePermissionDidactics.value;
      }

      if (this.isLicenseTypeEnabled(LicenseType.SELF_EVALUATION)) {
        licenseRequest.selfEvaluationLicenseRequest = new SelfEvaluationLicenseRequest();
        licenseRequest.selfEvaluationLicenseRequest.selfEvaluationLicenseId = this.isUpdateMode()
          ? this.license.selfEvaluationData?.licenseId ?? null
          : null;
        licenseRequest.selfEvaluationLicenseRequest.totalEvaluationsRemaining =
          this.licenseForm.controls.totalSelfEvaluationsRemaining.value;
      }

      this.licenseService.createOrUpdateLicense(licenseRequest).subscribe((resp) => {
        if (resp.success) {
          const message = this.isUpdateMode()
            ? this.translate.instant('childrenEvaluation.license.update_success')
            : this.translate.instant('childrenEvaluation.license.create_success');
          this.showMessage(true, message, true);
        } else {
          if (resp.httpStatus === 409) {
            this.licenseNameErrorMessage = this.translate.instant(
              'childrenEvaluation.license.name_conflict',
            );
          } else {
            const message = this.translate.instant('global.error.generic');
            this.showMessage(false, message, false);
          }
        }
      });
    }
  }

  setValidatorsAndResetValuesFor(licenseType: LicenseType): void {
    switch (licenseType) {
      case LicenseType.CHILDREN_EVALUATION:
        this.setValidatorsForLicenseTypeFormFieldsAndResetValues(
          'totalChildrenEvaluationsRemaining',
          licenseType,
        );
        break;
      case LicenseType.EXTERNAL_EVALUATION:
        this.setValidatorsForLicenseTypeFormFieldsAndResetValues(
          'totalExternalEvaluationsRemaining',
          licenseType,
        );
        const isEnabled = this.isLicenseTypeEnabled(licenseType);
        if (!isEnabled) {
          this.licenseForm.controls.scaleTypePermissionErrorControl.setValidators([
            Validators.required,
          ]);
          this.graziasV2Checkbox.checked = false;
          this.mathCheckbox.checked = false;
          this.tebiCheckbox.checked = false;
          this.didaCheckbox.checked = false;
        } else {
          this.licenseForm.controls.scaleTypePermissionErrorControl.clearValidators();
        }
        this.graziasV2Checkbox.disabled = !isEnabled;
        this.mathCheckbox.disabled = !isEnabled;
        this.tebiCheckbox.disabled = !isEnabled;
        this.didaCheckbox.disabled = !isEnabled;
        break;
      case LicenseType.SELF_EVALUATION:
        this.setValidatorsForLicenseTypeFormFieldsAndResetValues(
          'totalSelfEvaluationsRemaining',
          licenseType,
        );
        break;
    }
  }

  setValidatorsForLicenseTypeFormFieldsAndResetValues(
    control: string,
    licenseType: LicenseType,
  ): void {
    if (this.isLicenseTypeEnabled(licenseType)) {
      this.licenseForm.controls[control].setValidators([Validators.required]);
    } else {
      this.licenseForm.controls[control].setValue(0);
      this.licenseForm.controls[control].clearValidators();
      this.unsetErrorMessagesFor(licenseType);
    }
  }

  unsetErrorMessagesFor(licenseType: LicenseType): void {
    switch (licenseType) {
      case LicenseType.CHILDREN_EVALUATION:
        this.totalChildrenEvaluationsRemainingErrorMessage = null;
        this.licenseForm.controls.totalChildrenEvaluationsRemaining.setErrors(null);
        break;
      case LicenseType.EXTERNAL_EVALUATION:
        this.licenseForm.controls.scaleTypePermissionErrorControl.setErrors(null);
        this.scaleTypePermissionErrorMessage = null;
        this.licenseForm.controls.totalExternalEvaluationsRemaining.setErrors(null);
        this.totalExternalEvaluationsRemainingErrorMessage = null;
        break;
      case LicenseType.SELF_EVALUATION:
        this.totalSelfEvaluationsRemainingErrorMessage = null;
        this.licenseForm.controls.totalSelfEvaluationsRemaining.setErrors(null);
        break;
    }
  }

  private isUpdateMode = (): boolean => this.licenseDetailMode === LicenseDetailMode.UPDATE;

  private validateFields(): void {
    const start = new Date(this.licenseForm.controls.validFrom.value);
    const end = new Date(this.licenseForm.controls.validUntil.value);

    if (start >= end) {
      this.licenseForm.setErrors({ range: true });
      this.licenseValidFromErrorMessage = this.translate.instant(
        'generic.error.endDateCantBeLargerThanStart',
      );
      this.licenseValidUntilErrorMessage = '';
    } else {
      this.licenseForm.setErrors(null);
      this.licenseValidFromErrorMessage = FormValidationHelper.handleValidationError(
        this.licenseForm.controls.validFrom,
      );
      this.licenseValidUntilErrorMessage = FormValidationHelper.handleValidationError(
        this.licenseForm.controls.validUntil,
      );
    }

    if (this.licenseForm.controls.remainingActivations.value < 0) {
      this.licenseForm.controls.remainingActivations.setErrors({ incorrect: true });
      this.licenseRemainingActivationsErrorMessage = this.translate.instant(
        'childrenEvaluation.license.avoid_negative_values',
      );
    } else {
      this.licenseRemainingActivationsErrorMessage = FormValidationHelper.handleValidationError(
        this.licenseForm.controls.remainingActivations,
      );
    }

    if (this.isLicenseTypeEnabled(LicenseType.CHILDREN_EVALUATION)) {
      if (this.licenseForm.controls.totalChildrenEvaluationsRemaining.value < 0) {
        this.licenseForm.controls.totalChildrenEvaluationsRemaining.setErrors({ incorrect: true });
        this.totalChildrenEvaluationsRemainingErrorMessage = this.translate.instant(
          'childrenEvaluation.license.avoid_negative_values',
        );
      } else {
        this.totalChildrenEvaluationsRemainingErrorMessage =
          FormValidationHelper.handleValidationError(
            this.licenseForm.controls.totalChildrenEvaluationsRemaining,
          );
      }
    } else {
      this.totalChildrenEvaluationsRemainingErrorMessage = null;
      this.licenseForm.controls.totalChildrenEvaluationsRemaining.setErrors(null);
    }

    if (this.isLicenseTypeEnabled(LicenseType.EXTERNAL_EVALUATION)) {
      if (this.licenseForm.controls.totalExternalEvaluationsRemaining.value < 0) {
        this.licenseForm.controls.totalExternalEvaluationsRemaining.setErrors({ incorrect: true });
        this.totalExternalEvaluationsRemainingErrorMessage = this.translate.instant(
          'childrenEvaluation.license.avoid_negative_values',
        );
      } else {
        this.totalExternalEvaluationsRemainingErrorMessage =
          FormValidationHelper.handleValidationError(
            this.licenseForm.controls.totalExternalEvaluationsRemaining,
          );
      }
      if (
        this.licenseForm.controls.scaleTypePermissionGraziasV2.value === false &&
        this.licenseForm.controls.scaleTypePermissionMath.value === false &&
        this.licenseForm.controls.scaleTypePermissionNature.value === false &&
        this.licenseForm.controls.scaleTypePermissionDidactics.value === false
      ) {
        this.licenseForm.controls.scaleTypePermissionErrorControl.setErrors({ incorrect: true });
        this.scaleTypePermissionErrorMessage = this.translate.instant(
          'childrenEvaluation.license.atLeastOneScaleRequired',
        );
      } else {
        this.licenseForm.controls.scaleTypePermissionErrorControl.setErrors(null);
        this.scaleTypePermissionErrorMessage = null;
      }
    } else {
      this.totalExternalEvaluationsRemainingErrorMessage = null;
      this.licenseForm.controls.totalExternalEvaluationsRemaining.setErrors(null);
    }

    if (this.isLicenseTypeEnabled(LicenseType.SELF_EVALUATION)) {
      if (this.licenseForm.controls.totalSelfEvaluationsRemaining.value < 0) {
        this.licenseForm.controls.totalSelfEvaluationsRemaining.setErrors({ incorrect: true });
        this.totalSelfEvaluationsRemainingErrorMessage = this.translate.instant(
          'childrenEvaluation.license.avoid_negative_values',
        );
      } else {
        this.totalSelfEvaluationsRemainingErrorMessage = FormValidationHelper.handleValidationError(
          this.licenseForm.controls.totalSelfEvaluationsRemaining,
        );
      }
    } else {
      this.totalSelfEvaluationsRemainingErrorMessage = null;
      this.licenseForm.controls.totalSelfEvaluationsRemaining.setErrors(null);
    }

    if (
      !this.isLicenseTypeEnabled(LicenseType.CHILDREN_EVALUATION) &&
      !this.isLicenseTypeEnabled(LicenseType.EXTERNAL_EVALUATION) &&
      !this.isLicenseTypeEnabled(LicenseType.SELF_EVALUATION)
    ) {
      this.licenseForm.controls.totalExternalEvaluationsRemaining.setErrors({ incorrect: true });
      this.licenseForm.controls.totalChildrenEvaluationsRemaining.setErrors({ incorrect: true });
      this.licenseForm.controls.totalSelfEvaluationsRemaining.setErrors({ incorrect: true });
      this.noEvaluationChosenErrorMessage = this.translate.instant(
        'childrenEvaluation.license.atLeastOneEvaluationRequired',
      );
    } else {
      this.noEvaluationChosenErrorMessage = null;
    }

    this.licenseForm.controls.name.setValue(this.licenseForm.controls.name.value.trim());
    this.licenseNameErrorMessage = FormValidationHelper.handleValidationError(
      this.licenseForm.controls.name,
    );
  }

  private async showMessage(
    shouldReturn: boolean,
    message: string,
    success: boolean,
  ): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color: success ? 'success' : 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }
}

enum ScaleTypePermission {
  GRAZIAS_V2 = 'scaleTypePermissionGraziasV2',
  MATH = 'scaleTypePermissionMath',
  NATURE = 'scaleTypePermissionNature',
  DIDACTICS = 'scaleTypePermissionDidactics',
}

enum LicenseType {
  CHILDREN_EVALUATION = 'CHILDREN_EVALUATION',
  EXTERNAL_EVALUATION = 'EXTERNAL_EVALUATION',
  SELF_EVALUATION = 'SELF_EVALUATION',
}
