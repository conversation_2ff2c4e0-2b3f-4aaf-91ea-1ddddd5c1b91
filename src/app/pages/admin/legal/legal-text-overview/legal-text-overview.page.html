<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-title>{{ 'admin.legalText.overview.title' | translate }}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content class="design-v2 content">
  <div class="list">
    <div class="list-entry ion-activatable ripple-parent">
      <div class="navigation-hitbox" (click)="navigateToPrivacyStatementList()">
        <span>{{ 'admin.legalText.overview.tile.privacyStatement.title' | translate }}</span>
        <ion-img src="assets/img/right.png" alt="navigate"></ion-img>
      </div>
    </div>
    <div class="list-entry ion-activatable ripple-parent">
      <div class="navigation-hitbox" (click)="navigateToTermsOfServiceList()">
        <span>{{ 'admin.legalText.overview.tile.termsOfUse.title' | translate }}</span>
        <ion-img src="assets/img/right.png" alt="navigate"></ion-img>
      </div>
    </div>
  </div>
</ion-content>
