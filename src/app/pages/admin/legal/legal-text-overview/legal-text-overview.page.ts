import { Component } from '@angular/core';
import { NavController } from '@ionic/angular';
import {
  getUrlParamFromLegalTextType,
  LegalTextType,
} from '../../../../services/admin/admin.service';

@Component({
  selector: 'app-legal-text-overview',
  templateUrl: './legal-text-overview.page.html',
  styleUrls: ['./legal-text-overview.page.scss'],
})
export class LegalTextOverviewPage {
  constructor(private navController: NavController) {}

  navigateToPrivacyStatementList(): void {
    this.navController.navigateRoot(
      'admin/legal-text-version-list/' +
        getUrlParamFromLegalTextType(LegalTextType.PRIVACY_STATEMENT),
    );
  }

  navigateToTermsOfServiceList(): void {
    this.navController.navigateRoot(
      'admin/legal-text-version-list/' + getUrlParamFromLegalTextType(LegalTextType.TERMS_OF_USE),
    );
  }
}
