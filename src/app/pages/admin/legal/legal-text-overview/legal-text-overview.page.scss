@import 'src/theme/_variables_v2.scss';

.flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
  padding-bottom: 20px;
}
.toolbar-container {
  margin-top: 60px;
}

.main-content {
  height: 100%;
}

.content {
  height: calc(100vh - 128px);
}

.list-entry {
  height: 55px;
  border: solid $color-light-gray 1px;
  border-radius: 8px;
  display: flex;
  margin-top: 8px;
  padding: 5px 16px;
  align-items: center;

  .navigation-hitbox {
    height: 100%;
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  ion-img {
    height: 13px;
    width: 13px;
  }
}

.list {
  padding-left: 94px;
  padding-right: 94px;
  padding-bottom: 20px;
}
