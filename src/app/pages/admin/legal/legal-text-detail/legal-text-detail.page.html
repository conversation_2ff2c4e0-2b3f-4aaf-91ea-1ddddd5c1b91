<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <div class="flex">
        <ion-col class="ion-no-padding">
          <ion-row *ngIf="isPublished" class="ion-no-padding back-button" (click)="navigateBack()">
            <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
            <b class="back-button-img">{{ backButtonTitle | translate }}</b>
          </ion-row>
          <ion-title>{{ title }}</ion-title>
        </ion-col>
        <app-primary-button
          *ngIf="!isPublished && !isLoading"
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="showPublishConfirmationDialog()"
          [isDisabled]="!this.form.valid || isLoading || isSaveButtonLoading"
          [isLoading]="isPublishButtonLoading"
          [label]="'admin.legalText.detail.button.publish' | translate | uppercase"
        >
        </app-primary-button>
      </div>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content class="design-v2 content">
  <div class="page-padding">
    <form [formGroup]="form">
      <app-tab-bar
        [tabNames]="languageKeys"
        [contentStates]="contentStates"
        [showContentStates]="!isPublished"
        (tabSelected)="selectTab($event)"
      >
      </app-tab-bar>
      <div class="spacing"></div>
      <div [ngSwitch]="selectedTabIndex">
        <app-textarea-with-heading
          *ngSwitchCase="0"
          formControlName="translationDe"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationDe.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="1"
          formControlName="translationEn"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationEn.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="2"
          formControlName="translationEs"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationEs.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="3"
          formControlName="translationFr"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationFr.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="4"
          formControlName="translationHu"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationHu.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="5"
          formControlName="translationIt"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationIt.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="6"
          formControlName="translationPt"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationPt.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="7"
          formControlName="translationSl"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationSl.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <app-textarea-with-heading
          *ngSwitchCase="8"
          formControlName="translationZh"
          [label]="'admin.legalText.detail.textarea.label' | translate"
          [inputValue]="form.controls.translationZh.value"
          [errorMessage]="null"
          [setFocus]="true"
          [disabled]="isPublished || isSaveButtonLoading"
          rows="20"
        >
        </app-textarea-with-heading>
        <ion-grid class="button-row" *ngIf="!isPublished && !isLoading">
          <ion-row>
            <ion-col size="6"></ion-col>
            <ion-col size="3">
              <app-primary-button
                expand="block"
                (onClick)="showCancelDialog()"
                [isDisabled]="false"
                [isLoading]="false"
                [border]="true"
                [label]="'admin.legalText.detail.button.close' | translate"
              >
              </app-primary-button>
            </ion-col>
            <ion-col class="save-button" size="3">
              <app-primary-button
                type="submit"
                expand="block"
                (onClick)="save()"
                [isDisabled]="!isFormChanged()"
                [isLoading]="isSaveButtonLoading"
                [label]="'admin.legalText.detail.button.save' | translate"
                [buttonType]="buttonType.GRADIENT"
              >
              </app-primary-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>
    </form>
  </div>
</ion-content>
