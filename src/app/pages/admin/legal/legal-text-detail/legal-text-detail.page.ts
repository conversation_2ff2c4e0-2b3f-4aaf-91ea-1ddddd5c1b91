import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { PrimaryButtonType } from '../../../../modules/shared/components/primary-button/primary-button.component';
import {
  AdminService,
  LegalTextStatus,
  LegalTextType,
  LegalTextUpsertRequest,
  getLegalTextTypeFromUrlParam,
  getUrlParamFromLegalTextType,
} from '../../../../services/admin/admin.service';
import { AlertButtonState, Utils } from '../../../../utils/utils';

@Component({
  selector: 'app-legal-text-detail',
  templateUrl: './legal-text-detail.page.html',
  styleUrls: ['./legal-text-detail.page.scss'],
})
export class LegalTextDetailPage implements OnInit {
  public versionName: string;
  public versionId: string;
  public selectedTabIndex = 0;
  public buttonType = PrimaryButtonType;
  public isPublished = false;
  public legalTextType: LegalTextType;
  public isNewVersion = false;
  public isSaveButtonLoading = false;
  public isPublishButtonLoading = false;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public initialFormValues: any;

  public loadingUi: HTMLIonLoadingElement = null;
  public isLoading = false;
  public form = this.formBuilder.group({
    translationDe: ['', [Validators.required]],
    translationEn: ['', [Validators.required]],
    translationEs: ['', [Validators.required]],
    translationFr: ['', [Validators.required]],
    translationHu: ['', [Validators.required]],
    translationIt: ['', [Validators.required]],
    translationPt: ['', [Validators.required]],
    translationSl: ['', [Validators.required]],
    translationZh: ['', [Validators.required]],
  });

  public languageKeys = [
    'admin.legalText.detail.tabs.label.de',
    'admin.legalText.detail.tabs.label.en',
    'admin.legalText.detail.tabs.label.es',
    'admin.legalText.detail.tabs.label.fr',
    'admin.legalText.detail.tabs.label.hu',
    'admin.legalText.detail.tabs.label.it',
    'admin.legalText.detail.tabs.label.pt',
    'admin.legalText.detail.tabs.label.sl',
    'admin.legalText.detail.tabs.label.zh',
  ];

  constructor(
    private navController: NavController,
    private formBuilder: FormBuilder,
    private alertController: AlertController,
    private translate: TranslateService,
    private route: ActivatedRoute,
    private adminService: AdminService,
    public loadingController: LoadingController,
    private toastController: ToastController,
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.legalTextType = getLegalTextTypeFromUrlParam(params.legalTextType);
      if (params.versionName === 'draft' && params.versionId === undefined) {
        this.isNewVersion = true;
      } else {
        this.versionName = params.versionName;
        this.versionId = params.versionId;
        this.initForm();
      }
    });
  }

  public get contentStates(): boolean[] {
    return [
      this.form.controls.translationDe.value !== '',
      this.form.controls.translationEn.value !== '',
      this.form.controls.translationEs.value !== '',
      this.form.controls.translationFr.value !== '',
      this.form.controls.translationHu.value !== '',
      this.form.controls.translationIt.value !== '',
      this.form.controls.translationPt.value !== '',
      this.form.controls.translationSl.value !== '',
      this.form.controls.translationZh.value !== '',
    ];
  }

  public get title(): string {
    if (this.isLoading) {
      return '';
    }
    switch (this.legalTextType) {
      case LegalTextType.PRIVACY_STATEMENT:
        if (this.versionName === undefined) {
          return this.translate.instant('admin.legaltext.detail.title.privacyPolicy.new');
        } else {
          return this.isPublished
            ? this.translate.instant('admin.legaltext.detail.title.privacyPolicy.edit.published', {
                versionName: this.versionName,
              })
            : this.translate.instant('admin.legaltext.detail.title.privacyPolicy.edit.draft');
        }
      case LegalTextType.TERMS_OF_USE:
        if (this.versionName === undefined) {
          return this.translate.instant('admin.legaltext.detail.title.termsOfUse.new');
        } else {
          return this.isPublished
            ? this.translate.instant('admin.legaltext.detail.title.termsOfUse.edit.published', {
                versionName: this.versionName,
              })
            : this.translate.instant('admin.legaltext.detail.title.termsOfUse.edit.draft');
        }
    }
  }

  get backButtonTitle(): string {
    switch (this.legalTextType) {
      case LegalTextType.PRIVACY_STATEMENT:
        return 'admin.legalText.list.title.privacyStatement';
      case LegalTextType.TERMS_OF_USE:
        return 'admin.legalText.list.title.termsOfUse';
    }
  }

  public async initForm(): Promise<void> {
    this.isLoading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getLegalText(this.versionId, this.legalTextType).subscribe((response) => {
      if (response.success) {
        this.form.controls.translationDe.setValue(response.data.deContent ?? '');
        this.form.controls.translationEn.setValue(response.data.enContent ?? '');
        this.form.controls.translationEs.setValue(response.data.esContent ?? '');
        this.form.controls.translationFr.setValue(response.data.frContent ?? '');
        this.form.controls.translationHu.setValue(response.data.huContent ?? '');
        this.form.controls.translationIt.setValue(response.data.itContent ?? '');
        this.form.controls.translationPt.setValue(response.data.ptContent ?? '');
        this.form.controls.translationSl.setValue(response.data.slContent ?? '');
        this.form.controls.translationZh.setValue(response.data.zhContent ?? '');
        this.versionId = response.data.id;
        this.initialFormValues = this.form.value;
        this.isPublished = response.data.status !== LegalTextStatus.DRAFT;
      } else {
        this.showMessage(
          this.translate.instant('admin.legaltext.detail.error.loadingVersionFailed'),
          false,
        );
        this.navigateBack();
      }
      Object.values(this.form.controls).forEach((control) => {
        control.markAsPristine();
      });
      this.isLoading = false;
      this.loadingUi.dismiss();
    });
  }

  public selectTab(index: number): void {
    this.selectedTabIndex = index;
  }

  public navigateBack(): void {
    this.navController.navigateRoot(
      'admin/legal-text-version-list/' + getUrlParamFromLegalTextType(this.legalTextType),
    );
  }

  public async save(): Promise<void> {
    this.isSaveButtonLoading = true;
    const request = new LegalTextUpsertRequest(
      this.versionId ? this.versionId : null,
      this.form.controls.translationDe.value !== '' ? this.form.controls.translationDe.value : null,
      this.form.controls.translationEn.value !== '' ? this.form.controls.translationEn.value : null,
      this.form.controls.translationEs.value !== '' ? this.form.controls.translationEs.value : null,
      this.form.controls.translationFr.value !== '' ? this.form.controls.translationFr.value : null,
      this.form.controls.translationHu.value !== '' ? this.form.controls.translationHu.value : null,
      this.form.controls.translationIt.value !== '' ? this.form.controls.translationIt.value : null,
      this.form.controls.translationPt.value !== '' ? this.form.controls.translationPt.value : null,
      this.form.controls.translationSl.value !== '' ? this.form.controls.translationSl.value : null,
      this.form.controls.translationZh.value !== '' ? this.form.controls.translationZh.value : null,
    );
    const response = await this.adminService.saveLegalText(request, this.legalTextType).toPromise();
    if (response.success) {
      this.versionId = response.data.id;
      Object.values(this.form.controls).forEach((control) => {
        control.markAsPristine();
      });
      await this.showMessage(this.translate.instant('admin.legaltext.detail.success.saved'), true);
    } else {
      if (response.errorMessage === 'errors.legal_text.draft_already_exists') {
        await this.showMessage(
          this.translate.instant('admin.legaltext.detail.error.draftExists'),
          false,
        );
      } else {
        await this.showMessage(this.translate.instant('global.error.generic'), false);
      }
    }
    this.isSaveButtonLoading = false;
  }

  public async showCancelDialog(): Promise<void> {
    if (!this.isFormChanged()) {
      this.navigateBack();
      return;
    }
    const alert = await this.alertController.create(
      Utils.getAlertDialogConfig_v2(
        this.translate.instant('admin.legaltext.detail.dialog.cancel.title'),
        this.translate.instant('admin.legaltext.detail.dialog.cancel.text'),
        this.translate.instant('admin.legaltext.detail.dialog.cancel.button.confirm'),
        this.translate.instant('admin.legaltext.detail.dialog.cancel.button.cancel'),
        AlertButtonState.DANGER,
        AlertButtonState.NONE,
        () => this.navigateBack(),
      ),
    );
    await alert.present();
  }

  public async showPublishConfirmationDialog(): Promise<void> {
    const alert = await this.alertController.create(
      Utils.getAlertDialogConfig_v2(
        this.translate.instant('admin.legaltext.detail.dialog.publish.title'),
        this.translate.instant('admin.legaltext.detail.dialog.publish.text'),
        this.translate.instant('admin.legaltext.detail.dialog.publish.button.confirm'),
        this.translate.instant('admin.legaltext.detail.dialog.publish.button.cancel'),
        AlertButtonState.OK,
        AlertButtonState.NONE,
        async () => {
          this.isPublishButtonLoading = true;
          if (this.isFormChanged()) {
            await this.save();
          }
          this.adminService
            .publishLegalText(this.versionId, this.legalTextType)
            .subscribe((response) => {
              if (response.success) {
                this.navigateBack();
              } else {
                this.showMessage(this.translate.instant('global.error.generic'), false);
              }
              this.isPublishButtonLoading = false;
            });
        },
      ),
    );
    await alert.present();
  }

  public isFormChanged(): boolean {
    return Object.values(this.form.controls).some((control) => control.dirty);
  }

  private async showMessage(message: string, success: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color: success ? 'success' : 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }
}
