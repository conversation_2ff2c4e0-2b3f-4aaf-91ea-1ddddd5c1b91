import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { LegalTextVersionListPage } from './legal-text-version-list.page';
import { TranslateModule } from '@ngx-translate/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SharedModule } from '../../../../modules/shared/shared.module';

const routes: Routes = [
  {
    path: '',
    component: LegalTextVersionListPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule,
    SharedModule,
    MatCheckboxModule,
  ],
  declarations: [LegalTextVersionListPage],
})
export class LegalTextVersionListPageModule {}
