@import 'src/theme/_variables_v2.scss';

.flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
  padding-bottom: 20px;
}

.back-button-img {
  padding-left: 8px;
  text-transform: uppercase;
  margin: 0;
  font-size: small;
}

.list-entry {
  height: 55px;
  border: solid $color-light-gray 1px;
  border-radius: 8px;
  margin-top: 8px;
  padding: 5px 16px;

  .navigation-hitbox {
    height: 100%;
    width: 100%;
    cursor: pointer;
    display: grid;
    grid-template-columns: 3fr 3fr 2fr;
    align-items: center;

    > span:last-child {
      justify-self: end;
    }

    .current-version {
      padding: 4px 8px;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      background-color: $color-highlight;
    }

    ion-img {
      height: 22px;
      width: 22px;
    }
  }
}

.content {
  height: calc(100vh - 128px);
}

.back-button {
  cursor: pointer;
}

.list {
  padding-left: 94px;
  padding-right: 94px;
  padding-bottom: 20px;
}
