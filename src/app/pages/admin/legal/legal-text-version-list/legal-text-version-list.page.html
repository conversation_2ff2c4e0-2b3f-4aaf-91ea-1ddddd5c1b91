<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <div class="flex">
        <ion-col class="ion-no-padding">
          <ion-row class="ion-no-padding back-button" (click)="navigateBack()">
            <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
            <b class="back-button-img">{{ 'admin.legalText.overview.title' | translate }}</b>
          </ion-row>
          <ion-title>{{ title | translate }}</ion-title>
        </ion-col>
        <app-primary-button
          *ngIf="!hideCreateButton && !isLoading"
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="navigateToLegalTextVersion('draft', null)"
          [isDisabled]="false"
          [isLoading]="false"
          [label]="'admin.legalText.list.button.new' | translate | uppercase"
        >
        </app-primary-button>
      </div>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content class="design-v2 content">
  <div *ngIf="legalTextVersions.length > 0 || isLoading; else emptyView" class="list">
    <div *ngFor="let version of legalTextVersions" class="list-entry ion-activatable ripple-parent">
      <div class="navigation-hitbox" (click)="navigateToLegalTextVersion(version.name, version.id)">
        <span>{{ getVersionName(version.name) }}</span>
        <span translate>
          {{ version.status !== LegalTextStatus.DRAFT ? 'admin.legalText.list.tile.status.published'
          : 'admin.legalText.list.tile.status.draft' }}
        </span>
        <span
          translate
          class="current-version"
          *ngIf="version.status === LegalTextStatus.PUBLISHED"
        >
          {{ 'admin.legalText.list.tile.currentlyValid' }}
        </span>
        <span *ngIf="version.status === LegalTextStatus.DRAFT">
          <ion-img src="assets/svg/edit.svg"></ion-img>
        </span>
        <span *ngIf="version.status === LegalTextStatus.HISTORIC"></span>
      </div>
    </div>
  </div>
</ion-content>

<ng-template #emptyView>
  <app-empty-view
    [title]="'admin.legalText.list.emptyView.title' | translate"
    [text]="'admin.legalText.list.emptyView.text' | translate"
    imagePath="assets/svg/legal.svg"
  >
  </app-empty-view>
</ng-template>
