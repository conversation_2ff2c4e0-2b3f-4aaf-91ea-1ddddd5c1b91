import { Component, OnInit } from '@angular/core';
import { LoadingController, NavController, ToastController } from '@ionic/angular';
import { ActivatedRoute } from '@angular/router';
import { Utils } from '../../../../utils/utils';
import { TranslateService } from '@ngx-translate/core';
import {
  AdminService,
  getLegalTextTypeFromUrlParam,
  getUrlParamFromLegalTextType,
  LegalTextStatus,
  LegalTextType,
} from '../../../../services/admin/admin.service';

@Component({
  selector: 'app-legal-text-version-list',
  templateUrl: './legal-text-version-list.page.html',
  styleUrls: ['./legal-text-version-list.page.scss'],
})
export class LegalTextVersionListPage implements OnInit {
  public legalTextType: LegalTextType;
  public loadingUi: HTMLIonLoadingElement = null;
  public isLoading = false;
  public legalTextVersions: Array<LegalTextVersion> = [];
  public LegalTextStatus = LegalTextStatus;

  constructor(
    private navController: NavController,
    private route: ActivatedRoute,
    public loadingController: LoadingController,
    public translate: TranslateService,
    private adminService: AdminService,
    private toastController: ToastController,
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.legalTextType = getLegalTextTypeFromUrlParam(params.legalTextType);
    });
    this.fetchLegalTextVersions();
  }

  get title(): string {
    switch (this.legalTextType) {
      case LegalTextType.PRIVACY_STATEMENT:
        return 'admin.legalText.list.title.privacyStatement';
      case LegalTextType.TERMS_OF_USE:
        return 'admin.legalText.list.title.termsOfUse';
    }
  }

  get hideCreateButton(): boolean {
    return (
      this.legalTextVersions.filter((version) => version.status === LegalTextStatus.DRAFT).length >
      0
    );
  }

  public navigateBack(): void {
    this.navController.navigateRoot('admin/legal-text-overview');
  }

  public getVersionName(name: string): string {
    return name === null ? this.translate.instant('admin.legalText.list.label.draft') : name;
  }

  public navigateToLegalTextVersion(versionName: string, versionId: string): void {
    let url = 'admin/legal-text-detail/' + getUrlParamFromLegalTextType(this.legalTextType);
    if (versionName) {
      url = url + `/${versionName}`;
    } else {
      url = url + '/draft';
    }
    if (versionId) {
      url = url + `/${versionId}`;
    }
    this.navController.navigateRoot(url);
  }

  public async fetchLegalTextVersions(): Promise<void> {
    this.isLoading = true;
    this.loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await this.loadingUi.present();
    this.adminService.getLegalTextList(this.legalTextType).subscribe((response) => {
      if (response.success) {
        this.legalTextVersions = response.data.versions.map(
          (value) => new LegalTextVersion(value.id, value.version, value.status),
        );
      } else {
        this.showMessage(this.translate.instant('global.error.generic'), false);
      }
      this.isLoading = false;
      this.loadingUi.dismiss();
    });
  }

  private async showMessage(message: string, success: boolean): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color: success ? 'success' : 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }
}

class LegalTextVersion {
  id: string;
  name: string;
  status: LegalTextStatus;

  constructor(id: string, name: string, status: LegalTextStatus) {
    this.id = id;
    this.name = name;
    this.status = status;
  }
}
