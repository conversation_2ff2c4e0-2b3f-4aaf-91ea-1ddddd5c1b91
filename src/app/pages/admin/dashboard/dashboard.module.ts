import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { DashboardPage } from './dashboard.page';
import { TranslateModule } from '@ngx-translate/core';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { GraziasMissingTranslationHandler } from '../../../utils/missingTranslationHandler';
import { SharedModule } from '../../../modules/shared/shared.module';

const routes: Routes = [
  {
    path: '',
    component: DashboardPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
    RouterModule.forChild(routes),
    NgxDatatableModule,
    SharedModule,
  ],
  declarations: [DashboardPage],
})
export class DashboardPageModule {}
