<div class="main-content">
  <ion-header class="header-v2">
    <ion-toolbar class="toolbar-v2">
      <div class="flex">
        <ion-title>{{ 'admin.dashboard.title' | translate }}</ion-title>
        <app-primary-button
          type="submit"
          expand="block"
          [border]="true"
          (onClick)="showVersionNumber()"
          [isDisabled]="false"
          [isLoading]="false"
          [label]="'version' | translate | uppercase"
        >
        </app-primary-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <ion-content class="design-v2 content">
    <div class="page-padding">
      <ion-chip class="custom-chip active" (click)="navigateCreateUserForm(adminOption.CREATE)">
        <ion-label>{{ 'admin.dashboard.button.createUser' | translate }}</ion-label>
      </ion-chip>
      <ion-chip class="custom-chip active" (click)="navigateToEducatorSearch()">
        <ion-label>{{ 'admin.dashboard.button.editEducator' | translate }}</ion-label>
      </ion-chip>
      <ion-grid>
        <ion-row>
          <ion-col>
            <div>
              <ngx-datatable
                [rows]="users"
                [columns]="userColumns"
                columnMode="flex"
                rowHeight="auto"
                [headerHeight]="70"
                [reorderable]="false"
                (activate)="handleActivate($event)"
                [messages]="{ emptyMessage: translate.instant('admin.dashboard.label.noUser') }"
                style="width: 100%"
              >
                <ngx-datatable-column
                  *ngFor="let col of userColumns"
                  [name]="col.name"
                  [prop]="col.prop"
                  [flexGrow]="col.flexGrow"
                  [pipe]="col.pipe"
                >
                  <ng-template ngx-datatable-cell-template let-row="row" let-value="value">
                    <span [ngClass]="{ 'inactive-text': !isSelectable(row) }">
                      {{ col.prop === 'role' ? translate.instant('model.userRole.' + value) : value }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>
              </ngx-datatable>
            </div>
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  </ion-content>
</div>
