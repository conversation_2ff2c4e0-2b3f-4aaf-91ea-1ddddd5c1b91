import { Component, OnInit } from '@angular/core';
import { Alert<PERSON>ontroller, LoadingController, NavController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { TableColumn } from '@swimlane/ngx-datatable';
import { IsoDatePipe } from 'src/app/pipes/iso-date.pipe';
import { AdminCreateUserRouteState, RouteStateService } from 'src/app/services/RouteStateService';
import { AdminService } from 'src/app/services/admin/admin.service';
import { UserAdministrationOption, UserListItemViewModel, Utils } from 'src/app/utils/utils';
import { VERSION_NUMBER } from 'src/version';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.page.html',
  styleUrls: ['./dashboard.page.scss'],
})
export class DashboardPage implements OnInit {
  public userColumns: TableColumn[] = [];
  public users = new Array<UserListItemViewModel>();
  public isLoading = false;
  public adminOption = UserAdministrationOption;

  constructor(
    private navController: NavController,
    private alertController: AlertController,
    public translate: TranslateService,
    private adminService: AdminService,
    private loadingController: LoadingController,
    private toastController: ToastController,
    private routeStateService: RouteStateService,
  ) {}

  ngOnInit(): void {
    this.defineColumns();
    this.fetchUserList();
  }

  navigateCreateUserForm(mode: UserAdministrationOption, user?: UserListItemViewModel): void {
    this.routeStateService.updateAdminState(new AdminCreateUserRouteState(user, mode));
    this.navController.navigateForward(`/admin/create-user-form`);
  }

  navigateToEducatorSearch(): void {
    this.navController.navigateForward(`/admin/educator-search`);
  }

  defineColumns(): void {
    this.userColumns = [
      {
        prop: 'firstName',
        name: this.translate.instant('admin.dashboard.label.firstName'),
        flexGrow: 2,
      },
      {
        prop: 'lastName',
        name: this.translate.instant('admin.dashboard.label.lastName'),
        flexGrow: 2,
      },
      {
        prop: 'trackingId',
        name: this.translate.instant('global.label.trackingId'),
        flexGrow: 2,
      },
      {
        prop: 'mailAddress',
        name: this.translate.instant('admin.dashboard.label.mail'),
        flexGrow: 4,
      },
      {
        prop: 'role',
        name: this.translate.instant('admin.dashboard.label.role'),
        flexGrow: 2,
      },
      {
        prop: 'createdAt',
        name: this.translate.instant('admin.dashboard.label.created'),
        flexGrow: 3,
        pipe: new IsoDatePipe(this.translate.currentLang),
      },
    ];
  }

  async showVersionNumber(): Promise<void> {
    const alert = await this.alertController.create({
      header: this.translate.instant('global.version'),
      message: VERSION_NUMBER,
      buttons: [this.translate.instant('global.button.ok')],
    });

    await alert.present();
  }

  async fetchUserList(): Promise<any> {
    const loadingUi = await this.loadingController.create(
      Utils.getLoadingUiConfig_v2(this.translate.instant('global.label.loadingData')),
    );
    await loadingUi.present();
    this.isLoading = true;
    return new Promise<void>((resolve) => {
      this.adminService.getAdminAndEvaluatorUser().subscribe((resp) => {
        if (resp.success) {
          this.users = [];
          resp.data.users.forEach((user) => {
            this.users.push(new UserListItemViewModel(user.firstName, user.lastName, user.mailAddress, user.userName, user.trackingId, user.role, user.createdAt));
          });
          resp.data.pendingUsers.forEach((user) => {
            this.users.push(new UserListItemViewModel(user.firstName, user.lastName, user.mailAddress, user.userName, 'Nicht aktiviert', user.role, null));
          })
        } else {
          this.showToast(this.translate.instant('admin.dashboard.label.loadingError'), 'danger');
        }

        loadingUi.dismiss();
        this.isLoading = false;
        resolve();
      });
    });
  }

  onActivate(event: any): void {
    if (event.type === 'click') {
      const user = event.row;
      this.navigateCreateUserForm(this.adminOption.UPDATE, user);
    }
  }

  async showToast(message: string, color: string): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color,
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    toast.present();
  }

  handleActivate(event: any): void {
    if (this.isSelectable(event.row)) {
      this.onActivate(event);
    }
  }

  isSelectable(row: any): boolean {
    return row.createdAt !== null;
  }
}
