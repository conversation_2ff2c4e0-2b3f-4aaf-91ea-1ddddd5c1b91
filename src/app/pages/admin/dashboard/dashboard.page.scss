@import 'src/theme/_variables_v2.scss';

.flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
}

.main-content {
  height: 100%;
}

ion-content {
  .side-pad {
    margin-top: -0.5rem;
    padding-left: 2.625rem;
    padding-right: 2.625rem;
    display: flex;
    justify-content: center;
  }
}

.content {
  height: calc(100vh - 128px);
}

.round-bttn {
  margin-bottom: 5px;

  img {
    height: 3rem;
    width: 3rem;
  }
}

.bttn-container {
  margin-top: 1rem;
}

.bttn-row {
  text-align: center;
  max-width: 1280px;
  margin: auto;

  ion-col {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
}

.bttn-text {
  color: var(--ion-color-primary);

  &.critical {
    color: #f35454;
  }
}

.cursor-pointer {
  cursor: pointer;
}

app-export-modal {
  --border-radius: 0;
}

.custom-chip {
  color: white;
  background-color: $color-primary-v2;
  border-radius: 7px;
  margin: 12px 12px 8px 0;
  padding: 0 16px;
  height: 48px;

  ion-label {
    font-size: 16px;
  }

  &.critical {
    background-color: $color-danger;
    color: white;
  }
}

.page-padding {
  padding-left: 94px;
  padding-right: 94px;
  padding-bottom: 20px;
}

.inactive-text {
  color: #999999;
}
