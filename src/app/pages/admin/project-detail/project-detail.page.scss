@import 'src/theme/_variables_v2.scss';

.horizontal-spacer {
  height: 3rem;
}

.horizontal-spacer-small {
  height: 1rem;
}

.bottom-safe-area {
  height: 80px;
}

.indentation {
  margin: 0 4.6rem;
  max-width: var(--content-max-width);
}

ion-grid {
  &.form-spacing {
    ion-col {
      padding-top: 16px;
    }
  }
}

.content {
  height: calc(100vh - 128px);
}

.back-button {
  padding-left: 8px;
  text-transform: uppercase;
  margin: 0;
  font-size: small;
}

.button-row {
  cursor: pointer;
}

.section-padding {
  padding-top: 48px;
}

.hint-text {
  font-size: 16px;
  line-height: 18px;
  color: $color-hint-text-grey;
}

.checkbox-title {
  padding-top: 42px;
  font-size: 13px;
  line-height: 18px;
  font-weight: bold;
  text-transform: uppercase;
  color: $color-hint-text-grey;
}

.error-message {
  color: var(--ion-color-danger-V2);
  font-size: 13px;
  line-height: 18px;
  font-weight: bold;

  &.with-padding {
    padding-top: 40px;
  }
}
