import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NavController, ToastController } from '@ionic/angular';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { FormValidationHelper } from '../../../modules/organization/helpers/form-validation-helper';
import { PrimaryButtonType } from '../../../modules/shared/components/primary-button/primary-button.component';
import { ActivatedRoute } from '@angular/router';
import { Project, ProjectDetailMode } from '../projects/projects.page';
import {
  AdminService,
  CreateProjectRequest,
  UpdateProjectRequest,
} from '../../../services/admin/admin.service';
import { EmptyResponse } from '../../../services/api/response/empty-response';

@Component({
  selector: 'app-project-detail',
  templateUrl: './project-detail.page.html',
  styleUrls: ['./project-detail.page.scss'],
})
export class ProjectDetailPage implements OnInit {
  constructor(
    public translate: TranslateService,
    private navController: NavController,
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    private toastController: ToastController,
    private adminService: AdminService,
  ) {}

  public buttonType = PrimaryButtonType;
  public project: Project;
  public projectDetailMode: ProjectDetailMode;
  public projectNameErrorMessage?: string;

  public projectForm = this.formBuilder.group({
    name: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(64)]],
  });

  ngOnInit(): void {
    this.route.queryParams.subscribe(async (params) => {
      this.project = params.project;
      this.projectDetailMode = params.projectDetailMode;
    });
    this.initForm();
  }

  private initForm(): void {
    if (this.isUpdateMode()) {
      this.projectForm.controls.name.setValue(this.project.name);
    }
  }

  getToolbarTitle(): string {
    const suffix = this.isUpdateMode() ? ` ${this.project.name}` : '';
    return `${this.translate.instant('Projekt')}${suffix}`;
  }

  navigateBack(): void {
    this.navController.navigateRoot('admin/projects');
  }

  getSubmitButtonLabel(): string {
    const key = this.isUpdateMode() ? 'global.save' : 'global.create';
    return this.translate.instant(key);
  }

  submitForm(): void {
    this.projectForm.controls.name.setValue(this.projectForm.controls.name.value.trim());
    const projectName = this.projectForm.controls.name.value;
    this.projectNameErrorMessage = FormValidationHelper.handleValidationError(
      this.projectForm.controls.name,
    );
    if (this.projectForm.valid) {
      this.isUpdateMode()
        ? this.updateProject(new UpdateProjectRequest(this.project.id, projectName))
        : this.createProject(new CreateProjectRequest(projectName));
    }
  }

  private createProject(request: CreateProjectRequest): void {
    this.adminService.createProject(request).subscribe((response) => {
      this.takeCreateOrUpdateProjectResponse(response, request.projectName);
    });
  }

  private updateProject(request: UpdateProjectRequest): void {
    this.adminService.updateProject(request).subscribe((response) => {
      this.takeCreateOrUpdateProjectResponse(response, request.projectName);
    });
  }

  private takeCreateOrUpdateProjectResponse(response: EmptyResponse, projectName: string): void {
    if (response.success) {
      this.showMessage(
        true,
        this.translate.instant('admin.create_project.success_message', { project: projectName }),
        true,
      );
    } else {
      if (response.httpStatus === 409) {
        this.projectNameErrorMessage = this.translate.instant(
          'childrenEvaluation.license.name_conflict',
        );
      } else {
        this.showMessage(true, this.translate.instant('global.error.generic'), false);
      }
    }
  }

  private isUpdateMode = (): boolean => this.projectDetailMode === ProjectDetailMode.UPDATE;

  private async showMessage(
    shouldReturn: boolean,
    message: string,
    success: boolean,
  ): Promise<void> {
    const toast = await this.toastController.create({
      message,
      color: success ? 'success' : 'danger',
      position: 'bottom',
      duration: 10000,
      buttons: [
        {
          text: this.translate.instant('global.alert.close'),
          role: 'cancel',
        },
      ],
    });
    await toast.present();
    if (shouldReturn) {
      this.navigateBack();
    }
  }
}
