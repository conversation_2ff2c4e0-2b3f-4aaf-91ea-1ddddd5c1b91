<ion-header class="header-v2 design-v2">
  <ion-toolbar class="toolbar-v2">
    <ion-col class="ion-no-padding">
      <ion-row class="ion-no-padding button-row" (click)="navigateBack()">
        <ion-img src="assets/svg/back_small.svg" alt="back"></ion-img>
        <b class="back-button">{{'projects.title' | translate}}</b>
      </ion-row>
      <ion-title>{{getToolbarTitle()}}</ion-title>
    </ion-col>
  </ion-toolbar>
</ion-header>

<ion-content class="design-v2 content">
  <ion-grid class="indentation">
    <ion-row>
      <ion-col>
        <form [formGroup]="projectForm" (ngSubmit)="submitForm()" class="input-container">
          <ion-grid class="form-spacing">
            <ion-row>
              <ion-col>
                <app-input-with-heading
                  formControlName="name"
                  type="text"
                  [label]="'projects.label.name' | translate"
                  [setFocus]="true"
                  [inputValue]="projectForm.controls.name.value"
                  [errorMessage]="projectNameErrorMessage"
                >
                </app-input-with-heading>
              </ion-col>
            </ion-row>

            <div class="horizontal-spacer"></div>
            <ion-row>
              <ion-col size="6"></ion-col>
              <ion-col size="3">
                <app-primary-button
                  expand="block"
                  (onClick)="navigateBack()"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [border]="true"
                  [label]="'global.button.cancel' | translate"
                >
                </app-primary-button>
              </ion-col>
              <ion-col size="3">
                <app-primary-button
                  type="submit"
                  expand="block"
                  [isDisabled]="false"
                  [isLoading]="false"
                  [label]="getSubmitButtonLabel()"
                  [buttonType]="buttonType.GRADIENT"
                >
                </app-primary-button>
              </ion-col>
            </ion-row>
            <div class="bottom-safe-area"></div>
          </ion-grid>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
