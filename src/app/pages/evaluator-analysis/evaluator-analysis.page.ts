import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import moment from 'moment';
import { EvaluationService } from 'src/app/services/evaluation/evaluation.service';
import { PrintService, PrintStyles } from 'src/app/services/print.service';
import { Evaluation } from '../../services/evaluation/models/evaluation';
import { ScaleService } from '../../services/evaluation/scale.service';

export class EvaluationViewModel {
  public institutionName = '';
  public dateOfSurvey: Date;
  public customerServicesId = '';
  public dateOfSurveyFormatted = '';
  public dateOfSurveyBarChartFormatted = '';

  constructor(
    public evaluation: Evaluation,
    public translate: TranslateService,
    periodOfSurveys?: string | null,
  ) {
    this.institutionName = evaluation.institutionName ? evaluation.institutionName : '';
    this.customerServicesId = evaluation.customerServiceId ? evaluation.customerServiceId : '';
    this.dateOfSurvey = moment(evaluation.dateOfSurvey).toDate();
    this.dateOfSurveyFormatted =
      periodOfSurveys !== null
        ? periodOfSurveys
        : moment(evaluation.dateOfSurvey).format(this.translate.instant('global.date.dateOnly'));
    this.dateOfSurveyBarChartFormatted =
      periodOfSurveys !== null
        ? periodOfSurveys
        : moment(evaluation.dateOfSurvey).format('D MMM YY');
  }
}

@Component({
  selector: 'app-evaluator-analysis',
  templateUrl: './evaluator-analysis.page.html',
  styleUrls: ['./evaluator-analysis.page.scss'],
})
export class EvaluatorAnalysisPage {
  @ViewChild('printSection', { read: ElementRef, static: false }) printSection: ElementRef;

  public evaluation: Evaluation;
  public isLoading = false;
  public isError = false;
  public errorMessage = '';

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private evaluationService: EvaluationService,
    private translate: TranslateService,
    private printService: PrintService,
    private loadingController: LoadingController,
    public scaleService: ScaleService,
  ) {}

  ionViewWillEnter(): void {
    this.fetchRouteParams();
  }

  fetchRouteParams(): void {
    this.route.params.subscribe((param) => {
      this.translate.use(param.lang).subscribe(() => {
        this.http.get(`./assets/i18n/${param.lang}.json`).subscribe((translationFile) => {
          this.translate.setTranslation(param.lang, translationFile);
          this.loadEvalResults(param.accessId, param.evaluationRemoteId);
        });
      });
    });
  }

  async loadEvalResults(accessId: string, evaluationRemoteId: string): Promise<void> {
    if (this.isLoading) {
      return;
    }

    this.isLoading = true;

    const loadingUi = await this.loadingController.create({
      message: '',
    });
    await loadingUi.present();

    this.evaluationService.getEvaluationResult(accessId, evaluationRemoteId).subscribe((resp) => {
      if (resp.success) {
        this.evaluation = resp.data;
      } else {
        this.isError = true;
        this.errorMessage = this.translate.instant('global.error.generic');
      }

      this.isLoading = false;
      loadingUi.dismiss();
    });
  }

  customPrint(): void {
    const printContent = this.printSection.nativeElement;
    this.printService.openBrowserPrintDialog(printContent, PrintStyles.EVALUATOR_ANALYSIS);
  }
}
