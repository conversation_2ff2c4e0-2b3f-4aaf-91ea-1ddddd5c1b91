import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { EvaluatorAnalysisPage } from './evaluator-analysis.page';
import { TranslateModule } from '@ngx-translate/core';
import { GraziasMissingTranslationHandler } from '../../utils/missingTranslationHandler';
import { EvaluationReportComponent } from './evaluation-report/evaluation-report.component';
import { SharedModule } from '../../modules/shared/shared.module';

const routes: Routes = [
  {
    path: '',
    component: EvaluatorAnalysisPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule.forChild({
      missingTranslationHandler: { provide: GraziasMissingTranslationHandler },
    }),
    SharedModule,
  ],
  declarations: [EvaluatorAnalysisPage, EvaluationReportComponent],
  exports: [EvaluationReportComponent],
})
export class EvaluatorAnalysisPageModule {}
