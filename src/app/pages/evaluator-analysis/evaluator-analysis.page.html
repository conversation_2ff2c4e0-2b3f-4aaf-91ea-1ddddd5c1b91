<ion-content *ngIf="isError">{{ errorMessage }}</ion-content>
<ion-content *ngIf="!isError && !isLoading && evaluation">
  <ion-header no-border>
    <ion-toolbar>
      <div class="print-bttn-container">
        <ion-button size="large" class="print-bttn" (click)="customPrint()"
          >{{'global.button.print' | translate}}
        </ion-button>
      </div>
    </ion-toolbar>
  </ion-header>

  <app-evaluation-report
    #printSection
    class="screen-container"
    [evaluation]="evaluation"
    [isAggregated]="false"
  >
  </app-evaluation-report>
</ion-content>
