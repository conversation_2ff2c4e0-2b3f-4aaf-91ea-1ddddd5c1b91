import { Component, Input, OnInit } from '@angular/core';
import { Trait } from '../../../services/evaluation/models/trait';
import { Educator } from '../../../services/evaluation/models/educator';
import { TranslateService } from '@ngx-translate/core';
import { ScaleService } from '../../../services/evaluation/scale.service';
import { ScaleDefinition } from '../../../services/evaluation/models/scale-definition';
import { EvaluationViewModel } from '../evaluator-analysis.page';
import { Evaluation } from '../../../services/evaluation/models/evaluation';

@Component({
  selector: 'app-evaluation-report',
  templateUrl: './evaluation-report.component.html',
})
export class EvaluationReportComponent implements OnInit {
  @Input() public evaluation: Evaluation;
  @Input() public isAggregated: boolean;

  public evaluationViewModel: EvaluationViewModel;
  public traits: Array<Trait> = [];
  public educators: Array<Educator> = [];

  constructor(
    public scaleService: ScaleService,
    public translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.evaluationViewModel = new EvaluationViewModel(this.evaluation, this.translate);
    this.evaluation.educators.forEach((it) => this.educators.push(it));
    this.evaluation.scales.forEach((scale) =>
      scale.traits.forEach((trait) => this.traits.push(trait)),
    );
  }

  isGraziasScale(scale: ScaleDefinition): boolean {
    return scale.includes('GRAZIAS');
  }

  getTraitsForScale(scaleDef: ScaleDefinition): Array<Trait> {
    return Array.from(
      this.evaluationViewModel.evaluation.getScaleFromDefinition(scaleDef).traits.values(),
    );
  }
}
