<ion-grid>
  <div
    *ngFor="
      let scaleDef of evaluationViewModel.evaluation.getSortedScaleDefinitions();
      let index = index
    "
  >
    <ion-row>
      <ion-col size="4"></ion-col>
      <ion-col size="4">
        <div>
          <app-evaluator-analysis-title
            [scale]="scaleDef"
            [title]="evaluationViewModel.institutionName"
            [customerServiceId]="evaluationViewModel.customerServicesId"
            [date]="evaluationViewModel.dateOfSurveyFormatted"
            [hasPageBreakBefore]="index !== 0"
            [isAggregated]="isAggregated"
          ></app-evaluator-analysis-title>
        </div>
      </ion-col>
      <ion-col size="4"></ion-col>
    </ion-row>
    <ion-row *ngIf="isGraziasScale(scaleDef)">
      <ion-col size="12">
        <app-bubble-chart
          [title]="
            scaleService.getScaleAndAgeLocalizedString(scaleDef, evaluationViewModel.evaluation)
          "
          [evaluation]="evaluationViewModel.evaluation"
          [scaleDefinition]="scaleDef"
        ></app-bubble-chart>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="12">
        <div *ngFor="let trait of getTraitsForScale(scaleDef)">
          <app-trait-analysis-team [trait]="trait" [scale]="scaleDef" [educators]="educators">
          </app-trait-analysis-team>
        </div>
      </ion-col>
    </ion-row>
  </div>
  <ion-row>
    <ion-col size="12">
      <div *ngFor="let educator of educators">
        <div *ngFor="let scaleDef of evaluationViewModel.evaluation.getSortedScaleDefinitions()">
          <app-evaluator-analysis-title
            [scale]="scaleDef"
            [title]="evaluationViewModel.institutionName"
            [name]="educator.fullName()"
            [date]="evaluationViewModel.dateOfSurveyFormatted"
            [hasPageBreakBefore]="true"
            [isAggregated]="isAggregated"
          >
          </app-evaluator-analysis-title>
          <app-bubble-chart
            *ngIf="isGraziasScale(scaleDef)"
            [title]="
              scaleService.getScaleAndAgeLocalizedString(scaleDef, evaluationViewModel.evaluation)
            "
            [evaluation]="evaluationViewModel.evaluation"
            [educator]="educator"
            [scaleDefinition]="scaleDef"
          ></app-bubble-chart>
          <div *ngFor="let trait of getTraitsForScale(scaleDef)">
            <app-trait-analysis [trait]="trait" [educators]="[educator]" [scale]="scaleDef">
            </app-trait-analysis>
          </div>
        </div>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
