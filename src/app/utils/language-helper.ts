export enum Languages {
  DE = 'de',
  EN = 'en',
  IT = 'it',
  HU = 'hu',
  PT = 'pt',
  SL = 'sl',
  FR = 'fr',
  ES = 'es',
  ZH = 'zh',
}

export function getSurveyLanguages(): string[] {
  return [Languages.DE, Languages.EN, Languages.PT, Languages.FR, Languages.ES];
}

export function getSurveyLanguage(lang: string): Languages {
  switch (lang.toLowerCase()) {
    case 'de':
      return Languages.DE;
    case 'pt':
      return Languages.PT;
    case 'fr':
      return Languages.FR;
    case 'es':
      return Languages.ES;
    default:
      return Languages.EN;
  }
}

export function getLanguageTranslationKey(language: Languages): string {
  switch (language) {
    case Languages.DE:
      return 'survey.languages.german';
    case Languages.EN:
      return 'survey.languages.english';
    case Languages.PT:
      return 'survey.languages.portuguese';
    case Languages.FR:
      return 'survey.languages.french';
    case Languages.ES:
      return 'survey.languages.spanish';
    case Languages.ZH:
      return 'survey.languages.simplified_chinese';
  }
}

export function getLanguageFromCode(code: string): Languages {
  switch (code) {
    case 'de':
      return Languages.DE;
    case 'en':
      return Languages.EN;
    case 'pt':
      return Languages.PT;
    case 'fr':
      return Languages.FR;
    case 'es':
      return Languages.ES;
    case 'zh':
      return Languages.ZH;
    default:
      return Languages.DE;
  }
}
