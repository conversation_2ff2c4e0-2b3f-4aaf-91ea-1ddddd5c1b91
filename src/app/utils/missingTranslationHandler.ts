import { Injectable } from '@angular/core';
import { MissingTranslationHandler, MissingTranslationHandlerParams } from '@ngx-translate/core';

import enJson from '../../assets/i18n/en.json';

function getI18nFallback(translationFile, translationKey): string {
  try {
    let value = translationFile;
    const keySegments = translationKey.split('.');
    for (const item of keySegments) {
      value = value[item];
    }
    return value;
  } catch {
    return translationKey;
  }
}

@Injectable()
export class GraziasMissingTranslationHandler implements MissingTranslationHandler {
  handle(params: MissingTranslationHandlerParams): string {
    return getI18nFallback(enJson, params.key);
  }
}
