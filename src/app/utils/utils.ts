import { LoadingOptions } from '@ionic/angular';
import { UserRole } from '../services/user/user.service';

export enum UserAdministrationOption {
  CREATE = 'create',
  UPDATE = 'update',
}

export class UserListItemViewModel {
  public firstName?: string;
  public lastName?: string;
  public mailAddress?: string;
  public userName: string;
  public trackingId?: string;
  public role: UserRole;
  public createdAt?: Date;

  constructor(firstName?: string, lastName?: string, mailAddress?: string, userName?: string, trackingId?: string, role?: UserRole, createdAt?: Date) {
    this.firstName = firstName;
    this.lastName = lastName;
    this.mailAddress = mailAddress;
    this.userName = userName;
    this.trackingId = trackingId;
    this.role = role;
    this.createdAt = createdAt;
  }

}

export class Utils {
  static getLoadingUiConfig_v2(message: string): LoadingOptions {
    return {
      cssClass: 'design-v2',
      spinner: 'bubbles',
      message,
    };
  }

  static getAlertDialogConfig_v2(
    title: string,
    message: string,
    confirmButtonText: string,
    cancelButtonText: string,
    confirmButtonState: AlertButtonState,
    cancelButtonState: AlertButtonState,
    confirmAction: VoidFunction,
  ) {
    const confirmButtonCssClass = this.resolveButtonState(confirmButtonState);
    const cancelButtonCssClass = this.resolveButtonState(cancelButtonState);

    return {
      header: title,
      message,
      cssClass: 'design-v2 alert-v2',
      buttons: [
        {
          text: cancelButtonText,
          cssClass: cancelButtonCssClass,
          role: 'cancel',
        },
        {
          text: confirmButtonText,
          cssClass: confirmButtonCssClass,
          handler: () => {
            confirmAction();
          },
        },
      ],
      backdropDismiss: false,
    };
  }

  static getOkDialogConfig_v2(
    title: string,
    message: string,
    confirmButtonText: string,
    confirmButtonState: AlertButtonState,
    confirmAction: VoidFunction,
  ) {
    const confirmButtonCssClass = this.resolveButtonState(confirmButtonState);

    return {
      header: title,
      message,
      cssClass: 'design-v2 alert-v2',
      buttons: [
        {
          text: confirmButtonText,
          cssClass: confirmButtonCssClass,
          handler: () => {
            confirmAction();
          },
        },
      ],
      backdropDismiss: false,
    };
  }

  static copyToClipboard(value: string) {
    navigator.clipboard.writeText(value);
  }

  private static resolveButtonState(state: AlertButtonState): string {
    const baseClass = 'alert-button';
    switch (state) {
      case AlertButtonState.NONE:
        return baseClass + ' none';
      case AlertButtonState.OK:
        return baseClass + ' ok';
      case AlertButtonState.DANGER:
        return baseClass + ' danger';
    }
  }
}

export enum AlertButtonState {
  NONE,
  OK,
  DANGER,
}

export enum UiVersion {
  v1,
  v2,
}
