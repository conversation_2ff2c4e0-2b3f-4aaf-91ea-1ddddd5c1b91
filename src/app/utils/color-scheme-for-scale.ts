import { ScaleDefinition } from '../services/evaluation/models/scale-definition';
import { Trait } from '../services/evaluation/models/trait';

export enum ColorTargets {
  TRAIT_BG_COLOR = 'traitBg',
  DIVIDER_COLOR = 'divider',
}

export function getColorSchemeFor(
  target: ColorTargets,
  scale: ScaleDefinition,
  trait: Trait,
): string {
  let cssClass = '';

  cssClass = scale.includes('DIDACTICS') ? `didactics-${trait.id}` : cssClass + '';
  cssClass = scale.includes('GRAZIAS') ? `grazias-${trait.id}` : cssClass + '';
  cssClass = scale.includes('MATH') ? `math-${trait.id}` : cssClass + '';
  cssClass = scale.includes('NATURE') ? `nature-${trait.id}` : cssClass + '';

  switch (target) {
    case ColorTargets.TRAIT_BG_COLOR:
      cssClass = `branch-title-wrapper ${cssClass}-title`;
      break;
    case ColorTargets.DIVIDER_COLOR:
      cssClass = `vertical-divider ${cssClass}-divider`;
      break;
    default:
      break;
  }
  return cssClass;
}
