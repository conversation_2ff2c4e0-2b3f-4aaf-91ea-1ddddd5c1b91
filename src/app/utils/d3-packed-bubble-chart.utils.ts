import * as d3 from 'd3';

export class D3PackedBubbleChartUtils {
  constructor() {}

  static renderChart(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    dataset: any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    chartId: any,
    height: number,
    width: number,
    padding = 20,
  ): void {
    const nodes = d3
      .hierarchy(dataset)
      .sum((d) => d.count)
      .sort((a, b) => -(a.value - b.value));

    const bubble = d3.pack().size([width, height]).padding(padding);

    const svg = d3
      .select(chartId)
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .attr('viewBox', [0, 0, width, height])
      .attr('text-anchor', 'middle')
      .attr('class', 'bubble');

    const node = svg
      .selectAll('.node')
      .data(bubble(nodes).descendants())
      .enter()
      .filter((d) => !d.children)
      .append('g')
      .attr('class', 'node')
      .attr('transform', (d) => `translate(${d.y}, ${d.x})`)
      .style('fill', (d) => d.data.color);

    node
      .append('circle')
      .attr('x', (d) => d.x)
      .attr('y', (d) => d.y)
      .attr('r', (d) => d.r)
      .style('fill', (d) => d.data.color);

    const text = node.append('text').style('text-anchor', 'middle');

    text
      .selectAll('tspan')
      .data((d) => d.data.name.split('\n'))
      .join('tspan')
      .text((d) => d)
      .attr('x', '0')
      .attr('font-size', '9px')
      // tslint:disable-next-line:no-shadowed-variable
      .attr('y', (d, i, nodes) => `${i * 1.3 - nodes.length / 2 + 0.75}em`)
      .attr('fill', 'black');
  }

  static resizeSvg(): void {
    // Resize Svg to fit ViewBox
    const svgElements = Array.from(document.getElementsByTagName('svg'));

    svgElements.forEach((element) => {
      const bbox = element.getBBox();
      const viewBox = [bbox.x, bbox.y, bbox.width, bbox.height].join(' ');
      element.setAttribute('viewBox', viewBox);
    });
  }
}
