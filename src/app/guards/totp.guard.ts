import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { TotpState } from '../services/user/user.service';

export const totpGuard: CanActivateFn = () => {
  const router = inject(Router);
  const state = router.getCurrentNavigation()?.extras.state;

  if (!state || !state['userLoginIdentifier'] || !state['password'] || !state['totpState']) {
    return false;
  }

  const totpState = state['totpState'];
  if (totpState === TotpState.CODE_REQUIRED) {
    if (!state['totpSecret'] && !state['totpSetupQrCodeUrl']) {
      return true;
    }
  } else if (totpState === TotpState.SETUP_REQUIRED) {
    if (state['totpSecret'] && state['totpSetupQrCodeUrl']) {
      return true;
    }
  }

  return false;
};
