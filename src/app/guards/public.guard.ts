import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { NavController } from '@ionic/angular';
import { AdminRoutes } from '../app-routes';
import { UserService } from '../services/user/user.service';

export const publicGuard: CanActivateFn = () => {
  const userService = inject(UserService);
  const navController = inject(NavController);

  if (userService.isLoggedIn() && userService.isLoggedInUserAnAdmin()) {
    navController.navigateRoot(AdminRoutes.dashboard);
    return false;
  } else {
    return true;
  }
};
