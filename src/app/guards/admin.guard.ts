import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { NavController } from '@ionic/angular';
import { UserService } from '../services/user/user.service';

export const adminGuard: CanActivateFn = () => {
  const userService = inject(UserService);
  const navController = inject(NavController);

  if (userService.isLoggedIn() && userService.isLoggedInUserAnAdmin()) {
    return true;
  } else {
    navController.navigateRoot('/login');
    return false;
  }
};
