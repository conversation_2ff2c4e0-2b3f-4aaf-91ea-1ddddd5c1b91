/* Angular Material START */

// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@use '@angular/material' as mat;
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat.core();

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue. Available color palettes: https://material.io/design/color/
$app-frontend-primary: mat.define-palette(mat.$deep-purple-palette);
$app-frontend-accent: mat.define-palette(mat.$purple-palette);

// The warn palette is optional (defaults to red).
$app-frontend-warn: mat.define-palette(mat.$red-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$app-frontend-theme: mat.define-light-theme(
  (
    color: (
      primary: $app-frontend-primary,
      accent: $app-frontend-accent,
      warn: $app-frontend-warn,
    ),
  )
);

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include mat.all-component-themes($app-frontend-theme);

/* Angular Material END */

@import 'theme/material_overrides';

/* Ionic START */

@import 'theme/variables.scss';

/* Core CSS required for Ionic components to work properly */
@import '~@ionic/angular/css/core.css';

/* Basic CSS for apps built with Ionic */
@import '~@ionic/angular/css/normalize.css';
@import '~@ionic/angular/css/structure.css';
@import '~@ionic/angular/css/typography.css';
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import '~@ionic/angular/css/padding.css';
@import '~@ionic/angular/css/float-elements.css';
@import '~@ionic/angular/css/text-alignment.css';
@import '~@ionic/angular/css/text-transformation.css';
@import '~@ionic/angular/css/flex-utils.css';

@import 'theme/fonts.scss';
@import 'theme/app.scss';

/* Ionic END */

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif;
}
