@import '../../src/theme/mixins';
@import './variables_v2';
@import './app_v2';

@mixin background-gradient($deg) {
  background: linear-gradient($deg, lightgray 0%, white 100%);
}

//disable Scrollbar for all browsers
.disable-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.h-100 {
  height: 100%;
}

.w-100 {
  width: 100%;
}

.p-0 {
  padding: 0;
}

.pos-rel {
  position: relative;
}

html {
  font-family: 'Roboto', serif;
  -webkit-font-smoothing: antialiased;
  font-size: 16px;
}

ion-button {
  --border-radius: 0;
  --box-shadow: none;
}

.plt-ios {
  .ion-page {
    ion-toolbar {
      ion-title {
        padding-left: 6rem;
      }
    }
  }
}

.ion-page {
  ion-toolbar {
    height: 8rem;
    color: white;
    --background: var(--ion-color-primary);
    display: flex;
    align-items: center;

    ion-title {
      font-size: 1.6875rem;
      font-weight: 700;
      padding-left: 2.625rem;
      text-align: left;
      text-transform: uppercase;
    }

    ion-buttons {
      margin-right: 0.5rem;

      & + ion-title {
        padding-left: 2rem;
      }

      ion-button {
        height: 3rem;
        width: 3rem;
        margin-right: 0.5rem;
      }

      ion-menu-button {
        color: white;
        height: 3rem;
        width: 3rem;
      }
    }
  }
}

.safe-margin {
  margin: 0 2rem;
}

.safe-margin-bottom {
  margin-bottom: 2rem;
}

.no-padding-top {
  padding-top: 0;
}

ion-textarea {
  --placeholder-color: var(--ion-color-placeholder);
  border: solid 1px var(--ion-color-lightgrey);
  margin-top: 1rem;

  > textarea {
    --placeholder-color: var(--ion-color-placeholder);
  }
}

.ios ion-textarea {
  padding-left: 0.5rem;
}

ion-input {
  border-bottom: solid 1px var(--ion-color-lightgrey);
  margin-bottom: 1.5rem;

  > input {
    --placeholder-color: var(--ion-color-placeholder);
    --padding-start: 0;
    margin-bottom: -0.3rem;
  }
}

ion-select {
  --placeholder-color: var(--ion-color-placeholder);
  --padding-start: 0;
  padding-bottom: 5px;
}

ion-datetime {
  padding: 0 0 4rem;
  margin: -0.3rem 0 1.5rem 0;
  --placeholder-color: var(--ion-color-datepickergrey);
}

.picker {
  > div {
    margin-top: -1.4rem;
    margin-bottom: 1.6rem;
    width: 100%;
    height: 1px;
    background-color: var(--ion-color-lightgrey);

    &.select {
      margin-top: 0;
    }
  }
}

.divider {
  width: 100%;
  height: 1px;
  background-color: lightgray;
  @include background-gradient(-45deg);
}

ngx-datatable {
  max-width: 80rem;

  datatable-header {
    border-bottom: 2px solid var(--ion-color-lightgrey);

    .datatable-header-inner {
      padding-top: 2.25rem;

      .datatable-header-cell-wrapper {
        cursor: default !important;
      }
    }

    datatable-header-cell {
      font-weight: 500;
    }
  }

  datatable-body-row {
    border-bottom: 1px solid var(--ion-color-lightgrey);
    min-height: 3.5rem;

    > div {
      align-items: center;
    }

    datatable-body-cell {
      cursor: pointer;
      font-weight: 400;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
  }

  .empty-row {
    text-align: center;
    padding-top: 2rem;
  }
}

.evaluation-status-color-completed {
  color: var(--ion-color-status-completed);
}

.evaluation-status-color-created {
  color: var(--ion-color-status-created);
}

ion-alert {
  .alert-button {
    &.alert-button-danger {
      background-color: var(--ion-color-danger);
      color: var(--ion-color-danger-contrast);
    }

    &.alert-button-ok {
      background-color: var(--ion-color-primary);
      color: var(--ion-color-primary-contrast);
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  .alert-message.sc-ion-alert-md {
    color: var(--ion-text-color);
  }
}

.modal-wrapper {
  --max-width: 72rem;
}

.title-bttn {
  padding-right: 1rem;

  ion-button {
    margin-left: 1rem;
  }
}

.export-modal {
  --border-radius: 0;
}

.use-hyphenation {
  @include hyphenation();
}

ion-toast {
  --ion-safe-area-bottom: 2rem;
  --box-shadow: none;
  --border-radius: 4px;
}

ion-searchbar {
  .searchbar-input.sc-ion-searchbar-md {
    padding-inline-start: 20px;
    color: var(--ion-text-color);
  }

  .searchbar-input.sc-ion-searchbar-md {
    padding-inline-start: 20px;
    box-shadow: none;
    border: 1px solid var(--ion-color-lightgrey);
  }

  .searchbar-search-icon.sc-ion-searchbar-md,
  .searchbar-clear-icon.sc-ion-searchbar-md {
    left: unset;
    right: 17px;
    top: 10px;
    width: 25px;
    height: 25px;
    color: var(--ion-color-primary);
  }
}

.form-bttn {
  width: 46%;
  float: left;

  &.right {
    float: right;
  }

  &.full-width {
    width: 100%;
  }

  &.cancel {
    --background: var(--ion-color-medium);
    --background-activated: var(--ion-color-medium);
    --background-focused: var(--ion-color-medium);
    --background-hover: var(--ion-color-medium);
  }

  &.delete {
    --background: var(--ion-color-danger);
    --background-activated: var(--ion-color-danger);
    --background-focused: var(--ion-color-danger);
    --background-hover: var(--ion-color-danger);
  }
}

.login-button {
  height: 3.2rem;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 2rem;
  --box-shadow: none;
  --ion-color-primary: var(--ion-color-login-button);
  --ion-color-primary-shade: var(--ion-color-login-button-shade);
  --ion-color-primary-tint: var(--ion-color-login-button-tint);

  &.button-disabled {
    --opacity: 1;
    --ion-color-primary: var(--ion-color-login-button-disabled);
    --ion-color-primary-shade: var(--ion-color-login-button-disabled);
    --ion-color-primary-tint: var(--ion-color-login-button-disabled);
  }
}

.alert-radio-icon.sc-ion-alert-md {
  border-color: var(--ion-color-primary);
}

.add-user-modal,
.report-language-modal {
  --border-radius: 8px;
  --border: 1px solid $color-light-gray;
  --height: auto;
  --width: auto;
}
