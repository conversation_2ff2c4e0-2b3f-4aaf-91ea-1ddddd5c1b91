@mixin hyphenation() {
  hyphens: auto;
  -webkit-hyphens: auto;
}

@mixin no-hyphenation() {
  hyphens: none;
  -webkit-hyphens: none;
}

@mixin blue-based-primary-color-scheme() {
  --ion-color-primary: #9acbee;
  --ion-color-primary-rgb: 154, 203, 238;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 154, 203, 238;
  --ion-color-primary-shade: #9acbee;
  --ion-color-primary-tint: #9acbee;
}

@mixin colorNavButton($bg, $bgh, $bga, $bgf, $color) {
  #{$bg}: $color;
  #{$bgh}: $color;
  #{$bga}: $color;
  #{$bgf}: $color;
}
