@import '../../src/theme/mixins';
@import './variables_v2';

@mixin background-gradient($deg) {
  background: linear-gradient($deg, lightgray 0%, white 100%);
}

.design-v2 {
  --ion-text-color: var(--ion-color-primary-V2);

  .loading-wrapper {
    border-radius: var(--border-radius-V2);
  }

  .alert-wrapper {
    border-radius: var(--border-radius-V2);
    max-width: 50%;
    min-width: 450px;
    padding-inline-start: 8px;
    padding-inline-end: 8px;

    .alert-head {
      padding-top: 0;
      margin-top: 40px;
      text-align: left;
    }

    .alert-button-group {
      margin-bottom: 32px;
      padding-bottom: 0;
      display: inline-flex;
      justify-content: flex-end;
      flex-wrap: nowrap;
      padding-right: 24px;
      padding-left: 24px;
    }

    .alert-title {
      line-height: 1.5rem;
      font-weight: 900;
      font-size: 24px;
    }

    .alert-message {
      font-size: 16px;
      text-align: left;
      padding-bottom: 50px;
    }

    .alert-button {
      min-width: auto;
      margin: 0;
      flex: none;
      padding-left: 16px;
      padding-right: 16px;

      .alert-button-inner {
        justify-content: center;
      }

      color: var(--ion-color-primary-V2);
      border-radius: var(--button-border-radius);
      border: 1px solid $color-light-gray;
      font-weight: 800;

      &.danger {
        background-color: var(--ion-color-danger);
        color: var(--ion-color-danger-contrast);
        text-transform: uppercase;
      }

      &.ok {
        border: none;
        background: var(--ion-color-button-gradient);
        color: var(--ion-color-primary-V2);
        padding-left: 2rem;
        padding-right: 2rem;
        text-transform: uppercase;
      }

      &.none {
        margin-right: 16px;
        text-transform: uppercase;
      }
    }
  }

  * {
    font-family: 'Open Sans', serif;
  }

  h1 {
    color: $color-primary-v2;
    font-size: 24px;
    line-height: 33px;
    font-weight: 800;
  }

  h2 {
    color: $color-primary-v2;
    font-size: 40px;
    line-height: 52px;
    font-weight: 800;
    margin-top: 6px;
    margin-bottom: 6px;
  }

  ion-spinner {
    --spinner-color: var(--ion-color-primary-V2);
    --spinner: 'bubbles';
  }

  ion-toggle {
    --background: var(--ion-color-placeholder-V2);
    --background-checked: var(--ion-color-placeholder-V2);

    --handle-background: var(--ion-color-primary-V2);
    --handle-background-checked: var(--ion-color-primary-V2);
  }

  ion-checkbox {
    margin: 0 !important;
    --background: var(--ion-color-primary-V2);
    --background-checked: var(--ion-color-primary-V2);
    --border-color: white;
    --border-color-checked: white;
    --border-width: 1px;
  }
}

.ion-page {
  ion-header {
    &.header-v2 {
      &.header-md {
        box-shadow: none;
      }
    }
  }

  ion-toolbar {
    height: 6rem;
    color: white;
    --background: var(--ion-color-primary);
    display: flex;
    align-items: center;

    &.toolbar-v2 {
      --background: white;
      color: var(--ion-color-primary-V2);
      --border-width: 0 0 0 0;
      --border-style: none;
      padding-left: 94px;
      padding-right: 94px;
      padding-top: 60px;

      ion-title {
        color: var(--ion-color-primary-V2);
        font-size: 1.5rem;
        font-weight: 900;
        padding-left: 0;
        text-align: left;
        text-transform: none;
      }
    }
  }
}

.alert-v2 {
  border-radius: var(--border-radius-V2);
}

#surveyElement .sv-footer__next-btn {
  height: 56px;
  color: black;
  border-radius: 10px;
  font-size: 16px;
  text-transform: uppercase;
  background: linear-gradient(
    to top right,
    var(--ion-color-gradient-lila-V2),
    var(--ion-color-gradient-blue-V2),
    var(--ion-color-gradient-yellow-V2)
  );
  font-weight: 800;
}

#surveyElement .sv-footer__complete-btn {
  height: 56px;
  color: black;
  border-radius: 10px;
  font-size: 16px;
  text-transform: uppercase;
  background: linear-gradient(
    to top right,
    var(--ion-color-gradient-lila-V2),
    var(--ion-color-gradient-blue-V2),
    var(--ion-color-gradient-yellow-V2)
  );
  font-weight: 800;
}

#surveyElement {
  .sv-footer__prev-btn {
    height: 56px;
    color: black;
    border-radius: 10px;
    border: 1px solid $color-light-gray;
    font-size: 16px;
    text-transform: uppercase;
    background: white;
    font-weight: 800;
  }

  .sv-title {
    font-size: 16px;
    font-weight: bold;
    color: $color-primary-v2;
  }

  .sv-comment {
    font-size: 16px;
    font-weight: normal;
    color: $color-primary-v2;
  }

  .sv-selectbase__label {
    font-size: 16px;
    font-weight: normal;
    color: $color-primary-v2;
  }

  .sv-question {
    font-size: 16px;
    font-weight: bold;
    color: $color-primary-v2;
  }

  .sv-progress__text {
    font-weight: 500;
    margin-left: 0;
  }

  .sv-progress {
    margin-left: calc(5% + 0.55em);
    margin-right: calc(5% + 0.55em);
    height: 4px;
    border-radius: 12px;
    background-color: #e7e5e9;
  }

  .sv-progress__bar {
    border-radius: 12px;
  }

  .sv-radio__svg {
    border-color: #e7e5e9;
  }

  .sv-checkbox__svg {
    border-color: #e7e5e9;
  }

  .sv-radio--checked .sv-radio__svg {
    border-color: black;
  }
}

.eval-to-group-association-modal .modal-wrapper {
  --border-radius: var(--border-radius-V2);
  height: 480px;
  width: 80vw;
  max-width: 700px;
}

.activate-license-modal .modal-wrapper {
  --border-radius: var(--border-radius-V2);
  height: 400px;
  width: 50vw;
  max-width: 700px;
}

ion-modal .ion-page {
  // Safari
  border-radius: inherit;
}
