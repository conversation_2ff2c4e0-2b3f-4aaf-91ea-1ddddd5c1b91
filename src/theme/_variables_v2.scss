$color-primary-v2: #160325;
$color-danger: #f76c6c;
$color-light-gray: #e7e5e9;
$color-dark-gray: #73677c;
$color-gradient-lila: #ccbffa;
$color-gradient-blue: #bbf8f9;
$color-gradient-yellow: #f3f875;
$color-hint-text-grey: #b9b3bd;
$color-info-box-background: #f8f7f9;
$color-empty-text: #b9b3bd;
$color-highlight: #aab3f4;

:root {
  --ion-color-primary-V2: #160325;
  --ion-color-primary-contrast-V2: #ffffff;
  --ion-color-placeholder-V2: #8f8696;
  --ion-color-light-grey-V2: #e7e5e9;
  --ion-color-dark-grey-V2: #b9b3bd;
  --ion-color-gradient-lila-V2: #ccbffa;
  --ion-color-gradient-blue-V2: #bbf8f9;
  --ion-color-gradient-yellow-V2: #f3f875;
  --ion-color-danger-V2: #f76c6c;
  --ion-color-darker-grey-V2: #73677c;

  // survey
  --ion-color-hover: #919df0;

  // survey entries in admin-dashboard tree-view
  --ion-color-organization-survey: #eaecff;
  --ion-color-organization-survey-bright: #eaecff99; //base-color with alpha .6 = #99
  --ion-color-organization-survey-brighter: #eaecff66; //base-color with alpha .4 = #66
  --ion-background-organization-survey-open-state: repeating-linear-gradient(
    -45deg,
    var(--ion-color-organization-survey-bright),
    var(--ion-color-organization-survey-bright) 5px,
    var(--ion-color-organization-survey-brighter) 5px,
    var(--ion-color-organization-survey-brighter) 10px
  );
  --ion-color-administration-survey: #dafaed;
  --ion-color-administration-survey-bright: #dafaed99; //base-color with alpha .6 = #99
  --ion-color-administration-survey-brighter: #dafaed66; //base-color with alpha .4 = #66
  --ion-background-administration-survey-open-state: repeating-linear-gradient(
    -45deg,
    var(--ion-color-administration-survey-bright),
    var(--ion-color-administration-survey-bright) 5px,
    var(--ion-color-administration-survey-brighter) 5px,
    var(--ion-color-administration-survey-brighter) 10px
  );
  --ion-color-parental-survey: #fdebf6;
  --ion-color-parental-survey-bright: #fdebf699; //base-color with alpha .6 = #99
  --ion-color-parental-survey-brighter: #fdebf666; //base-color with alpha .4 = #66
  --ion-background-parental-survey-open-state: repeating-linear-gradient(
    -45deg,
    var(--ion-color-parental-survey-bright),
    var(--ion-color-parental-survey-bright) 5px,
    var(--ion-color-parental-survey-brighter) 5px,
    var(--ion-color-parental-survey-brighter) 10px
  );
  --ion-color-staff-survey: #eafcd2;
  --ion-color-staff-survey-bright: #eafcd299; //base-color with alpha .6 = #99
  --ion-color-staff-survey-brighter: #eafcd266; //base-color with alpha .4 = #66
  --ion-background-staff-survey-open-state: repeating-linear-gradient(
    -45deg,
    var(--ion-color-staff-survey-bright),
    var(--ion-color-staff-survey-bright) 5px,
    var(--ion-color-staff-survey-brighter) 5px,
    var(--ion-color-staff-survey-brighter) 10px
  );
  --ion-color-child-evaluation: #ffefb9;
  --ion-color-child-evaluation-bright: #ffefb999; //base-color with alpha .6 = #99
  --ion-color-child-evaluation-brighter: #ffefb966; //base-color with alpha .4 = #66
  --ion-background-child-evaluation-open-state: repeating-linear-gradient(
    -45deg,
    var(--ion-color-child-evaluation-bright),
    var(--ion-color-child-evaluation-bright) 5px,
    var(--ion-color-child-evaluation-brighter) 5px,
    var(--ion-color-child-evaluation-brighter) 10px
  );
  --ion-color-grazias-evaluation: #dcfbfe; // Fremdevaluation
  --ion-color-grazias-evaluation-bright: #dcfbfe99; //base-color with alpha .6 = #99
  --ion-color-grazias-evaluation-brighter: #dcfbfe66; //base-color with alpha .4 = #66
  --ion-background-grazias-evaluation-open-state: repeating-linear-gradient(
    -45deg,
    var(--ion-color-grazias-evaluation-bright),
    var(--ion-color-grazias-evaluation-bright) 5px,
    var(--ion-color-grazias-evaluation-brighter) 5px,
    var(--ion-color-grazias-evaluation-brighter) 10px
  );

  /** success **/
  --ion-color-success: #7dde87;

  --border-radius-V2: 1rem;

  --ion-color-button-gradient: linear-gradient(
    to top right,
    var(--ion-color-gradient-lila-V2),
    var(--ion-color-gradient-blue-V2),
    var(--ion-color-gradient-yellow-V2)
  );
  --button-border-radius: 8px;
  --card-component-border-radius: 10px;
}
