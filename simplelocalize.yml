# Common
apiKey: aF8ee9D22B550b2d7Bc86de4e8A9bC8406BB0E1EF6C6C585

# Business plan option
# customerId: grazias


#################################
# 'simplelocalize upload' command
#################################

# Upload path is a path to a file(s) with translations. Use {lang} placeholder to specify language or locale and {ns} placeholder to specify namespace.
# For example, if you have translations in 2 languages and 2 namespaces, you can use the following path: ./src/translations/{lang}/{ns}.json.
# Add '--dryRun' parameter to check what translation files will be uploaded without actually uploading them.
#uploadPath: ./src/assets/i18n/{lang}.json

# Upload format is a format of the file(s) with translations.
# Supported formats: https://simplelocalize.io/docs/general/file-formats/
#uploadFormat: single-language-json

# Upload options are options that are passed to the upload command.
# Supported options: https://simplelocalize.io/docs/general/options/
uploadOptions:
#  - 'REPLACE_TRANSLATION_IF_FOUND' # overwrite translation for given a key and namespace if found


###################################
# 'simplelocalize download' command
###################################

# Download path is a path to a file(s) with translations. Use {lang} placeholder to specify language or locale and {ns} placeholder to specify namespace.
# For example, if you have translations in 2 languages and 2 namespaces, you can use the following path: ./src/translations/{lang}/{ns}.json
downloadPath: ./src/assets/i18n/{lang}.json

# Download format is a format of the file(s) with translations.
# Supported formats: https://simplelocalize.io/docs/general/file-formats/
downloadFormat: single-language-json

# Download options are options that are passed to the download command.
# Supported options: https://simplelocalize.io/docs/general/options/
downloadOptions:
  - 'WRITE_NESTED' # write nested JSON
  - 'ONLY_TRANSLATED' # download only translated keys


#########################################
# 'simplelocalize auto-translate' command
#########################################

# autoTranslation.languageKeys is a list of languages that should be auto-translated.
# Leave empty to auto-translate all project languages.
#autoTranslation:
#  languageKeys:
#    - 'en'
#    - 'de'

##################################
# 'simplelocalize extract' command
##################################

# Extract path is a path to a directory with application source code.
#searchDir: ./lib

# Project type is a type of the project. It is used to extract i18n keys from the source code.
# Supported project types: https://simplelocalize.io/docs/cli/i18n-keys-extraction/
#projectType: yahoo/react-intl

# Ignore keys are a list of keys that should be ignored during i18n keys extraction.
#ignoreKeys:
#  - 'WELCOME'
#  - 'ABOUT-US'
