#!/bin/bash

set -eo pipefail

_set_app_base_url() {
    array=("./angular.json" "src/environments/environment.local.ts")
    for element in "${array[@]}"
    do
      sed -i '' "s/NNN.developers.bytepoets.net/$1/g" "$element"
    done
}

main() {
        # set backend config for local development
        if [ -n "$1" ]
        then
            _set_app_base_url $1
        else
            echo
            echo 'usage: `./prepare-startup.sh NNN.developers.bytepoets.net`'
            echo
        fi
}

main "$@"
