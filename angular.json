{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["src/global.scss"], "scripts": [], "allowedCommonJsDependencies": ["chart.js", "survey-angular", "chartjs-plugin-datalabels", "moment", "qrcode"], "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "outputHashing": "all"}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "outputHashing": "all"}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "test"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"host": "127.0.0.1", "port": 3000, "publicHost": "https://NNN.developers.bytepoets.net:12800", "buildTarget": "app:build:local"}, "configurations": {"local": {"buildTarget": "app:build:local"}}, "defaultConfiguration": "local"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics", "@ionic/angular-toolkit"], "analytics": false}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}