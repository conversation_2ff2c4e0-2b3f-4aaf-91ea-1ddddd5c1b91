# GrazIAS Ionic - GrazIAS Admin Frontend

## Prerequisites

* Node 14 LTS

## Development

After a fresh clone use following commands to set up your workspace properly:

Setup URLs with `./prepare-startup.sh NNN.developers.bytepoets.net` using your moniker.

Navigate to `https://<yourMoniker>.developers.bytepoets.net:12800/` after starting to serve locally. The application will automatically reload if you change any of the source files.

```
npm install
npm start
```

#### Generate build 

From the workspace directory: `npm run build-<environment>`

#### Generate surveys from templates

```npm run generate-surveys```

### Translations
Lokal *SimpleLocalize CLI* installieren (siehe [Online-Doku Get started | SimpleLocalize](https://simplelocalize.io/docs/cli/get-started/))

Translations mit `simplelocalize download` herunterladen
